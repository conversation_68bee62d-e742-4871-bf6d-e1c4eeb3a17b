2025-07-15 18:32:20.122 [34mINFO [0;39m 3364 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-15 18:32:20.124 [34mINFO [0;39m 3364 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 3364 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-15 18:32:20.125 [39mDEBUG[0;39m 3364 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-15 18:32:20.126 [34mINFO [0;39m 3364 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-15 18:32:21.259 [34mINFO [0;39m 3364 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-15 18:32:21.260 [34mINFO [0;39m 3364 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-15 18:32:21.297 [34mINFO [0;39m 3364 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-15 18:32:21.433 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-15 18:32:21.434 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-15 18:32:21.434 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-15 18:32:21.434 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-15 18:32:21.435 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-15 18:32:21.437 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-15 18:32:21.437 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-15 18:32:21.438 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-15 18:32:21.438 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-15 18:32:21.438 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-15 18:32:21.438 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-15 18:32:21.439 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-15 18:32:21.439 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-15 18:32:21.439 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-15 18:32:21.439 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-15 18:32:21.439 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-15 18:32:21.439 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-15 18:32:21.440 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-15 18:32:21.440 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-15 18:32:21.440 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-15 18:32:21.440 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-15 18:32:21.441 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-15 18:32:21.441 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-15 18:32:21.441 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-15 18:32:21.441 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-15 18:32:21.442 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-15 18:32:21.442 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-15 18:32:21.443 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-15 18:32:21.443 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-15 18:32:21.443 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-15 18:32:21.443 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-15 18:32:21.444 [39mDEBUG[0;39m 3364 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-15 18:32:22.154 [34mINFO [0;39m 3364 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-15 18:32:22.161 [34mINFO [0;39m 3364 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-15 18:32:22.162 [34mINFO [0;39m 3364 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-15 18:32:22.162 [34mINFO [0;39m 3364 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-15 18:32:22.277 [34mINFO [0;39m 3364 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-15 18:32:22.278 [34mINFO [0;39m 3364 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2109 ms
2025-07-15 18:32:22.535 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-15 18:32:22.546 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-15 18:32:22.552 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-15 18:32:22.559 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-15 18:32:22.572 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-15 18:32:22.576 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-15 18:32:22.583 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-15 18:32:22.588 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-15 18:32:22.595 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-15 18:32:22.602 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-15 18:32:22.614 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-15 18:32:22.621 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-15 18:32:22.626 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-15 18:32:22.633 [39mDEBUG[0;39m 3364 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-15 18:32:22.645 [34mINFO [0;39m 3364 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-15 18:32:23.703 [1;31mERROR[0;39m 3364 --- [main] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:332)
	at org.springframework.boot.jdbc.EmbeddedDatabaseConnection.isEmbedded(EmbeddedDatabaseConnection.java:164)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.isEmbeddedDatabase(DataSourceScriptDatabaseInitializer.java:70)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.isEnabled(AbstractScriptDatabaseInitializer.java:83)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:106)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.pure.PureApplication.main(PureApplication.java:37)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 132 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 135 common frames omitted
2025-07-15 18:32:24.561 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-15 18:32:24.561 [39mDEBUG[0;39m 3364 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-15 18:32:25.047 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-15 18:32:25.050 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:25.715 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-15 18:32:25.932 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-15 18:32:25.940 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-15 18:32:26.005 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-15 18:32:26.005 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.006 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-15 18:32:26.006 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.006 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-15 18:32:26.007 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.007 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-15 18:32:26.008 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.008 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-15 18:32:26.009 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.009 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-15 18:32:26.009 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.013 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-15 18:32:26.013 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.013 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-15 18:32:26.014 [39mDEBUG[0;39m 3364 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-15 18:32:26.127 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-15 18:32:26.129 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-15 18:32:26.135 [34mINFO [0;39m 3364 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@6e70861, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5fcff272, org.springframework.security.web.context.SecurityContextPersistenceFilter@29811d4d, org.springframework.security.web.header.HeaderWriterFilter@46067a74, org.springframework.security.web.authentication.logout.LogoutFilter@6c0bf8f4, com.example.pure.filter.JwtFilter@e7b3e54, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@56fc2cea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e30a8ef, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5b5d6f9c, org.springframework.security.web.session.SessionManagementFilter@687d31a9, org.springframework.security.web.access.ExceptionTranslationFilter@155767a7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@78723798]
2025-07-15 18:32:26.138 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-15 18:32:26.141 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-15 18:32:26.141 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-15 18:32:26.142 [34mINFO [0;39m 3364 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-15 18:32:26.323 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-15 18:32:26.348 [34mINFO [0;39m 3364 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-15 18:32:26.421 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-15 18:32:26.429 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-15 18:32:27.077 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-15 18:32:27.349 [34mINFO [0;39m 3364 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-15 18:32:27.398 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-15 18:32:27.399 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-15 18:32:27.400 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-15 18:32:27.401 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-15 18:32:27.401 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-15 18:32:27.401 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-15 18:32:27.401 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-15 18:32:27.401 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-15 18:32:27.401 [39mDEBUG[0;39m 3364 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-15 18:32:27.402 [34mINFO [0;39m 3364 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@37aec9b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34538ffe, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ae91bcc, org.springframework.security.web.header.HeaderWriterFilter@258291de, org.springframework.web.filter.CorsFilter@7ae75ba6, org.springframework.security.web.authentication.logout.LogoutFilter@77d42ed7, com.example.pure.filter.JwtFilter@e7b3e54, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@679886ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5625e7e1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2bab1821, org.springframework.security.web.session.SessionManagementFilter@4f63909f, org.springframework.security.web.access.ExceptionTranslationFilter@1a18e68a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7915111a]
2025-07-15 18:32:27.466 [39mTRACE[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1c05a54d, started on Tue Jul 15 18:32:20 CST 2025
2025-07-15 18:32:27.487 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-15 18:32:27.487 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-15 18:32:27.488 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-15 18:32:27.489 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-15 18:32:27.493 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-15 18:32:27.495 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-15 18:32:27.496 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-15 18:32:27.496 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-15 18:32:27.496 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-15 18:32:27.635 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-15 18:32:27.673 [39mDEBUG[0;39m 3364 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-15 18:32:28.023 [34mINFO [0;39m 3364 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-15 18:32:28.038 [34mINFO [0;39m 3364 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-15 18:32:28.041 [39mDEBUG[0;39m 3364 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-15 18:32:28.041 [39mDEBUG[0;39m 3364 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-15 18:32:28.041 [34mINFO [0;39m 3364 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-15 18:32:28.041 [39mDEBUG[0;39m 3364 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ab0168]
2025-07-15 18:32:28.041 [39mDEBUG[0;39m 3364 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ab0168]
2025-07-15 18:32:28.042 [34mINFO [0;39m 3364 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ab0168]]
2025-07-15 18:32:28.042 [34mINFO [0;39m 3364 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-15 18:32:28.042 [39mDEBUG[0;39m 3364 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-15 18:32:28.042 [39mDEBUG[0;39m 3364 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-15 18:32:28.062 [34mINFO [0;39m 3364 --- [main] com.example.pure.PureApplication : Started PureApplication in 8.379 seconds (JVM running for 9.679)
2025-07-15 18:33:09.831 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 18:33:09.832 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-15 18:33:09.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-15 18:33:09.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-15 18:33:09.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-15 18:33:09.835 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@3a15eb9e
2025-07-15 18:33:09.835 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@3d222337
2025-07-15 18:33:09.836 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-15 18:33:09.836 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-07-15 18:33:09.852 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-07-15 18:33:09.852 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-07-15 18:33:09.855 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:09.855 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:09.867 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:33:09.867 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:33:09.937 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-1] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-07-13T09:22:58Z. Current time: 2025-07-15T10:33:09Z, a difference of 177011935 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-15 18:33:09.937 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-2] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-07-13T09:22:58Z. Current time: 2025-07-15T10:33:09Z, a difference of 177011935 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-15 18:33:09.937 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/messages/users/unread-count
2025-07-15 18:33:09.937 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/messages/unread
2025-07-15 18:33:09.980 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:09.980 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:22.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-07-15 18:33:22.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-07-15 18:33:22.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:22.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:22.726 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.video.PureVideoUrlController#getVideoAllType()
2025-07-15 18:33:22.726 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.video.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-07-15 18:33:22.728 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-4] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-07-13T09:22:58Z. Current time: 2025-07-15T10:33:22Z, a difference of 177024728 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-15 18:33:22.728 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-3] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-07-13T09:22:58Z. Current time: 2025-07-15T10:33:22Z, a difference of 177024728 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-15 18:33:22.728 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/videoUrl/videoinfo/pagination
2025-07-15 18:33:22.728 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/videoUrl/type
2025-07-15 18:33:22.729 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:22.729 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:27.437 [34mINFO [0;39m 3364 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 18:33:31.722 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/videoinfo/pagination?keyword=&pageNum=1&pageSize=12
2025-07-15 18:33:31.722 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/videoUrl/type
2025-07-15 18:33:31.722 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:31.722 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:31.722 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.video.PureVideoUrlController#getVideoAllType()
2025-07-15 18:33:31.722 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.video.PureVideoUrlController#getVideoInfoWithPagination(PageRequestDTO)
2025-07-15 18:33:31.724 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-5] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-07-13T09:22:58Z. Current time: 2025-07-15T10:33:31Z, a difference of 177033724 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-15 18:33:31.724 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-6] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-07-13T09:22:58Z. Current time: 2025-07-15T10:33:31Z, a difference of 177033724 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-15 18:33:31.725 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/videoUrl/type
2025-07-15 18:33:31.725 [31mWARN [0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/videoUrl/videoinfo/pagination
2025-07-15 18:33:31.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:31.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:46.879 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/github
2025-07-15 18:33:46.879 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:46.881 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:33:46.884 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:33:46.901 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/github] with attributes [permitAll]
2025-07-15 18:33:46.901 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/github
2025-07-15 18:33:46.903 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/github", parameters={}
2025-07-15 18:33:46.906 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:33:46.940 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-8] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://github.com/login/oauth/authorize?response_type=code&client_id=Ov23liowOuqP1FEcOBXl&redirect_uri=http://localhost:8080/oauth/callback/github&state=ec610ac18d8b1186955da3970c56ddf4&scope=
2025-07-15 18:33:46.940 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:33:46.941 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:33:47.999 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/github?code=2ce2da8b6e42ecfd83cd&state=ec610ac18d8b1186955da3970c56ddf4
2025-07-15 18:33:47.999 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:33:48.000 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:33:48.000 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:33:48.000 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/github?code=2ce2da8b6e42ecfd83cd&state=ec610ac18d8b1186955da3970c56ddf4] with attributes [permitAll]
2025-07-15 18:33:48.001 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/github?code=2ce2da8b6e42ecfd83cd&state=ec610ac18d8b1186955da3970c56ddf4
2025-07-15 18:33:48.001 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/github?code=2ce2da8b6e42ecfd83cd&state=ec610ac18d8b1186955da3970c56ddf4", parameters={masked}
2025-07-15 18:33:48.002 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:33:48.010 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-7] c.e.pure.controller.OAuth2Controller : 收到github的回调: me.zhyd.oauth.model.AuthCallback@73d0303e
2025-07-15 18:33:50.196 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-7] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@30ccd365
2025-07-15 18:33:50.200 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-7] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-15 18:33:51.230 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-7] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:33:51.232 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-7] c.e.pure.controller.OAuth2Controller : 登录意外失败
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:313)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	... 113 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:33:51.233 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:33:51.233 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:34:09.810 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/github
2025-07-15 18:34:09.810 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:34:09.811 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:34:09.811 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:34:09.811 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/github] with attributes [permitAll]
2025-07-15 18:34:09.811 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/github
2025-07-15 18:34:09.812 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/github", parameters={}
2025-07-15 18:34:09.812 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:34:09.813 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-1] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://github.com/login/oauth/authorize?response_type=code&client_id=Ov23liowOuqP1FEcOBXl&redirect_uri=http://localhost:8080/oauth/callback/github&state=ad39e5ddd50b4267a0fd832dc83764d5&scope=
2025-07-15 18:34:09.813 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:34:09.813 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:34:10.975 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/github?code=a31424c45851f554166f&state=ad39e5ddd50b4267a0fd832dc83764d5
2025-07-15 18:34:10.975 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:34:10.976 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:34:10.976 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:34:10.976 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/github?code=a31424c45851f554166f&state=ad39e5ddd50b4267a0fd832dc83764d5] with attributes [permitAll]
2025-07-15 18:34:10.976 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/github?code=a31424c45851f554166f&state=ad39e5ddd50b4267a0fd832dc83764d5
2025-07-15 18:34:10.976 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/github?code=a31424c45851f554166f&state=ad39e5ddd50b4267a0fd832dc83764d5", parameters={masked}
2025-07-15 18:34:10.977 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:34:10.977 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.pure.controller.OAuth2Controller : 收到github的回调: me.zhyd.oauth.model.AuthCallback@77f209f
2025-07-15 18:34:13.315 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@35330d7b
2025-07-15 18:34:13.316 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-15 18:34:14.341 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-2] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:34:14.342 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-2] c.e.pure.controller.OAuth2Controller : 登录意外失败
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:313)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	... 113 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:34:14.343 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:34:14.344 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:34:26.793 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/github
2025-07-15 18:34:26.793 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:34:26.794 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:34:26.794 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:34:26.795 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/github] with attributes [permitAll]
2025-07-15 18:34:26.795 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/github
2025-07-15 18:34:26.795 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/github", parameters={}
2025-07-15 18:34:26.796 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:34:26.796 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-3] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://github.com/login/oauth/authorize?response_type=code&client_id=Ov23liowOuqP1FEcOBXl&redirect_uri=http://localhost:8080/oauth/callback/github&state=ce62ca5d92fed6f480cff91b49e6ec0e&scope=
2025-07-15 18:34:26.796 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:34:26.796 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:34:27.403 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/github?code=d966125102bfe68b3dcb&state=ce62ca5d92fed6f480cff91b49e6ec0e
2025-07-15 18:34:27.403 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:34:27.403 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:34:27.404 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:34:27.404 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/github?code=d966125102bfe68b3dcb&state=ce62ca5d92fed6f480cff91b49e6ec0e] with attributes [permitAll]
2025-07-15 18:34:27.404 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/github?code=d966125102bfe68b3dcb&state=ce62ca5d92fed6f480cff91b49e6ec0e
2025-07-15 18:34:27.404 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/github?code=d966125102bfe68b3dcb&state=ce62ca5d92fed6f480cff91b49e6ec0e", parameters={masked}
2025-07-15 18:34:27.404 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:34:27.405 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.pure.controller.OAuth2Controller : 收到github的回调: me.zhyd.oauth.model.AuthCallback@1303a3e5
2025-07-15 18:34:29.253 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@22391522
2025-07-15 18:34:29.253 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-15 18:34:30.257 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-4] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:34:30.259 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-4] c.e.pure.controller.OAuth2Controller : 登录意外失败
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:313)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	... 113 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:34:30.259 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:34:30.259 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:34:53.258 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/gitee
2025-07-15 18:34:53.258 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:34:53.259 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:34:53.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:34:53.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/gitee] with attributes [permitAll]
2025-07-15 18:34:53.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/gitee
2025-07-15 18:34:53.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/gitee", parameters={}
2025-07-15 18:34:53.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:34:53.265 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-5] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://gitee.com/oauth/authorize?response_type=code&client_id=f669b4c8f06bc432ff0e0e8925b65eeee7486c9105b3101c956f327bbcf5957c&redirect_uri=http://localhost:8080/oauth/callback/gitee&state=6bbb66fdc92835535675e969d76aca91&scope=user_info
2025-07-15 18:34:53.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:34:53.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:35:32.255 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/gitee?code=c8bd8faf4c5f983ddf9b3389b6b5d82b677a2126db14e8ad7b2fc904afc07004&state=6bbb66fdc92835535675e969d76aca91
2025-07-15 18:35:32.255 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:35:32.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:35:32.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:35:32.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/gitee?code=c8bd8faf4c5f983ddf9b3389b6b5d82b677a2126db14e8ad7b2fc904afc07004&state=6bbb66fdc92835535675e969d76aca91] with attributes [permitAll]
2025-07-15 18:35:32.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/gitee?code=c8bd8faf4c5f983ddf9b3389b6b5d82b677a2126db14e8ad7b2fc904afc07004&state=6bbb66fdc92835535675e969d76aca91
2025-07-15 18:35:32.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/gitee?code=c8bd8faf4c5f983ddf9b3389b6b5d82b677a2126db14e8ad7b2fc904afc07004&state=6bbb66fdc92835535675e969d76aca91", parameters={masked}
2025-07-15 18:35:32.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:35:32.258 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.pure.controller.OAuth2Controller : 收到gitee的回调: me.zhyd.oauth.model.AuthCallback@62ca5941
2025-07-15 18:35:32.789 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@2d30b153
2025-07-15 18:35:32.789 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-15 18:35:33.797 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-9] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:35:33.798 [1;31mERROR[0;39m 3364 --- [http-nio-8080-exec-9] c.e.pure.controller.OAuth2Controller : 登录意外失败
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:313)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.auth.impl.OAuth2ServiceImpl$$EnhancerBySpringCGLIB$$ee0a9a98.handleOAuth2User(<generated>)
	at com.example.pure.controller.auth.OAuth2Controller.login(OAuth2Controller.java:144)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:269)
	... 113 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 124 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 127 common frames omitted
2025-07-15 18:35:33.799 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:35:33.799 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:17.529 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /oauth/render/github
2025-07-15 18:36:17.529 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:17.530 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:36:17.530 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:36:17.530 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/render/github] with attributes [permitAll]
2025-07-15 18:36:17.530 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /oauth/render/github
2025-07-15 18:36:17.531 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/oauth/render/github", parameters={}
2025-07-15 18:36:17.531 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#renderAuth(String, HttpServletResponse)
2025-07-15 18:36:17.532 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-1] c.e.pure.controller.OAuth2Controller : Generated authorize url: https://github.com/login/oauth/authorize?response_type=code&client_id=Ov23liowOuqP1FEcOBXl&redirect_uri=http://localhost:8080/oauth/callback/github&state=f33e6707fbf378564a80f2d5017ca21c&scope=
2025-07-15 18:36:17.532 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:36:17.532 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:18.474 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /oauth/callback/github?code=484622d7534fe9c2a354&state=f33e6707fbf378564a80f2d5017ca21c
2025-07-15 18:36:18.474 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:18.475 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:36:18.475 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 18:36:18.475 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /oauth/callback/github?code=484622d7534fe9c2a354&state=f33e6707fbf378564a80f2d5017ca21c] with attributes [permitAll]
2025-07-15 18:36:18.475 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /oauth/callback/github?code=484622d7534fe9c2a354&state=f33e6707fbf378564a80f2d5017ca21c
2025-07-15 18:36:18.475 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/oauth/callback/github?code=484622d7534fe9c2a354&state=f33e6707fbf378564a80f2d5017ca21c", parameters={masked}
2025-07-15 18:36:18.475 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.OAuth2Controller#login(String, AuthCallback, HttpServletResponse)
2025-07-15 18:36:18.476 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.pure.controller.OAuth2Controller : 收到github的回调: me.zhyd.oauth.model.AuthCallback@1166cad0
2025-07-15 18:36:20.371 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.pure.controller.OAuth2Controller : 用户信息: me.zhyd.oauth.model.AuthUser@84e61b3
2025-07-15 18:36:20.371 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-15 18:36:20.514 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-15 18:36:20.931 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.OAuth2ServiceImpl : Processing OAuth2 user: Haohao268826
2025-07-15 18:36:20.952 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:20.956 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:20.965 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@522418940 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:20.969 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-07-15 18:36:20.996 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:36:21.023 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-07-15 18:36:21.026 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.028 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0] from current transaction
2025-07-15 18:36:21.028 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.O.findOAuth2InfoByUserIdAndProvider : ==>  Preparing: SELECT * FROM o_auth2 WHERE user_id = ? AND provider = ?
2025-07-15 18:36:21.029 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.O.findOAuth2InfoByUserIdAndProvider : ==> Parameters: 23(Long), GITHUB(String)
2025-07-15 18:36:21.033 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.O.findOAuth2InfoByUserIdAndProvider : <==      Total: 1
2025-07-15 18:36:21.033 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.033 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0] from current transaction
2025-07-15 18:36:21.033 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.O.updateOAuth2Info : ==>  Preparing: UPDATE o_auth2 SET access_token = ?, access_token_expire_in = ?, refresh_token = ?, refresh_token_expire_in = ?, updated_time = NOW() WHERE user_id = ? AND provider = ?
2025-07-15 18:36:21.034 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.O.updateOAuth2Info : ==> Parameters: ****************************************(String), 0(Integer), null, 0(Integer), 23(Long), GITHUB(String)
2025-07-15 18:36:21.038 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.O.updateOAuth2Info : <==    Updates: 1
2025-07-15 18:36:21.038 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.041 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0] from current transaction
2025-07-15 18:36:21.041 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findUserWithPasswordByUserId : ==>  Preparing: SELECT * FROM user WHERE id = ?
2025-07-15 18:36:21.041 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findUserWithPasswordByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:21.044 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findUserWithPasswordByUserId : <==      Total: 1
2025-07-15 18:36:21.044 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.255 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 78607f5b-f2f1-4026-ab15-a93b5388ef27
2025-07-15 18:36:21.274 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - 48ca674d-73fd-4685-8f78-823403bc0def
2025-07-15 18:36:21.275 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.OAuth2ServiceImpl : 用户 Haohao268826 的 Access Token 设备数超限，已移除最早的设备: $2a$10$YaVF0oYHzine2wjNIXm.lunDP7qHnSaTneXoqIZ6DcAd5TAw9lvGi
2025-07-15 18:36:21.282 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:refreshToken:Haohao268826, DeviceId - 824e0e63-4972-4746-88c9-c33c2cb667a5
2025-07-15 18:36:21.287 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - 48ca674d-73fd-4685-8f78-823403bc0def
2025-07-15 18:36:21.287 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.OAuth2ServiceImpl : 用户 Haohao268826 的 Refresh Token 设备数超限，已移除最早的设备: $2a$10$B/uJcfQ9BZz2YB/2hs1/o.wXn/Hjyrk/UWNrUcR2l4MBiVt27Fy3a
2025-07-15 18:36:21.332 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-07-15 18:36:21.333 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0] from current transaction
2025-07-15 18:36:21.362 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-15 18:36:21.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-15 18:36:21.287763(Timestamp), $2a$10$lkI83B.UwGKvWMRa5Apz5uHSrPo1EaTWei.2jFwFaWHbjgz.F5O1W(String), 2025-07-29 18:36:21.0(Timestamp), 23(Long)
2025-07-15 18:36:21.368 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-15 18:36:21.369 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-07-15 18:36:21.371 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.service.impl.OAuth2ServiceImpl : Successfully processed OAuth2 user: Haohao268826
2025-07-15 18:36:21.382 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.383 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.383 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f4d0]
2025-07-15 18:36:21.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 302 FOUND
2025-07-15 18:36:21.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing POST /api/auth/refresh
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-07-15 18:36:24.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:36:24.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:24.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:24.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:24.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59]
2025-07-15 18:36:24.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82]
2025-07-15 18:36:24.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a]
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1322149759 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@9097741 wrapping com.mysql.cj.jdbc.ConnectionImpl@558789ac] will be managed by Spring
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@773773116 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will be managed by Spring
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:36:24.359 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:36:24.362 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-15 18:36:24.362 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-15 18:36:24.362 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-15 18:36:24.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a]
2025-07-15 18:36:24.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82]
2025-07-15 18:36:24.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59]
2025-07-15 18:36:24.363 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:24.363 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:24.363 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:24.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82] from current transaction
2025-07-15 18:36:24.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59] from current transaction
2025-07-15 18:36:24.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a] from current transaction
2025-07-15 18:36:24.364 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:24.364 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:24.364 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:24.364 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.364 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.364 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.369 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:24.369 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:24.369 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:24.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59]
2025-07-15 18:36:24.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82]
2025-07-15 18:36:24.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@8af3c59]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cb4f0a]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82]
2025-07-15 18:36:24.371 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19eee82]
2025-07-15 18:36:24.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:36:24.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:36:24.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:36:24.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/refresh] with attributes [permitAll]
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured POST /api/auth/refresh
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : POST "/api/auth/refresh", parameters={}
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:36:24.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:36:24.384 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.384 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.384 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbfcd] was not registered for synchronization because synchronization is not active
2025-07-15 18:36:24.384 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea] was not registered for synchronization because synchronization is not active
2025-07-15 18:36:24.384 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2113504990 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:36:24.384 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-07-15 18:36:24.385 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.386 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1411938062 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:36:24.386 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-07-15 18:36:24.386 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-07-15 18:36:24.389 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-07-15 18:36:24.389 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-07-15 18:36:24.390 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1dcfbfcd]
2025-07-15 18:36:24.390 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea]
2025-07-15 18:36:24.390 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.390 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ffc2cbd] was not registered for synchronization because synchronization is not active
2025-07-15 18:36:24.391 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@142321739 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:36:24.391 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-07-15 18:36:24.392 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-07-15 18:36:24.395 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-07-15 18:36:24.395 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ffc2cbd]
2025-07-15 18:36:24.395 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.395 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@599bde9e] was not registered for synchronization because synchronization is not active
2025-07-15 18:36:24.395 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@662477012 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:36:24.396 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:36:24.396 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-07-15 18:36:24.396 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.400 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-07-15 18:36:24.400 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@599bde9e]
2025-07-15 18:36:24.402 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:36:24.406 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.pure.controller.AuthController : 刷新令牌
2025-07-15 18:36:24.406 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 处理令牌刷新请求
2025-07-15 18:36:24.407 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-07-15T10: (truncated)...]
2025-07-15 18:36:24.409 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-07-15 18:36:24.412 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:24.419 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd24a3b]
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@440639050 wrapping com.mysql.cj.jdbc.ConnectionImpl@558789ac] will be managed by Spring
2025-07-15 18:36:24.419 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:24.420 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.423 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:24.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd24a3b]
2025-07-15 18:36:24.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:24.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd24a3b]
2025-07-15 18:36:24.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd24a3b]
2025-07-15 18:36:24.424 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dd24a3b]
2025-07-15 18:36:24.434 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 用户令牌创建成功: Haohao268826
2025-07-15 18:36:24.655 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 通过refreshToken移除设备成功: Key - user:devices:refreshToken:Haohao268826
2025-07-15 18:36:24.658 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 4b3dc98f-e4f7-4f65-9866-71d6edcdf10b
2025-07-15 18:36:24.661 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - fd33c08a-d28c-4994-9b7d-68a69f9d6d37
2025-07-15 18:36:24.661 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 用户 Haohao268826 刷新令牌后，Access Token 设备数超限，已移除最早的设备: $2a$10$Glk4TRU8oEbngl2psowBv.55ZE.rDFPEhrGpSNGy66vzwP7zVEihG
2025-07-15 18:36:24.664 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - 9d594f30-6443-48d3-8b5b-833e9802a0df
2025-07-15 18:36:24.667 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-07-15 18:36:24.667 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.668 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@df4d2be]
2025-07-15 18:36:24.668 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@558789ac] will be managed by Spring
2025-07-15 18:36:24.668 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-15 18:36:24.668 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-15 18:36:24.6666228(Timestamp), $2a$10$Rxu.8yDmmdXvpw3MnlyWTuFJO5JmbNAj1PT6j7dsdgvmZaDbxNog.(String), 2025-07-29 18:36:24.0(Timestamp), 23(Long)
2025-07-15 18:36:24.670 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-15 18:36:24.670 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@df4d2be]
2025-07-15 18:36:24.670 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-07-15 18:36:24.670 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@df4d2be]
2025-07-15 18:36:24.671 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@df4d2be]
2025-07-15 18:36:24.671 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@df4d2be]
2025-07-15 18:36:24.688 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.service.impl.AuthServiceImpl : 令牌刷新成功: Haohao268826
2025-07-15 18:36:24.698 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.698 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d372d12]
2025-07-15 18:36:24.698 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1542116550 wrapping com.mysql.cj.jdbc.ConnectionImpl@558789ac] will be managed by Spring
2025-07-15 18:36:24.698 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-15 18:36:24.699 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了刷新令牌(String), 2025-07-15T18:36:24.695040200(LocalDateTime)
2025-07-15 18:36:24.703 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-15 18:36:24.705 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d372d12]
2025-07-15 18:36:24.705 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了刷新令牌
2025-07-15 18:36:24.705 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d372d12]
2025-07-15 18:36:24.705 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d372d12]
2025-07-15 18:36:24.705 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d372d12]
2025-07-15 18:36:24.715 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:36:24.721 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=令牌刷新成功, success=true, data=TokenResponse(username=Haohao268826, accessToken (truncated)...]
2025-07-15 18:36:24.724 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:36:24.725 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:24.843 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/user/UserWithUserProfile
2025-07-15 18:36:24.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:24.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.user.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-07-15 18:36:24.922 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:24.923 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:24.923 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.923 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65464ae6]
2025-07-15 18:36:24.923 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@522209131 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:24.924 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:24.924 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.926 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:24.926 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65464ae6]
2025-07-15 18:36:24.926 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:24.926 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65464ae6]
2025-07-15 18:36:24.926 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65464ae6]
2025-07-15 18:36:24.926 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65464ae6]
2025-07-15 18:36:24.929 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:36:24.929 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user/UserWithUserProfile] with attributes [authenticated]
2025-07-15 18:36:24.929 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/user/UserWithUserProfile
2025-07-15 18:36:24.929 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/user/UserWithUserProfile", parameters={}
2025-07-15 18:36:24.930 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.user.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-07-15 18:36:24.935 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-15 18:36:24.948 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.service.impl.UserServiceImpl : 获取用户DTO, username: Haohao268826
2025-07-15 18:36:24.949 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.949 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be]
2025-07-15 18:36:24.949 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1147549791 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:24.949 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : ==>  Preparing: SELECT u.id AS u_id, u.username AS u_username, u.created_time AS u_created_time, u.updated_time AS u_updated_time, up.email AS up_email, up.nickname AS up_nickname, up.avatar AS up_avatar, up.phone AS up_phone, up.description AS up_description FROM user u LEFT JOIN user_profile up ON u.id = up.id WHERE u.username = ?
2025-07-15 18:36:24.949 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:36:24.953 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.UserWithUserProfileDTOByUsername : <==      Total: 1
2025-07-15 18:36:24.954 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be]
2025-07-15 18:36:24.955 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be] from current transaction
2025-07-15 18:36:24.955 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.findRolesByUserId : ==>  Preparing: SELECT r.id, r.name, r.created_time, r.updated_time FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:24.955 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.findRolesByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:24.957 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.findRolesByUserId : <==      Total: 1
2025-07-15 18:36:24.958 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be]
2025-07-15 18:36:24.958 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be]
2025-07-15 18:36:24.958 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be]
2025-07-15 18:36:24.958 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@50b3e6be]
2025-07-15 18:36:24.971 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.pure.controller.UserController : 获取用户信息：Haohao268826
2025-07-15 18:36:24.977 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.977 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.977 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1055398417 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:24.977 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-07-15 18:36:24.978 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2025-07-15(String)
2025-07-15 18:36:24.981 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-07-15 18:36:24.982 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.983 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02] from current transaction
2025-07-15 18:36:24.983 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-07-15 18:36:24.984 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2025-07-14(String)
2025-07-15 18:36:24.985 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-07-15 18:36:24.986 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.986 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02] from current transaction
2025-07-15 18:36:24.986 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.AccessLogMapper.insert : ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, created_time, updated_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-15 18:36:24.987 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.AccessLogMapper.insert : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 1(Integer), 2025-07-15(LocalDate), 2025-07-15T18:36:24.986766700(LocalDateTime), 2025-07-15T18:36:24.986766700(LocalDateTime), 127.0.0.1(String)
2025-07-15 18:36:24.989 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.AccessLogMapper.insert : <==    Updates: 1
2025-07-15 18:36:24.990 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.990 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.impl.AccessLogServiceImpl : Created new access log for user: 23, type: GET_USER_WITH_USER_PROFILE, count: 1
2025-07-15 18:36:24.990 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.990 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.990 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1766fc02]
2025-07-15 18:36:24.999 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:24.999 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@373d435d]
2025-07-15 18:36:24.999 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1302281744 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:24.999 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-15 18:36:25.000 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了操作的名称还未定义(String), 2025-07-15T18:36:24.998055600(LocalDateTime)
2025-07-15 18:36:25.002 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-15 18:36:25.002 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@373d435d]
2025-07-15 18:36:25.002 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了操作的名称还未定义
2025-07-15 18:36:25.002 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@373d435d]
2025-07-15 18:36:25.002 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@373d435d]
2025-07-15 18:36:25.002 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@373d435d]
2025-07-15 18:36:25.009 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:36:25.021 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=查询用户名获取全部资料成功, success=true, data=UserWithUserProfileDTO(id=23, username=Ha (truncated)...]
2025-07-15 18:36:25.023 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:36:25.023 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:53.745 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing POST /api/auth/refresh
2025-07-15 18:36:53.745 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:53.745 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-07-15 18:36:53.825 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:53.827 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:53.827 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:53.827 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b24a0dc]
2025-07-15 18:36:53.827 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1033315772 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:53.827 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:53.827 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:53.829 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:53.829 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b24a0dc]
2025-07-15 18:36:53.829 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:53.829 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b24a0dc]
2025-07-15 18:36:53.829 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b24a0dc]
2025-07-15 18:36:53.829 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2b24a0dc]
2025-07-15 18:36:53.831 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:36:53.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/refresh] with attributes [permitAll]
2025-07-15 18:36:53.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured POST /api/auth/refresh
2025-07-15 18:36:53.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : POST "/api/auth/refresh", parameters={}
2025-07-15 18:36:53.832 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#refreshToken(RefreshTokenRequest, HttpServletRequest, HttpServletResponse)
2025-07-15 18:36:53.835 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.pure.controller.AuthController : 刷新令牌
2025-07-15 18:36:53.836 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.AuthServiceImpl : 处理令牌刷新请求
2025-07-15 18:36:53.840 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:53.841 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:53.841 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:53.841 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20ad98ad]
2025-07-15 18:36:53.841 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1301984481 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:53.841 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:53.842 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:53.843 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:53.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20ad98ad]
2025-07-15 18:36:53.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:53.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20ad98ad]
2025-07-15 18:36:53.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20ad98ad]
2025-07-15 18:36:53.844 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20ad98ad]
2025-07-15 18:36:53.848 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.AuthServiceImpl : 用户令牌创建成功: Haohao268826
2025-07-15 18:36:54.124 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.DeviceServiceImpl : 通过refreshToken移除设备成功: Key - user:devices:refreshToken:Haohao268826
2025-07-15 18:36:54.126 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.DeviceServiceImpl : 用户设备数量已达上限，移除最早登录的设备: Key - user:devices:accessToken:Haohao268826, DeviceId - 1ccf1f1c-a8f2-479b-9fd5-d28a44dc90d5
2025-07-15 18:36:54.128 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:Haohao268826, DeviceId - 6b3ece88-09b9-4f21-85a9-0f1d8f0894b6
2025-07-15 18:36:54.128 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.AuthServiceImpl : 用户 Haohao268826 刷新令牌后，Access Token 设备数超限，已移除最早的设备: $2a$10$7Fi6haaw3p/KOoFtAw4ePu6SeizXtpJCLGFpHf0.B.QNNMzsCb6Xa
2025-07-15 18:36:54.132 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:Haohao268826, DeviceId - 7673d1f5-512b-449c-ac05-0b3de9767591
2025-07-15 18:36:54.135 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.UserServiceImpl : 更新用户信息, userId: 23
2025-07-15 18:36:54.135 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:54.135 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5989e8c]
2025-07-15 18:36:54.135 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:54.135 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-15 18:36:54.136 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.primary.UserMapper.update : ==> Parameters: Haohao268826(String), $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-15 18:36:54.1333882(Timestamp), $2a$10$eqDfWvUqtnLu/BxY.9Bom.8FLwGSvm24Gi/nPZP56WqduNmaq.xU6(String), 2025-07-29 18:36:53.0(Timestamp), 23(Long)
2025-07-15 18:36:54.138 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-15 18:36:54.138 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5989e8c]
2025-07-15 18:36:54.138 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.UserServiceImpl : 用户信息更新成功, userId: 23
2025-07-15 18:36:54.138 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5989e8c]
2025-07-15 18:36:54.138 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5989e8c]
2025-07-15 18:36:54.138 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5989e8c]
2025-07-15 18:36:54.156 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.service.impl.AuthServiceImpl : 令牌刷新成功: Haohao268826
2025-07-15 18:36:54.159 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:54.159 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52514c98]
2025-07-15 18:36:54.159 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2137922315 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:54.159 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-15 18:36:54.160 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了刷新令牌(String), 2025-07-15T18:36:54.158751600(LocalDateTime)
2025-07-15 18:36:54.161 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-15 18:36:54.162 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52514c98]
2025-07-15 18:36:54.162 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-9] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了刷新令牌
2025-07-15 18:36:54.162 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52514c98]
2025-07-15 18:36:54.162 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52514c98]
2025-07-15 18:36:54.162 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52514c98]
2025-07-15 18:36:54.170 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:36:54.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=令牌刷新成功, success=true, data=TokenResponse(username=Haohao268826, accessToken (truncated)...]
2025-07-15 18:36:54.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:36:54.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:36:54.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing GET /api/user/UserWithUserProfile
2025-07-15 18:36:54.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:36:54.257 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.user.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-07-15 18:36:54.339 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:36:54.341 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:36:54.341 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:54.341 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f35b2b]
2025-07-15 18:36:54.341 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@302006760 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:54.341 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:54.341 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:54.342 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:36:54.343 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f35b2b]
2025-07-15 18:36:54.343 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:36:54.343 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f35b2b]
2025-07-15 18:36:54.343 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f35b2b]
2025-07-15 18:36:54.343 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f35b2b]
2025-07-15 18:36:54.345 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:36:54.345 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user/UserWithUserProfile] with attributes [authenticated]
2025-07-15 18:36:54.345 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured GET /api/user/UserWithUserProfile
2025-07-15 18:36:54.346 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : GET "/api/user/UserWithUserProfile", parameters={}
2025-07-15 18:36:54.346 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.user.UserController#getUserWithUserProfileByUsername(HttpServletRequest, UserDetails)
2025-07-15 18:36:54.346 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-15 18:36:54.349 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.service.impl.UserServiceImpl : 获取用户DTO, username: Haohao268826
2025-07-15 18:36:54.354 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:54.354 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@579c429f]
2025-07-15 18:36:54.354 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@863431555 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:54.354 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.U.findRolesByUserId : ==>  Preparing: SELECT r.id, r.name, r.created_time, r.updated_time FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:36:54.354 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.U.findRolesByUserId : ==> Parameters: 23(Long)
2025-07-15 18:36:54.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.U.findRolesByUserId : <==      Total: 1
2025-07-15 18:36:54.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@579c429f]
2025-07-15 18:36:54.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@579c429f]
2025-07-15 18:36:54.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@579c429f]
2025-07-15 18:36:54.356 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@579c429f]
2025-07-15 18:36:54.358 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.pure.controller.UserController : 获取用户信息：Haohao268826
2025-07-15 18:36:54.360 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:54.360 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d210485]
2025-07-15 18:36:54.360 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@683464384 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:54.361 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-07-15 18:36:54.361 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 23(Long), GET_USER_WITH_USER_PROFILE(String), 2025-07-15(String)
2025-07-15 18:36:54.362 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 1
2025-07-15 18:36:54.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d210485]
2025-07-15 18:36:54.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d210485]
2025-07-15 18:36:54.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d210485]
2025-07-15 18:36:54.363 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d210485]
2025-07-15 18:36:54.369 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:36:54.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@481b8bfc]
2025-07-15 18:36:54.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1143469564 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:36:54.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-15 18:36:54.370 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 23(Long), 127.0.0.1(String), 0|0|0|内网IP|内网IP(String), Windows(String), Chrome(String), 用户Haohao268826执行了操作的名称还未定义(String), 2025-07-15T18:36:54.368897500(LocalDateTime)
2025-07-15 18:36:54.372 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-15 18:36:54.372 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@481b8bfc]
2025-07-15 18:36:54.372 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-10] c.e.p.s.impl.OperatingLogServiceImpl : 记录用户操作日志: userId=23, summary=用户Haohao268826执行了操作的名称还未定义
2025-07-15 18:36:54.372 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@481b8bfc]
2025-07-15 18:36:54.372 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@481b8bfc]
2025-07-15 18:36:54.372 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@481b8bfc]
2025-07-15 18:36:54.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:36:54.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=查询用户名获取全部资料成功, success=true, data=UserWithUserProfileDTO(id=23, username=Ha (truncated)...]
2025-07-15 18:36:54.378 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:36:54.379 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:04.025 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /api/user-profile
2025-07-15 18:37:04.025 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:04.026 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:04.105 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:04.107 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:04.107 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:04.107 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@189eed73]
2025-07-15 18:37:04.107 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1039842063 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:37:04.107 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:04.107 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:04.109 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:04.109 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@189eed73]
2025-07-15 18:37:04.109 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:04.109 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@189eed73]
2025-07-15 18:37:04.109 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@189eed73]
2025-07-15 18:37:04.109 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@189eed73]
2025-07-15 18:37:04.112 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:04.112 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user-profile] with attributes [authenticated]
2025-07-15 18:37:04.112 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /api/user-profile
2025-07-15 18:37:04.112 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/api/user-profile", parameters={}
2025-07-15 18:37:04.112 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:04.114 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.userprofile.UserProfileController] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:37:04.116 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.c.UserProfileController : 请求获取用户 'Haohao268826' 的详细信息
2025-07-15 18:37:04.117 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.s.impl.UserProfileServiceImpl : 根据用户名查找用户详情: Haohao268826
2025-07-15 18:37:04.117 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:04.117 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a92bcb] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:04.118 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1853817118 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:04.118 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUsername : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE username = ?
2025-07-15 18:37:04.118 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:37:04.121 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findUserProfileByUsername : <==      Total: 1
2025-07-15 18:37:04.121 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22a92bcb]
2025-07-15 18:37:04.121 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:04.130 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UserProfile(id=23, username=Haohao268826, phone=18 (truncated)...]
2025-07-15 18:37:04.133 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:04.133 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:04.136 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/user-profile
2025-07-15 18:37:04.136 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:04.136 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:04.213 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:04.214 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:04.214 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:04.214 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5abd84cc]
2025-07-15 18:37:04.214 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@765344337 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:37:04.215 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:04.215 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:04.217 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:04.217 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5abd84cc]
2025-07-15 18:37:04.217 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:04.217 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5abd84cc]
2025-07-15 18:37:04.217 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5abd84cc]
2025-07-15 18:37:04.217 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5abd84cc]
2025-07-15 18:37:04.220 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:04.220 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user-profile] with attributes [authenticated]
2025-07-15 18:37:04.220 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /api/user-profile
2025-07-15 18:37:04.220 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/api/user-profile", parameters={}
2025-07-15 18:37:04.220 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.userprofile.UserProfileController] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.c.UserProfileController : 请求获取用户 'Haohao268826' 的详细信息
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.s.impl.UserProfileServiceImpl : 根据用户名查找用户详情: Haohao268826
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@771c22a5] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1641329882 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUsername : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE username = ?
2025-07-15 18:37:04.221 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:37:04.224 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findUserProfileByUsername : <==      Total: 1
2025-07-15 18:37:04.224 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@771c22a5]
2025-07-15 18:37:04.224 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:04.225 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UserProfile(id=23, username=Haohao268826, phone=18 (truncated)...]
2025-07-15 18:37:04.225 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:04.225 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:06.689 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-07-15 18:37:06.689 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-07-15 18:37:06.689 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:06.689 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:06.689 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:37:06.689 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:37:06.766 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:06.766 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:06.767 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:06.767 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7de65138]
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2651b480]
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2008550982 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will be managed by Spring
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1392880257 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:06.767 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7de65138]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2651b480]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7de65138]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2651b480]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7de65138]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2651b480]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7de65138]
2025-07-15 18:37:06.770 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2651b480]
2025-07-15 18:37:06.772 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:06.772 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@457b1023] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2135547127 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41cffa75] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:06.773 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-07-15 18:37:06.774 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:06.774 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@569617732 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:06.774 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-07-15 18:37:06.774 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41cffa75]
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@457b1023]
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75552c0e] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@517898329 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-07-15T10: (truncated)...]
2025-07-15 18:37:06.776 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-07-15 18:37:06.777 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:06.777 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:06.778 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-07-15 18:37:06.778 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75552c0e]
2025-07-15 18:37:06.779 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:06.779 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3280e746] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:06.779 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1852414020 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:06.779 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-07-15 18:37:06.779 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:06.781 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-07-15 18:37:06.782 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3280e746]
2025-07-15 18:37:06.782 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:06.782 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-07-15 18:37:06.783 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:06.783 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:19.984 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/user-profile
2025-07-15 18:37:19.984 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:19.984 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:20.066 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:20.070 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:20.070 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:20.070 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b0f4aa9]
2025-07-15 18:37:20.070 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1747984797 wrapping com.mysql.cj.jdbc.ConnectionImpl@558789ac] will be managed by Spring
2025-07-15 18:37:20.070 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:20.071 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:20.073 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:20.073 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b0f4aa9]
2025-07-15 18:37:20.073 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:20.073 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b0f4aa9]
2025-07-15 18:37:20.073 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b0f4aa9]
2025-07-15 18:37:20.073 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b0f4aa9]
2025-07-15 18:37:20.075 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:20.076 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user-profile] with attributes [authenticated]
2025-07-15 18:37:20.076 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured GET /api/user-profile
2025-07-15 18:37:20.076 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : GET "/api/user-profile", parameters={}
2025-07-15 18:37:20.076 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.userprofile.UserProfileController] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.c.UserProfileController : 请求获取用户 'Haohao268826' 的详细信息
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.s.impl.UserProfileServiceImpl : 根据用户名查找用户详情: Haohao268826
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@548a2b9e] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1994638520 wrapping com.mysql.cj.jdbc.ConnectionImpl@558789ac] will not be managed by Spring
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUsername : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE username = ?
2025-07-15 18:37:20.077 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:37:20.079 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] c.e.p.m.p.U.findUserProfileByUsername : <==      Total: 1
2025-07-15 18:37:20.079 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@548a2b9e]
2025-07-15 18:37:20.079 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:20.080 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UserProfile(id=23, username=Haohao268826, phone=18 (truncated)...]
2025-07-15 18:37:20.081 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:20.082 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:20.085 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/user-profile
2025-07-15 18:37:20.085 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:20.085 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:20.162 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:20.163 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:20.163 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:20.164 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61b5562]
2025-07-15 18:37:20.164 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1451739579 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:37:20.164 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:20.164 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:20.166 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:20.167 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61b5562]
2025-07-15 18:37:20.167 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:20.167 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61b5562]
2025-07-15 18:37:20.167 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61b5562]
2025-07-15 18:37:20.167 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61b5562]
2025-07-15 18:37:20.170 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:20.170 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/user-profile] with attributes [authenticated]
2025-07-15 18:37:20.170 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/user-profile
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : GET "/api/user-profile", parameters={}
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.userprofile.UserProfileController#getUserProfileByUsername(UserDetails)
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.a.i.a.MethodSecurityInterceptor : Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.userprofile.UserProfileController] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.c.UserProfileController : 请求获取用户 'Haohao268826' 的详细信息
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.s.impl.UserProfileServiceImpl : 根据用户名查找用户详情: Haohao268826
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:20.171 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59039b8] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:20.172 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1291969739 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:20.172 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUsername : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE username = ?
2025-07-15 18:37:20.172 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUsername : ==> Parameters: Haohao268826(String)
2025-07-15 18:37:20.174 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] c.e.p.m.p.U.findUserProfileByUsername : <==      Total: 1
2025-07-15 18:37:20.174 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59039b8]
2025-07-15 18:37:20.175 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:20.175 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UserProfile(id=23, username=Haohao268826, phone=18 (truncated)...]
2025-07-15 18:37:20.176 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:20.176 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:22.153 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/messages/unread
2025-07-15 18:37:22.153 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing GET /api/messages/users/unread-count
2025-07-15 18:37:22.153 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:22.153 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-15 18:37:22.154 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:37:22.154 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:37:22.250 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:22.250 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-15 18:37:22.252 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:22.252 [34mINFO [0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-15 18:37:22.252 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:22.252 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:22.252 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3e8065]
2025-07-15 18:37:22.252 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da9f4d7]
2025-07-15 18:37:22.253 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2004961240 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will be managed by Spring
2025-07-15 18:37:22.253 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@444815287 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will be managed by Spring
2025-07-15 18:37:22.253 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:22.253 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-15 18:37:22.253 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:22.253 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:22.255 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:22.255 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da9f4d7]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3e8065]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da9f4d7]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3e8065]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da9f4d7]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3e8065]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da9f4d7]
2025-07-15 18:37:22.256 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c3e8065]
2025-07-15 18:37:22.259 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:22.259 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/users/unread-count] with attributes [authenticated]
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/messages/unread] with attributes [authenticated]
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured GET /api/messages/users/unread-count
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/messages/unread
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : GET "/api/messages/users/unread-count", parameters={}
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/messages/unread", parameters={}
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUnreadMessageCount(CustomUserDetails)
2025-07-15 18:37:22.260 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.messages.MessagesController#getUserMessages(Authentication, int, int, String)
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b0f1510] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@603843292 wrapping com.mysql.cj.jdbc.ConnectionImpl@f883221] will not be managed by Spring
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3d3759] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==>  Preparing: SELECT COUNT(*) FROM user_messages WHERE user_id = ? AND is_read = false
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@201441093 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==>  Preparing: SELECT COUNT(*) FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ?
2025-07-15 18:37:22.261 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : ==> Parameters: 23(Long)
2025-07-15 18:37:22.264 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.M.countUserMessagesWithDetails : <==      Total: 1
2025-07-15 18:37:22.264 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] c.e.p.m.p.M.countUnreadMessagesByUserId : <==      Total: 1
2025-07-15 18:37:22.264 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b0f1510]
2025-07-15 18:37:22.264 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3d3759]
2025-07-15 18:37:22.264 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:22.264 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11895942] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:22.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@369697749 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:37:22.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:22.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==>  Preparing: SELECT m.id, m.sender_id AS senderId, m.title, m.content, m.message_type AS messageType, m.created_time AS createdTime, um.is_read AS isRead, um.read_time AS readTime FROM messages m JOIN user_messages um ON m.id = um.message_id WHERE um.user_id = ? ORDER BY m.created_time DESC LIMIT ? OFFSET ?
2025-07-15 18:37:22.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=UnreadCountDTO(unreadCount=0), time=2025-07-15T10: (truncated)...]
2025-07-15 18:37:22.265 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : ==> Parameters: 23(Long), 10(Integer), 0(Integer)
2025-07-15 18:37:22.266 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:22.266 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:22.267 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.M.selectUserMessagesWithDetails : <==      Total: 0
2025-07-15 18:37:22.268 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@11895942]
2025-07-15 18:37:22.268 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-15 18:37:22.268 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d76970f] was not registered for synchronization because synchronization is not active
2025-07-15 18:37:22.268 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@107910826 wrapping com.mysql.cj.jdbc.ConnectionImpl@7780a4e3] will not be managed by Spring
2025-07-15 18:37:22.268 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==>  Preparing: SELECT id, username, phone, email, nickname, avatar, description FROM user_profile WHERE id = ?
2025-07-15 18:37:22.268 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : ==> Parameters: 23(Long)
2025-07-15 18:37:22.271 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] c.e.p.m.p.U.findUserProfileByUserId : <==      Total: 1
2025-07-15 18:37:22.272 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d76970f]
2025-07-15 18:37:22.273 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-15 18:37:22.273 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=操作成功, success=true, data=PageFinalResult(list=[], pageResult=PageResult(tot (truncated)...]
2025-07-15 18:37:22.274 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-15 18:37:22.274 [39mDEBUG[0;39m 3364 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-15 18:37:48.883 [39mDEBUG[0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-15 18:37:48.883 [39mDEBUG[0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-15 18:37:48.883 [34mINFO [0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-15 18:37:48.883 [34mINFO [0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ab0168]]
2025-07-15 18:37:48.883 [39mDEBUG[0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ab0168]
2025-07-15 18:37:48.883 [39mDEBUG[0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ab0168]
2025-07-15 18:37:48.883 [34mINFO [0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-15 18:37:48.883 [39mDEBUG[0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-15 18:37:48.883 [39mDEBUG[0;39m 3364 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-15 18:37:49.513 [34mINFO [0;39m 3364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-15 18:37:49.523 [34mINFO [0;39m 3364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
