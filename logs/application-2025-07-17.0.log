2025-07-17 14:29:00.886 [34mINFO [0;39m 2580 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-17 14:29:00.887 [34mINFO [0;39m 2580 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 2580 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-17 14:29:00.888 [39mDEBUG[0;39m 2580 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-17 14:29:00.889 [34mINFO [0;39m 2580 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-17 14:29:02.038 [34mINFO [0;39m 2580 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-17 14:29:02.041 [34mINFO [0;39m 2580 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 14:29:02.107 [34mINFO [0;39m 2580 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-17 14:29:02.309 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-17 14:29:02.310 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-17 14:29:02.310 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-17 14:29:02.310 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-17 14:29:02.310 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-17 14:29:02.310 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-17 14:29:02.312 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-17 14:29:02.312 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-17 14:29:02.312 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-17 14:29:02.313 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-17 14:29:02.314 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-17 14:29:02.314 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-17 14:29:02.314 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-17 14:29:02.314 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-17 14:29:02.314 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-17 14:29:02.314 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-17 14:29:02.315 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-17 14:29:02.316 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-17 14:29:02.316 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-17 14:29:02.316 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-17 14:29:02.317 [39mDEBUG[0;39m 2580 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-17 14:29:03.203 [34mINFO [0;39m 2580 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-17 14:29:03.210 [34mINFO [0;39m 2580 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 14:29:03.211 [34mINFO [0;39m 2580 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-17 14:29:03.211 [34mINFO [0;39m 2580 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 14:29:03.342 [34mINFO [0;39m 2580 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-17 14:29:03.343 [34mINFO [0;39m 2580 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2409 ms
2025-07-17 14:29:03.625 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-17 14:29:03.637 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-17 14:29:03.644 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-17 14:29:03.652 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-17 14:29:03.666 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-17 14:29:03.672 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-17 14:29:03.679 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-17 14:29:03.686 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-17 14:29:03.695 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-17 14:29:03.706 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-17 14:29:03.719 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-17 14:29:03.727 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-17 14:29:03.733 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-17 14:29:03.742 [39mDEBUG[0;39m 2580 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-17 14:29:03.759 [34mINFO [0;39m 2580 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-17 14:29:05.114 [1;31mERROR[0;39m 2580 --- [main] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:833)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:416)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:332)
	at org.springframework.boot.jdbc.EmbeddedDatabaseConnection.isEmbedded(EmbeddedDatabaseConnection.java:164)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.isEmbeddedDatabase(DataSourceScriptDatabaseInitializer.java:70)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.isEnabled(AbstractScriptDatabaseInitializer.java:83)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:106)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.pure.PureApplication.main(PureApplication.java:37)
2025-07-17 14:29:05.804 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-17 14:29:05.805 [39mDEBUG[0;39m 2580 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-17 14:29:06.093 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 14:29:06.096 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.565 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-17 14:29:06.744 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-17 14:29:06.751 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-17 14:29:06.805 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 14:29:06.805 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.805 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-17 14:29:06.807 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.807 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-17 14:29:06.807 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.807 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 14:29:06.807 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.807 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-17 14:29:06.808 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.808 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-17 14:29:06.808 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.811 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 14:29:06.811 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.811 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-17 14:29:06.812 [39mDEBUG[0;39m 2580 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-17 14:29:06.901 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-17 14:29:06.902 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-17 14:29:06.907 [34mINFO [0;39m 2580 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@4a561f7d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@601ca994, org.springframework.security.web.context.SecurityContextPersistenceFilter@969c2f9, org.springframework.security.web.header.HeaderWriterFilter@4b8c9729, org.springframework.security.web.authentication.logout.LogoutFilter@7f38545a, com.example.pure.filter.JwtFilter@40277077, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7d741200, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@36e11b60, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1acd2a14, org.springframework.security.web.session.SessionManagementFilter@3e8995cc, org.springframework.security.web.access.ExceptionTranslationFilter@47a90d2a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@542c6165]
2025-07-17 14:29:06.910 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-17 14:29:06.912 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-17 14:29:06.912 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-17 14:29:06.912 [34mINFO [0;39m 2580 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-17 14:29:07.059 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-17 14:29:07.081 [34mINFO [0;39m 2580 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-17 14:29:07.143 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-17 14:29:07.150 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-17 14:29:07.495 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-17 14:29:07.592 [34mINFO [0;39m 2580 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-17 14:29:07.610 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-17 14:29:07.611 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-17 14:29:07.612 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-17 14:29:07.613 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-17 14:29:07.613 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-17 14:29:07.613 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-17 14:29:07.613 [39mDEBUG[0;39m 2580 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-17 14:29:07.613 [34mINFO [0;39m 2580 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1fcef6f9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@57cfb4d8, org.springframework.security.web.context.SecurityContextPersistenceFilter@4c5e123d, org.springframework.security.web.header.HeaderWriterFilter@751526a7, org.springframework.web.filter.CorsFilter@d4f2f3d, org.springframework.security.web.authentication.logout.LogoutFilter@64f1b2fb, com.example.pure.filter.JwtFilter@40277077, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69542112, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@35a81281, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e5bce8e, org.springframework.security.web.session.SessionManagementFilter@2279e7c7, org.springframework.security.web.access.ExceptionTranslationFilter@31c29db1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@47e89fed]
2025-07-17 14:29:07.649 [39mTRACE[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@35e52059, started on Thu Jul 17 14:29:00 CST 2025
2025-07-17 14:29:07.660 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-17 14:29:07.661 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-17 14:29:07.664 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-17 14:29:07.665 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-17 14:29:07.665 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-17 14:29:07.665 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-17 14:29:07.665 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-17 14:29:07.748 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-17 14:29:07.777 [39mDEBUG[0;39m 2580 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-17 14:29:07.990 [34mINFO [0;39m 2580 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 14:29:08.000 [34mINFO [0;39m 2580 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-17 14:29:08.001 [39mDEBUG[0;39m 2580 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 14:29:08.002 [39mDEBUG[0;39m 2580 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 14:29:08.002 [34mINFO [0;39m 2580 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-17 14:29:08.002 [39mDEBUG[0;39m 2580 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6ea0dfaa]
2025-07-17 14:29:08.002 [39mDEBUG[0;39m 2580 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6ea0dfaa]
2025-07-17 14:29:08.002 [34mINFO [0;39m 2580 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6ea0dfaa]]
2025-07-17 14:29:08.002 [34mINFO [0;39m 2580 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-17 14:29:08.002 [39mDEBUG[0;39m 2580 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 14:29:08.002 [39mDEBUG[0;39m 2580 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 14:29:08.014 [34mINFO [0;39m 2580 --- [main] com.example.pure.PureApplication : Started PureApplication in 7.549 seconds (JVM running for 8.96)
2025-07-17 14:29:30.439 [39mDEBUG[0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 14:29:30.439 [39mDEBUG[0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 14:29:30.439 [34mINFO [0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-17 14:29:30.439 [34mINFO [0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6ea0dfaa]]
2025-07-17 14:29:30.439 [39mDEBUG[0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6ea0dfaa]
2025-07-17 14:29:30.439 [39mDEBUG[0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6ea0dfaa]
2025-07-17 14:29:30.439 [34mINFO [0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-17 14:29:30.439 [39mDEBUG[0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 14:29:30.439 [39mDEBUG[0;39m 2580 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:48:19.581 [34mINFO [0;39m 24088 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-17 22:48:19.582 [34mINFO [0;39m 24088 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 24088 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-17 22:48:19.584 [39mDEBUG[0;39m 24088 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-17 22:48:19.584 [34mINFO [0;39m 24088 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-17 22:48:20.750 [34mINFO [0;39m 24088 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-17 22:48:20.753 [34mINFO [0;39m 24088 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 22:48:20.815 [34mINFO [0;39m 24088 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-07-17 22:48:20.972 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-17 22:48:20.972 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-17 22:48:20.973 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-17 22:48:20.975 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-17 22:48:20.978 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-17 22:48:20.978 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-17 22:48:20.978 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-17 22:48:20.978 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-17 22:48:20.979 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-17 22:48:20.979 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-17 22:48:20.979 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-17 22:48:20.980 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-17 22:48:20.980 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-17 22:48:20.980 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-17 22:48:20.981 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-17 22:48:20.981 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-17 22:48:20.981 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-17 22:48:20.981 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-17 22:48:20.981 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-17 22:48:20.981 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-17 22:48:20.982 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-17 22:48:20.982 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-17 22:48:20.982 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-17 22:48:20.982 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-17 22:48:20.982 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-17 22:48:20.982 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-17 22:48:20.983 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-17 22:48:20.983 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-17 22:48:20.983 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-17 22:48:20.983 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-17 22:48:20.984 [39mDEBUG[0;39m 24088 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-17 22:48:21.559 [34mINFO [0;39m 24088 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-17 22:48:21.567 [34mINFO [0;39m 24088 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 22:48:21.568 [34mINFO [0;39m 24088 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-17 22:48:21.569 [34mINFO [0;39m 24088 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 22:48:21.703 [34mINFO [0;39m 24088 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-17 22:48:21.704 [34mINFO [0;39m 24088 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2075 ms
2025-07-17 22:48:22.040 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-17 22:48:22.054 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-17 22:48:22.061 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-17 22:48:22.069 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-17 22:48:22.081 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-17 22:48:22.086 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-17 22:48:22.092 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-17 22:48:22.097 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-17 22:48:22.105 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-17 22:48:22.113 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-17 22:48:22.130 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-17 22:48:22.142 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-17 22:48:22.161 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-17 22:48:22.176 [39mDEBUG[0;39m 24088 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-17 22:48:22.188 [34mINFO [0;39m 24088 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-17 22:48:23.445 [1;31mERROR[0;39m 24088 --- [main] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:833)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:416)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:332)
	at org.springframework.boot.jdbc.EmbeddedDatabaseConnection.isEmbedded(EmbeddedDatabaseConnection.java:164)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.isEmbeddedDatabase(DataSourceScriptDatabaseInitializer.java:70)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.isEnabled(AbstractScriptDatabaseInitializer.java:83)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:106)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.pure.PureApplication.main(PureApplication.java:37)
2025-07-17 22:48:24.046 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-17 22:48:24.047 [39mDEBUG[0;39m 24088 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-17 22:48:24.303 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 22:48:24.305 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.702 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-17 22:48:24.881 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-17 22:48:24.889 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-17 22:48:24.951 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:48:24.951 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.952 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:48:24.952 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.953 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-17 22:48:24.954 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.954 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 22:48:24.954 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.954 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-17 22:48:24.954 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.955 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-17 22:48:24.955 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.958 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:48:24.958 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:24.958 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-17 22:48:24.958 [39mDEBUG[0;39m 24088 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-17 22:48:25.040 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-17 22:48:25.042 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-17 22:48:25.045 [34mINFO [0;39m 24088 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@c6db00d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@732fa176, org.springframework.security.web.context.SecurityContextPersistenceFilter@1267f832, org.springframework.security.web.header.HeaderWriterFilter@488c608a, org.springframework.security.web.authentication.logout.LogoutFilter@7bfedfb7, com.example.pure.filter.JwtFilter@b5390, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@73eaae1e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@659565ed, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@413e8246, org.springframework.security.web.session.SessionManagementFilter@1a7f2d34, org.springframework.security.web.access.ExceptionTranslationFilter@55d776ac, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@94b5fe3]
2025-07-17 22:48:25.047 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-17 22:48:25.049 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-17 22:48:25.049 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-17 22:48:25.050 [34mINFO [0;39m 24088 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-17 22:48:25.204 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-17 22:48:25.226 [34mINFO [0;39m 24088 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-17 22:48:25.285 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-17 22:48:25.293 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-17 22:48:25.627 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-17 22:48:25.771 [34mINFO [0;39m 24088 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-17 22:48:25.793 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-17 22:48:25.794 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-17 22:48:25.795 [39mDEBUG[0;39m 24088 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-17 22:48:25.796 [34mINFO [0;39m 24088 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@72b60be8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6f254d79, org.springframework.security.web.context.SecurityContextPersistenceFilter@1bf4a79b, org.springframework.security.web.header.HeaderWriterFilter@3aac31b7, org.springframework.web.filter.CorsFilter@589cc8eb, org.springframework.security.web.authentication.logout.LogoutFilter@4f239a76, com.example.pure.filter.JwtFilter@b5390, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a1be5cb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@51e14cb6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@44233014, org.springframework.security.web.session.SessionManagementFilter@57def953, org.springframework.security.web.access.ExceptionTranslationFilter@423791, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@da2303c]
2025-07-17 22:48:25.830 [39mTRACE[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3b718392, started on Thu Jul 17 22:48:19 CST 2025
2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-17 22:48:25.843 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-17 22:48:25.844 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-17 22:48:25.846 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-07-17 22:48:25.848 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-17 22:48:25.848 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-17 22:48:25.848 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-17 22:48:25.848 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-17 22:48:25.931 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-17 22:48:25.970 [39mDEBUG[0;39m 24088 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-17 22:48:26.236 [34mINFO [0;39m 24088 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 22:48:26.245 [34mINFO [0;39m 24088 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-17 22:48:26.247 [39mDEBUG[0;39m 24088 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 22:48:26.247 [39mDEBUG[0;39m 24088 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 22:48:26.247 [34mINFO [0;39m 24088 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-17 22:48:26.247 [39mDEBUG[0;39m 24088 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2292e793]
2025-07-17 22:48:26.247 [39mDEBUG[0;39m 24088 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2292e793]
2025-07-17 22:48:26.247 [34mINFO [0;39m 24088 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2292e793]]
2025-07-17 22:48:26.247 [34mINFO [0;39m 24088 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-17 22:48:26.248 [39mDEBUG[0;39m 24088 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:48:26.248 [39mDEBUG[0;39m 24088 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:48:26.260 [34mINFO [0;39m 24088 --- [main] com.example.pure.PureApplication : Started PureApplication in 7.192 seconds (JVM running for 7.97)
2025-07-17 22:49:25.819 [34mINFO [0;39m 24088 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-17 22:50:00.434 [39mDEBUG[0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 22:50:00.434 [39mDEBUG[0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 22:50:00.434 [34mINFO [0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-17 22:50:00.434 [34mINFO [0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2292e793]]
2025-07-17 22:50:00.435 [39mDEBUG[0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2292e793]
2025-07-17 22:50:00.435 [39mDEBUG[0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2292e793]
2025-07-17 22:50:00.435 [34mINFO [0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-17 22:50:00.435 [39mDEBUG[0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:00.435 [39mDEBUG[0;39m 24088 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:04.891 [34mINFO [0;39m 22456 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 22456 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-17 22:50:04.893 [39mDEBUG[0;39m 22456 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-17 22:50:04.893 [34mINFO [0;39m 22456 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-17 22:50:04.894 [34mINFO [0;39m 22456 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-17 22:50:05.920 [34mINFO [0;39m 22456 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-17 22:50:05.923 [34mINFO [0;39m 22456 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 22:50:05.961 [34mINFO [0;39m 22456 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-17 22:50:06.071 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-17 22:50:06.072 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-17 22:50:06.073 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-17 22:50:06.074 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-17 22:50:06.074 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-17 22:50:06.074 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-17 22:50:06.074 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-17 22:50:06.075 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-17 22:50:06.075 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-17 22:50:06.075 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-17 22:50:06.075 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-17 22:50:06.075 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-17 22:50:06.075 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-17 22:50:06.076 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-17 22:50:06.077 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-17 22:50:06.078 [39mDEBUG[0;39m 22456 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-17 22:50:06.526 [34mINFO [0;39m 22456 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-17 22:50:06.531 [34mINFO [0;39m 22456 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 22:50:06.532 [34mINFO [0;39m 22456 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-17 22:50:06.532 [34mINFO [0;39m 22456 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 22:50:06.622 [34mINFO [0;39m 22456 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-17 22:50:06.622 [34mINFO [0;39m 22456 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1681 ms
2025-07-17 22:50:06.866 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-17 22:50:06.877 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-17 22:50:06.883 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-17 22:50:06.889 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-17 22:50:06.901 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-17 22:50:06.906 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-17 22:50:06.912 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-17 22:50:06.916 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-17 22:50:06.923 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-17 22:50:06.931 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-17 22:50:06.942 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-17 22:50:06.947 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-17 22:50:06.951 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-17 22:50:06.956 [39mDEBUG[0;39m 22456 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-17 22:50:06.967 [34mINFO [0;39m 22456 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-17 22:50:07.210 [34mINFO [0;39m 22456 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-17 22:50:07.773 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-17 22:50:07.773 [39mDEBUG[0;39m 22456 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-17 22:50:08.040 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 22:50:08.042 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.465 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-17 22:50:08.604 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-17 22:50:08.611 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-17 22:50:08.662 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-17 22:50:08.663 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.664 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-17 22:50:08.664 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.664 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:50:08.664 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.665 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 22:50:08.665 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.665 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-17 22:50:08.665 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.665 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:50:08.665 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.668 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-17 22:50:08.669 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.669 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:50:08.669 [39mDEBUG[0;39m 22456 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:08.764 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-17 22:50:08.765 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-17 22:50:08.768 [34mINFO [0;39m 22456 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@153cf928, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4884350b, org.springframework.security.web.context.SecurityContextPersistenceFilter@6d71f296, org.springframework.security.web.header.HeaderWriterFilter@751526a7, org.springframework.security.web.authentication.logout.LogoutFilter@a1be5cb, com.example.pure.filter.JwtFilter@723b8eff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5319efc0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@549fe529, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@53cba89f, org.springframework.security.web.session.SessionManagementFilter@518ed9b4, org.springframework.security.web.access.ExceptionTranslationFilter@cdc09d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@54a98358]
2025-07-17 22:50:08.770 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-17 22:50:08.772 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-17 22:50:08.772 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-17 22:50:08.772 [34mINFO [0;39m 22456 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-17 22:50:08.908 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-17 22:50:08.930 [34mINFO [0;39m 22456 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-17 22:50:08.989 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-17 22:50:08.996 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-17 22:50:09.322 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-17 22:50:09.423 [34mINFO [0;39m 22456 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-17 22:50:09.442 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-17 22:50:09.443 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-17 22:50:09.444 [39mDEBUG[0;39m 22456 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-17 22:50:09.444 [34mINFO [0;39m 22456 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4b0df349, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@21f7c0be, org.springframework.security.web.context.SecurityContextPersistenceFilter@798d8247, org.springframework.security.web.header.HeaderWriterFilter@1a13b5c1, org.springframework.web.filter.CorsFilter@48463900, org.springframework.security.web.authentication.logout.LogoutFilter@3941bdd7, com.example.pure.filter.JwtFilter@723b8eff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@41623694, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46d83ec4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5834298c, org.springframework.security.web.session.SessionManagementFilter@1b6e32ee, org.springframework.security.web.access.ExceptionTranslationFilter@20331c8e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7c7d83ff]
2025-07-17 22:50:09.474 [39mTRACE[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d9fc57a, started on Thu Jul 17 22:50:04 CST 2025
2025-07-17 22:50:09.485 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-17 22:50:09.486 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-17 22:50:09.487 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-17 22:50:09.487 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-17 22:50:09.489 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-07-17 22:50:09.490 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-17 22:50:09.490 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-17 22:50:09.490 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-17 22:50:09.491 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-17 22:50:09.559 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-17 22:50:09.590 [39mDEBUG[0;39m 22456 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-17 22:50:09.802 [34mINFO [0;39m 22456 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 22:50:09.810 [34mINFO [0;39m 22456 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-17 22:50:09.812 [39mDEBUG[0;39m 22456 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 22:50:09.812 [39mDEBUG[0;39m 22456 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 22:50:09.812 [34mINFO [0;39m 22456 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-17 22:50:09.812 [39mDEBUG[0;39m 22456 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1912ba29]
2025-07-17 22:50:09.812 [39mDEBUG[0;39m 22456 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1912ba29]
2025-07-17 22:50:09.812 [34mINFO [0;39m 22456 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1912ba29]]
2025-07-17 22:50:09.812 [34mINFO [0;39m 22456 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-17 22:50:09.812 [39mDEBUG[0;39m 22456 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:09.812 [39mDEBUG[0;39m 22456 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:09.824 [34mINFO [0;39m 22456 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.442 seconds (JVM running for 6.359)
2025-07-17 22:50:12.185 [39mDEBUG[0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 22:50:12.185 [39mDEBUG[0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 22:50:12.185 [34mINFO [0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-17 22:50:12.185 [34mINFO [0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1912ba29]]
2025-07-17 22:50:12.185 [39mDEBUG[0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1912ba29]
2025-07-17 22:50:12.185 [39mDEBUG[0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1912ba29]
2025-07-17 22:50:12.185 [34mINFO [0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-17 22:50:12.185 [39mDEBUG[0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:12.185 [39mDEBUG[0;39m 22456 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:12.480 [34mINFO [0;39m 22456 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-17 22:50:12.493 [34mINFO [0;39m 22456 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-17 22:50:16.318 [34mINFO [0;39m 5896 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 5896 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-17 22:50:16.317 [34mINFO [0;39m 5896 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-17 22:50:16.320 [39mDEBUG[0;39m 5896 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-17 22:50:16.321 [34mINFO [0;39m 5896 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-17 22:50:17.264 [34mINFO [0;39m 5896 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-17 22:50:17.266 [34mINFO [0;39m 5896 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 22:50:17.299 [34mINFO [0;39m 5896 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-17 22:50:17.386 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-17 22:50:17.387 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-17 22:50:17.387 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-17 22:50:17.388 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-17 22:50:17.388 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-17 22:50:17.388 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-17 22:50:17.388 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-17 22:50:17.389 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-17 22:50:17.389 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-17 22:50:17.389 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-17 22:50:17.389 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-17 22:50:17.389 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-17 22:50:17.389 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-17 22:50:17.390 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-17 22:50:17.391 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-17 22:50:17.392 [39mDEBUG[0;39m 5896 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-17 22:50:17.913 [34mINFO [0;39m 5896 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-17 22:50:17.919 [34mINFO [0;39m 5896 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 22:50:17.920 [34mINFO [0;39m 5896 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-17 22:50:17.920 [34mINFO [0;39m 5896 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 22:50:18.039 [34mINFO [0;39m 5896 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-17 22:50:18.039 [34mINFO [0;39m 5896 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1677 ms
2025-07-17 22:50:18.302 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-17 22:50:18.313 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-17 22:50:18.319 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-17 22:50:18.326 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-17 22:50:18.337 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-17 22:50:18.341 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-17 22:50:18.347 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-17 22:50:18.353 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-17 22:50:18.367 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-17 22:50:18.374 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-17 22:50:18.384 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-17 22:50:18.389 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-17 22:50:18.393 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-17 22:50:18.398 [39mDEBUG[0;39m 5896 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-17 22:50:18.408 [34mINFO [0;39m 5896 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-17 22:50:18.651 [34mINFO [0;39m 5896 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-17 22:50:19.231 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-17 22:50:19.232 [39mDEBUG[0;39m 5896 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-17 22:50:19.487 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 22:50:19.489 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:19.921 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-17 22:50:20.070 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-17 22:50:20.077 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-17 22:50:20.137 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:50:20.137 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.138 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-17 22:50:20.140 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.140 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-17 22:50:20.140 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.141 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-17 22:50:20.141 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.141 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-17 22:50:20.141 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.141 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:50:20.141 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.145 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-17 22:50:20.145 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.146 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-17 22:50:20.146 [39mDEBUG[0;39m 5896 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-17 22:50:20.231 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-17 22:50:20.232 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-17 22:50:20.236 [34mINFO [0;39m 5896 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2a20aa4e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3f7d73e3, org.springframework.security.web.context.SecurityContextPersistenceFilter@ce5a2ea, org.springframework.security.web.header.HeaderWriterFilter@1b62ad1e, org.springframework.security.web.authentication.logout.LogoutFilter@51a73873, com.example.pure.filter.JwtFilter@706c062e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5de32e00, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@219ec4b6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@513ddf5f, org.springframework.security.web.session.SessionManagementFilter@648bfebe, org.springframework.security.web.access.ExceptionTranslationFilter@64ff0eaa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5ac35b17]
2025-07-17 22:50:20.238 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-17 22:50:20.239 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-17 22:50:20.240 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-17 22:50:20.240 [34mINFO [0;39m 5896 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-17 22:50:20.394 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-17 22:50:20.417 [34mINFO [0;39m 5896 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-17 22:50:20.495 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-17 22:50:20.505 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-17 22:50:20.966 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-17 22:50:21.063 [34mINFO [0;39m 5896 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-17 22:50:21.082 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-17 22:50:21.083 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-17 22:50:21.084 [39mDEBUG[0;39m 5896 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-17 22:50:21.085 [34mINFO [0;39m 5896 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3dbcde0b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c168f98, org.springframework.security.web.context.SecurityContextPersistenceFilter@69860811, org.springframework.security.web.header.HeaderWriterFilter@14b0b7f9, org.springframework.web.filter.CorsFilter@2f98859a, org.springframework.security.web.authentication.logout.LogoutFilter@58ec3e8b, com.example.pure.filter.JwtFilter@706c062e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3ad80206, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b4675db, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6e95c023, org.springframework.security.web.session.SessionManagementFilter@16a78700, org.springframework.security.web.access.ExceptionTranslationFilter@301c622d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@30ae3c46]
2025-07-17 22:50:21.119 [39mTRACE[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3fcdcf, started on Thu Jul 17 22:50:16 CST 2025
2025-07-17 22:50:21.130 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-17 22:50:21.131 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-17 22:50:21.132 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-17 22:50:21.132 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-17 22:50:21.132 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-17 22:50:21.134 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
2025-07-17 22:50:21.136 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-17 22:50:21.136 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-17 22:50:21.136 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-17 22:50:21.136 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-17 22:50:21.206 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-17 22:50:21.234 [39mDEBUG[0;39m 5896 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-17 22:50:21.453 [34mINFO [0;39m 5896 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 22:50:21.462 [34mINFO [0;39m 5896 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-17 22:50:21.463 [39mDEBUG[0;39m 5896 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 22:50:21.463 [39mDEBUG[0;39m 5896 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 22:50:21.464 [34mINFO [0;39m 5896 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-17 22:50:21.464 [39mDEBUG[0;39m 5896 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a29520e]
2025-07-17 22:50:21.464 [39mDEBUG[0;39m 5896 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a29520e]
2025-07-17 22:50:21.464 [34mINFO [0;39m 5896 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a29520e]]
2025-07-17 22:50:21.464 [34mINFO [0;39m 5896 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-17 22:50:21.464 [39mDEBUG[0;39m 5896 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:21.464 [39mDEBUG[0;39m 5896 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:50:21.476 [34mINFO [0;39m 5896 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.637 seconds (JVM running for 6.653)
2025-07-17 22:50:57.707 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 22:50:57.707 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-17 22:50:57.707 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-17 22:50:57.707 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-17 22:50:57.707 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-17 22:50:57.709 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@3477b872
2025-07-17 22:50:57.709 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@7a7c0958
2025-07-17 22:50:57.709 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-17 22:50:57.710 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-07-17 22:50:57.721 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing GET /actuator/health
2025-07-17 22:50:57.723 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-17 22:50:58.168 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-17 22:50:58.181 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-17 22:50:58.184 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77]
2025-07-17 22:50:58.188 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@24e68d1b] will be managed by Spring
2025-07-17 22:50:58.190 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-17 22:50:58.205 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-07-17 22:50:58.226 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-17 22:50:58.227 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77]
2025-07-17 22:50:58.228 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-17 22:50:58.229 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77] from current transaction
2025-07-17 22:50:58.229 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-17 22:50:58.229 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-17 22:50:58.232 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-17 22:50:58.232 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77]
2025-07-17 22:50:58.233 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-17 22:50:58.234 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77]
2025-07-17 22:50:58.234 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77]
2025-07-17 22:50:58.234 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45f36c77]
2025-07-17 22:50:58.275 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-17 22:50:58.280 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /actuator/health] with attributes [permitAll]
2025-07-17 22:50:58.280 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured GET /actuator/health
2025-07-17 22:50:58.282 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : GET "/actuator/health", parameters={}
2025-07-17 22:50:58.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/vnd.spring-boot.actuator.v3+json;q=0.8', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-07-17 22:50:58.823 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [org.springframework.boot.actuate.health.SystemHealth@7f30a552]
2025-07-17 22:50:58.836 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-17 22:50:58.837 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-17 22:50:59.704 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /favicon.ico
2025-07-17 22:50:59.704 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-17 22:50:59.712 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.w.s.h.SimpleUrlHandlerMapping : Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-07-17 22:50:59.793 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-17 22:50:59.815 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-17 22:50:59.815 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-17 22:50:59.815 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba2292e]
2025-07-17 22:50:59.815 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1329402235 wrapping com.mysql.cj.jdbc.ConnectionImpl@24e68d1b] will be managed by Spring
2025-07-17 22:50:59.815 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-17 22:50:59.816 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-17 22:50:59.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-17 22:50:59.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba2292e]
2025-07-17 22:50:59.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-17 22:50:59.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba2292e]
2025-07-17 22:50:59.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba2292e]
2025-07-17 22:50:59.818 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ba2292e]
2025-07-17 22:50:59.820 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-17 22:50:59.821 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /favicon.ico] with attributes [permitAll]
2025-07-17 22:50:59.821 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /favicon.ico
2025-07-17 22:50:59.821 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : GET "/favicon.ico", parameters={}
2025-07-17 22:50:59.822 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.w.s.h.SimpleUrlHandlerMapping : Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-07-17 22:50:59.827 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.w.s.r.ResourceHttpRequestHandler : Resource not found
2025-07-17 22:50:59.828 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 404 NOT_FOUND
2025-07-17 22:50:59.828 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-17 22:50:59.830 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /error
2025-07-17 22:50:59.830 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-17 22:50:59.830 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-17 22:50:59.830 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured GET /error
2025-07-17 22:50:59.831 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : "ERROR" dispatch for GET "/error", parameters={}
2025-07-17 22:50:59.833 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)
2025-07-17 22:50:59.835 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-17 22:50:59.836 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [{timestamp=Thu Jul 17 22:50:59 CST 2025, status=404, error=Not Found, path=/favicon.ico}]
2025-07-17 22:50:59.851 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Exiting from "ERROR" dispatch, status 404
2025-07-17 22:50:59.851 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-17 22:51:21.105 [34mINFO [0;39m 5896 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-17 22:51:57.781 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-6] o.a.coyote.http11.Http11Processor : Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x060xd20x010x000x060xce0x030x030x1f0xe10xe60xa7j0xe10xd70xa30x9d0x1e0xee0xbbW0xcaL0xde"0xb7R0x020x1e0xa61"''90xecB0xb4a0xcb ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:407)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:263)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-17 22:51:57.781 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-5] o.a.coyote.http11.Http11Processor : Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x060xd20x010x000x060xce0x030x030x140xb70x1f={C0xd10x1eY(0xa9l0x160xa00x150xa6t]'0xa60xeeG0xff0xa10xe80x1c0xaa50xf10x050xa20xea ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:407)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:263)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-17 22:52:03.758 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing GET /api/qrlogin/create
2025-07-17 22:52:03.759 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-17 22:52:03.760 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.QRLoginController#createQRCode(CommunicationType)
2025-07-17 22:52:03.838 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-17 22:52:03.841 [34mINFO [0;39m 5896 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-17 22:52:03.841 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-17 22:52:03.841 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0c62ab]
2025-07-17 22:52:03.841 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1985989922 wrapping com.mysql.cj.jdbc.ConnectionImpl@24e68d1b] will be managed by Spring
2025-07-17 22:52:03.841 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-17 22:52:03.842 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-17 22:52:03.844 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-17 22:52:03.844 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0c62ab]
2025-07-17 22:52:03.844 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-17 22:52:03.844 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0c62ab]
2025-07-17 22:52:03.844 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0c62ab]
2025-07-17 22:52:03.844 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0c62ab]
2025-07-17 22:52:03.847 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-17 22:52:03.847 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/qrlogin/create] with attributes [permitAll]
2025-07-17 22:52:03.847 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured GET /api/qrlogin/create
2025-07-17 22:52:03.847 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : GET "/api/qrlogin/create", parameters={}
2025-07-17 22:52:03.848 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.QRLoginController#createQRCode(CommunicationType)
2025-07-17 22:52:03.975 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json;q=0.8', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-17 22:52:03.987 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=二维码创建成功, success=true, data=QRLoginDTO.QRCodeResponse(qrId=5f2a4e36-8a26-4b (truncated)...]
2025-07-17 22:52:03.990 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-17 22:52:03.990 [39mDEBUG[0;39m 5896 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-17 22:52:36.891 [39mDEBUG[0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-17 22:52:36.891 [39mDEBUG[0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-17 22:52:36.891 [34mINFO [0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-17 22:52:36.891 [34mINFO [0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a29520e]]
2025-07-17 22:52:36.891 [39mDEBUG[0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a29520e]
2025-07-17 22:52:36.891 [39mDEBUG[0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a29520e]
2025-07-17 22:52:36.891 [34mINFO [0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-17 22:52:36.891 [39mDEBUG[0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:52:36.891 [39mDEBUG[0;39m 5896 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-17 22:52:37.239 [34mINFO [0;39m 5896 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-17 22:52:37.246 [34mINFO [0;39m 5896 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
