2025-04-04 19:30:40.230 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-04 19:30:40.233 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 21220 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-04 19:30:40.240 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-04 19:30:40.241 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-04 19:30:41.096 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-04 19:30:41.097 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-04 19:30:41.120 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-04-04 19:30:41.197 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-04 19:30:41.197 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-04 19:30:41.197 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-04 19:30:41.198 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-04 19:30:41.199 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-04 19:30:41.199 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-04 19:30:41.199 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-04 19:30:41.199 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-04 19:30:41.200 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-04 19:30:41.585 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-04 19:30:41.589 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-04 19:30:41.590 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-04 19:30:41.590 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-04 19:30:41.661 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-04 19:30:41.661 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1383 ms
2025-04-04 19:30:41.841 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-04 19:30:41.852 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-04 19:30:41.861 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-04 19:30:41.870 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-04 19:30:41.969 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-04 19:30:42.093 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-04 19:30:42.424 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-04 19:30:42.427 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-04 19:30:42.433 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-04 19:30:42.434 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-04 19:30:42.434 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-04 19:30:42.434 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-04 19:30:42.435 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-04 19:30:42.435 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-04 19:30:42.435 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-04 19:30:42.435 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-04 19:30:42.435 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-04 19:30:42.435 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-04 19:30:42.511 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-04 19:30:42.512 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-04 19:30:42.515 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@74e497ae, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@13516600, org.springframework.security.web.context.SecurityContextPersistenceFilter@7c71c889, org.springframework.security.web.header.HeaderWriterFilter@7ac058a0, org.springframework.security.web.authentication.logout.LogoutFilter@d36c1c3, com.example.pure.filter.JwtFilter@32091c14, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c59a0f7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@625487a6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35d7386b, org.springframework.security.web.session.SessionManagementFilter@787d1f9c, org.springframework.security.web.access.ExceptionTranslationFilter@2616b618, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@c97721b]
2025-04-04 19:30:42.636 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**] in 'stompWebSocketHandlerMapping'
2025-04-04 19:30:42.652 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-04 19:30:42.701 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 28 mappings in 'requestMappingHandlerMapping'
2025-04-04 19:30:42.707 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-04 19:30:42.985 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-04 19:30:43.067 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-04 19:30:43.083 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-04 19:30:43.084 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-04 19:30:43.085 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-04 19:30:43.085 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-04 19:30:43.085 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-04 19:30:43.085 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@49e479da, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2d24cdd9, org.springframework.security.web.context.SecurityContextPersistenceFilter@8f2e3e6, org.springframework.security.web.header.HeaderWriterFilter@440ef8d, org.springframework.web.filter.CorsFilter@30dd00b6, org.springframework.security.web.authentication.logout.LogoutFilter@3b170235, com.example.pure.filter.JwtFilter@32091c14, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f315b08, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c8efde4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4656fcc5, org.springframework.security.web.session.SessionManagementFilter@17fbfb02, org.springframework.security.web.access.ExceptionTranslationFilter@388be5fd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6182ffef]
2025-04-04 19:30:43.123 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-04 19:30:43.123 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-04 19:30:43.124 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-04 19:30:43.126 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-04 19:30:43.126 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-04 19:30:43.126 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-04 19:30:43.127 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-04 19:30:43.127 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-04 19:30:43.127 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-04 19:30:43.159 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-04 19:30:43.181 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-04 19:30:43.398 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-04 19:30:43.515 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-04 19:30:43.517 [main] DEBUG org.springframework.web.SimpLogging - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-04 19:30:43.517 [main] DEBUG org.springframework.web.SimpLogging - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-04 19:30:43.517 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-04 19:30:43.517 [main] DEBUG org.springframework.web.SimpLogging - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@64c95480]
2025-04-04 19:30:43.517 [main] DEBUG org.springframework.web.SimpLogging - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@64c95480]
2025-04-04 19:30:43.517 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@64c95480]]
2025-04-04 19:30:43.517 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-04 19:30:43.517 [main] DEBUG org.springframework.web.SimpLogging - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-04 19:30:43.517 [main] DEBUG org.springframework.web.SimpLogging - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-04 19:30:43.527 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.743 seconds (JVM running for 4.348)
2025-04-04 19:30:51.855 [SpringApplicationShutdownHook] DEBUG org.springframework.web.SimpLogging - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-04 19:30:51.855 [SpringApplicationShutdownHook] DEBUG org.springframework.web.SimpLogging - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-04 19:30:51.855 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-04 19:30:51.856 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@64c95480]]
2025-04-04 19:30:51.856 [SpringApplicationShutdownHook] DEBUG org.springframework.web.SimpLogging - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@64c95480]
2025-04-04 19:30:51.856 [SpringApplicationShutdownHook] DEBUG org.springframework.web.SimpLogging - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@64c95480]
2025-04-04 19:30:51.856 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-04 19:30:51.856 [SpringApplicationShutdownHook] DEBUG org.springframework.web.SimpLogging - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-04 19:30:51.856 [SpringApplicationShutdownHook] DEBUG org.springframework.web.SimpLogging - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-04 19:30:52.013 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-04 19:30:52.016 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
