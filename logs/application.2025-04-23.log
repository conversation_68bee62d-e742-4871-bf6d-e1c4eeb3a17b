2025-04-23 00:01:18.456 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
2025-04-23 00:31:18.471 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-<PERSON>tt<PERSON><PERSON><PERSON>(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 12]
2025-04-23 01:01:18.472 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 13]
2025-04-23 01:31:18.475 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 14]
2025-04-23 02:01:18.485 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 15]
2025-04-23 02:18:25.558 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21a5b599]]
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21a5b599]
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21a5b599]
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 02:18:25.559 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 02:18:25.954 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-23 02:18:25.968 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-23 13:37:42.465 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-23 13:37:42.468 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 13372 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-23 13:37:42.469 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-23 13:37:42.469 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-23 13:37:43.556 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-23 13:37:43.559 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-23 13:37:43.592 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-04-23 13:37:43.712 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\AccessLogMapper.class]
2025-04-23 13:37:43.713 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\RoleMapper.class]
2025-04-23 13:37:43.713 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\UserMapper.class]
2025-04-23 13:37:43.713 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\UserRoleMapper.class]
2025-04-23 13:37:43.713 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-23 13:37:43.715 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-23 13:37:43.715 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-23 13:37:43.715 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-23 13:37:43.715 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-23 13:37:43.716 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-23 13:37:43.716 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-04-23 13:37:43.716 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-04-23 13:37:44.243 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-23 13:37:44.249 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-23 13:37:44.250 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-23 13:37:44.250 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-23 13:37:44.338 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-23 13:37:44.339 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1816 ms
2025-04-23 13:37:44.560 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-23 13:37:44.571 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-23 13:37:44.585 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-23 13:37:44.590 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserRoleMapper.xml]'
2025-04-23 13:37:44.601 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-23 13:37:44.717 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-23 13:37:45.090 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-23 13:37:45.255 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)
2025-04-23 13:37:45.258 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-23 13:37:45.265 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.266 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-23 13:37:45.266 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.266 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-23 13:37:45.266 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 13:37:45.353 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-23 13:37:45.355 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-23 13:37:45.359 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@4662752a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@693f2213, org.springframework.security.web.context.SecurityContextPersistenceFilter@5e2a6991, org.springframework.security.web.header.HeaderWriterFilter@23ad2d17, org.springframework.security.web.authentication.logout.LogoutFilter@4a1dda83, com.example.pure.filter.JwtFilter@67acfde9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f96dd64, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@409732fb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@514377fc, org.springframework.security.web.session.SessionManagementFilter@20a3e10c, org.springframework.security.web.access.ExceptionTranslationFilter@207dd1b7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@15369d73]
2025-04-23 13:37:45.361 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-23 13:37:45.361 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-23 13:37:45.504 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-23 13:37:45.520 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-23 13:37:45.568 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 49 mappings in 'requestMappingHandlerMapping'
2025-04-23 13:37:45.575 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-23 13:37:45.890 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-23 13:37:45.976 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-23 13:37:45.995 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-23 13:37:45.995 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-23 13:37:45.995 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-23 13:37:45.996 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-23 13:37:45.997 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-23 13:37:45.997 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f1f9515, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c8ab9de, org.springframework.security.web.context.SecurityContextPersistenceFilter@eaf8427, org.springframework.security.web.header.HeaderWriterFilter@24a04257, org.springframework.web.filter.CorsFilter@57416e49, org.springframework.security.web.authentication.logout.LogoutFilter@13e00016, com.example.pure.filter.JwtFilter@67acfde9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4bf4680c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6d6039df, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2e7bb00e, org.springframework.security.web.session.SessionManagementFilter@6615237, org.springframework.security.web.access.ExceptionTranslationFilter@55421b8d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@bfe47a8]
2025-04-23 13:37:46.028 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6cc4cdb9, started on Wed Apr 23 13:37:42 CST 2025
2025-04-23 13:37:46.039 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-23 13:37:46.039 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-23 13:37:46.039 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-23 13:37:46.039 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-23 13:37:46.040 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-23 13:37:46.040 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-23 13:37:46.042 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-23 13:37:46.043 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-23 13:37:46.043 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-23 13:37:46.043 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
2025-04-23 13:37:46.044 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-23 13:37:46.044 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-23 13:37:46.044 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-23 13:37:46.045 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-23 13:37:46.080 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-23 13:37:46.105 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-23 13:37:46.295 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-23 13:37:46.421 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-23 13:37:46.423 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 13:37:46.423 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 13:37:46.423 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-23 13:37:46.423 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-23 13:37:46.423 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-23 13:37:46.423 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]]
2025-04-23 13:37:46.423 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-23 13:37:46.423 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 13:37:46.423 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 13:37:46.434 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 4.572 seconds (JVM running for 5.687)
2025-04-23 13:38:25.335 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-23 13:38:25.335 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-23 13:38:25.335 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-23 13:38:25.335 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-23 13:38:25.335 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-23 13:38:25.336 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@403e6097
2025-04-23 13:38:25.336 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@4303f90c
2025-04-23 13:38:25.336 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-23 13:38:25.336 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-23 13:38:25.345 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users
2025-04-23 13:38:25.347 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-23 13:38:25.355 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getAllUsers()
2025-04-23 13:38:25.356 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-23 13:38:25.361 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Failed to authorize filter invocation [GET /api/users] with attributes [authenticated]
2025-04-23 13:38:25.362 [http-nio-8080-exec-1] WARN  c.e.d.h.CustomAuthenticationEntryPoint - 认证失败: /api/users, URI: Full authentication is required to access this resource
2025-04-23 13:38:25.396 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-23 13:38:46.018 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-23 13:40:23.032 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-23 13:40:23.032 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-23 13:40:23.033 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-23 13:40:23.033 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-23 13:40:23.033 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-23 13:40:23.034 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-23 13:40:23.036 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-23 13:40:23.037 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-23 13:40:23.069 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=23adfa126662, password=12345678, email=null)]
2025-04-23 13:40:25.629 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-04-23 13:40:25.631 [http-nio-8080-exec-3] ERROR c.e.d.e.GlobalExceptionHandler - 系统异常
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1093)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:421)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.example.pure.util.IpUtil.getClientIp(IpUtil.java:42)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:108)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d3ef747e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$394edbd1.login(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 123 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-04-23 13:40:25.637 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-23 13:40:25.639 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [Result(code=500, message=系统异常，请联系管理员, data=null)]
2025-04-23 13:40:25.648 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379]
2025-04-23 13:40:25.648 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 500 INTERNAL_SERVER_ERROR
2025-04-23 13:40:25.649 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-23 13:41:27.177 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-23 13:41:27.177 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-23 13:41:27.178 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-23 13:41:27.178 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-23 13:41:27.178 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-23 13:41:27.178 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-23 13:41:27.178 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-23 13:41:27.179 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-23 13:41:27.179 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=23adfa126662, password=12345678, email=null)]
2025-04-23 13:41:27.253 [http-nio-8080-exec-7] INFO  c.e.demo13.controller.AuthController - 用户登录: 23adfa126662
2025-04-23 13:41:27.253 [http-nio-8080-exec-7] DEBUG c.e.d.service.impl.AuthServiceImpl - 处理用户登录请求: 23adfa126662
2025-04-23 13:41:27.326 [http-nio-8080-exec-7] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-23 13:41:27.338 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-23 13:41:27.341 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31]
2025-04-23 13:41:27.347 [http-nio-8080-exec-7] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@f199301] will be managed by Spring
2025-04-23 13:41:27.349 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-23 13:41:27.367 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.UserMapper.findByUsername - ==> Parameters: 23adfa126662(String)
2025-04-23 13:41:27.387 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.UserMapper.findByUsername - <==      Total: 1
2025-04-23 13:41:27.388 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31]
2025-04-23 13:41:27.390 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31] from current transaction
2025-04-23 13:41:27.390 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-23 13:41:27.391 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-23 13:41:27.392 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.RoleMapper.findByUserId - <==      Total: 1
2025-04-23 13:41:27.393 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31]
2025-04-23 13:41:27.393 [http-nio-8080-exec-7] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-23 13:41:27.393 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31]
2025-04-23 13:41:27.394 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31]
2025-04-23 13:41:27.394 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e762d31]
2025-04-23 13:41:27.654 [http-nio-8080-exec-7] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-04-23 13:41:27.720 [http-nio-8080-exec-7] INFO  c.e.d.service.impl.DeviceServiceImpl - 用户 23adfa126662 设备数量已达上限，移除最早登录的设备: eee7e57c-f1c2-4aa6-9d5d-dc1566e4b9e8
2025-04-23 13:41:27.725 [http-nio-8080-exec-7] INFO  c.e.d.service.impl.DeviceServiceImpl - 通过token移除用户 23adfa126662 设备成功
2025-04-23 13:41:27.729 [http-nio-8080-exec-7] INFO  c.e.d.service.impl.DeviceServiceImpl - 用户 23adfa126662 添加设备 923c9378-2957-4ab7-bd04-2f7c5f154b1d 成功，使用token作为标识
2025-04-23 13:41:27.729 [http-nio-8080-exec-7] INFO  c.e.d.service.impl.AuthServiceImpl - 用户 23adfa126662 登录设备超过限制，已移除最早登录的设备: eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************.HDaHBwVXROJ3s4Ctax1-wverg-6Hul2wG_rXcU9HCeM
2025-04-23 13:41:27.736 [http-nio-8080-exec-7] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-23 13:41:27.742 [http-nio-8080-exec-7] DEBUG c.e.d.service.impl.UserServiceImpl - 更新用户基本信息: 23adfa126662
2025-04-23 13:41:27.742 [http-nio-8080-exec-7] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-23 13:41:27.744 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-23 13:41:27.744 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62e919ef]
2025-04-23 13:41:27.756 [http-nio-8080-exec-7] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@******** wrapping com.mysql.cj.jdbc.ConnectionImpl@f199301] will be managed by Spring
2025-04-23 13:41:27.756 [http-nio-8080-exec-7] DEBUG c.e.d.m.primary.UserMapper.update - ==>  Preparing: UPDATE user SET username = ?, password = ?, email = ?, nickname = ?, avatar = ?, gender = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, update_time = NOW() WHERE id = ?
2025-04-23 13:41:27.757 [http-nio-8080-exec-7] DEBUG c.e.d.m.primary.UserMapper.update - ==> Parameters: 23adfa126662(String), $2a$12$zZJ.NzKo/56aVFdL5CCu7up/dX2v.BH0sr3KHGCIake0V/UYbH2oS(String), <EMAIL>(String), nihaohao(String), a(String), 1(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-04-23T13:41:27.741(LocalDateTime), eyJhbGciOiJIUzI1NiJ9.***************************************************************************.3cT4k3p2B9sqGJ0t4MosfYCwXg6j2tBw_52R9Vd-u_U(String), 1(Long)
2025-04-23 13:41:27.760 [http-nio-8080-exec-7] DEBUG c.e.d.m.primary.UserMapper.update - <==    Updates: 1
2025-04-23 13:41:27.760 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62e919ef]
2025-04-23 13:41:27.761 [http-nio-8080-exec-7] DEBUG c.e.d.service.impl.UserServiceImpl - 用户基本信息更新成功: 23adfa126662
2025-04-23 13:41:27.761 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62e919ef]
2025-04-23 13:41:27.761 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62e919ef]
2025-04-23 13:41:27.761 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62e919ef]
2025-04-23 13:41:27.766 [http-nio-8080-exec-7] INFO  c.e.d.service.impl.AuthServiceImpl - 用户登录成功: 23adfa126662
2025-04-23 13:41:27.766 [http-nio-8080-exec-7] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-23 13:41:27.771 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-23 13:41:27.771 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.771 [http-nio-8080-exec-7] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@756528042 wrapping com.mysql.cj.jdbc.ConnectionImpl@f199301] will be managed by Spring
2025-04-23 13:41:27.771 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-23 13:41:27.772 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), LOGIN(String), 2025-04-23(String)
2025-04-23 13:41:27.773 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-23 13:41:27.773 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.774 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a] from current transaction
2025-04-23 13:41:27.774 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-23 13:41:27.774 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), LOGIN(String), 2025-04-22(String)
2025-04-23 13:41:27.775 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.A.findByUserIdAndTypeAndDate - <==      Total: 1
2025-04-23 13:41:27.775 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.775 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a] from current transaction
2025-04-23 13:41:27.775 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.AccessLogMapper.insert - ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, create_time, update_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-04-23 13:41:27.775 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.AccessLogMapper.insert - ==> Parameters: 1(Long), LOGIN(String), 2(Integer), 2025-04-23(LocalDate), 2025-04-23T13:41:27.775(LocalDateTime), 2025-04-23T13:41:27.775(LocalDateTime), 127.0.0.1(String)
2025-04-23 13:41:27.776 [http-nio-8080-exec-7] DEBUG c.e.d.m.p.AccessLogMapper.insert - <==    Updates: 1
2025-04-23 13:41:27.776 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.776 [http-nio-8080-exec-7] INFO  c.e.d.s.impl.AccessLogServiceImpl - Created new access log for user: 1, type: LOGIN, count: 2
2025-04-23 13:41:27.776 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.776 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.776 [http-nio-8080-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c0e2c6a]
2025-04-23 13:41:27.788 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-23 13:41:27.788 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=登录成功, data=TokenResponse(accessToken=Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdXRob3 (truncated)...]
2025-04-23 13:41:27.789 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-23 13:41:27.789 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-23 13:42:10.511 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users
2025-04-23 13:42:10.511 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-23 13:42:10.512 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getAllUsers()
2025-04-23 13:42:10.519 [http-nio-8080-exec-10] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-23 13:42:10.523 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-23 13:42:10.524 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27644c36]
2025-04-23 13:42:10.524 [http-nio-8080-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@154774031 wrapping com.mysql.cj.jdbc.ConnectionImpl@f199301] will be managed by Spring
2025-04-23 13:42:10.524 [http-nio-8080-exec-10] DEBUG c.e.d.m.p.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-23 13:42:10.524 [http-nio-8080-exec-10] DEBUG c.e.d.m.p.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-23 13:42:10.525 [http-nio-8080-exec-10] DEBUG c.e.d.m.p.RoleMapper.findByUserId - <==      Total: 1
2025-04-23 13:42:10.525 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27644c36]
2025-04-23 13:42:10.525 [http-nio-8080-exec-10] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-23 13:42:10.525 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27644c36]
2025-04-23 13:42:10.525 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27644c36]
2025-04-23 13:42:10.526 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27644c36]
2025-04-23 13:42:10.526 [http-nio-8080-exec-10] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-23 13:42:10.526 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users] with attributes [authenticated]
2025-04-23 13:42:10.526 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users
2025-04-23 13:42:10.527 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users", parameters={}
2025-04-23 13:42:10.527 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getAllUsers()
2025-04-23 13:42:10.533 [http-nio-8080-exec-10] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers(); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 13:42:10.544 [http-nio-8080-exec-10] DEBUG c.e.demo13.controller.UserController - 获取所有用户
2025-04-23 13:42:10.545 [http-nio-8080-exec-10] DEBUG c.e.d.service.impl.UserServiceImpl - 查找所有用户
2025-04-23 13:42:10.545 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-23 13:42:10.545 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14992432]
2025-04-23 13:42:10.546 [http-nio-8080-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1470606546 wrapping com.mysql.cj.jdbc.ConnectionImpl@f199301] will be managed by Spring
2025-04-23 13:42:10.546 [http-nio-8080-exec-10] DEBUG c.e.d.m.primary.UserMapper.findAll - ==>  Preparing: SELECT id, username, email, nickname, avatar, gender, create_time, last_login_time, score FROM user
2025-04-23 13:42:10.546 [http-nio-8080-exec-10] DEBUG c.e.d.m.primary.UserMapper.findAll - ==> Parameters:
2025-04-23 13:42:10.549 [http-nio-8080-exec-10] DEBUG c.e.d.m.primary.UserMapper.findAll - <==      Total: 11
2025-04-23 13:42:10.549 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14992432]
2025-04-23 13:42:10.549 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14992432]
2025-04-23 13:42:10.549 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14992432]
2025-04-23 13:42:10.549 [http-nio-8080-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14992432]
2025-04-23 13:42:10.551 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-23 13:42:10.551 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=获取成功, data=[UserDTO(id=1, username=23adfa126662, email=<EMAIL>, nic (truncated)...]
2025-04-23 13:42:10.557 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-23 13:42:10.557 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-23 13:43:25.088 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/me?password=&enabled=&username=&authorities=&accountNonExpired=&accountNonLocked=&credentialsNonExpired=
2025-04-23 13:43:25.089 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-23 13:43:25.089 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getCurrentUser(UserDetails)
2025-04-23 13:43:25.096 [http-nio-8080-exec-5] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-23 13:43:25.098 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-23 13:43:25.099 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@259915f6]
2025-04-23 13:43:25.099 [http-nio-8080-exec-5] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@f199301] will be managed by Spring
2025-04-23 13:43:25.099 [http-nio-8080-exec-5] DEBUG c.e.d.m.p.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-23 13:43:25.099 [http-nio-8080-exec-5] DEBUG c.e.d.m.p.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG c.e.d.m.p.RoleMapper.findByUserId - <==      Total: 1
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@259915f6]
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@259915f6]
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@259915f6]
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@259915f6]
2025-04-23 13:43:25.100 [http-nio-8080-exec-5] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-23 13:43:25.101 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users/me?password=&enabled=&username=&authorities=&accountNonExpired=&accountNonLocked=&credentialsNonExpired=] with attributes [authenticated]
2025-04-23 13:43:25.101 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users/me?password=&enabled=&username=&authorities=&accountNonExpired=&accountNonLocked=&credentialsNonExpired=
2025-04-23 13:43:25.101 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users/me?password=&enabled=&username=&authorities=&accountNonExpired=&accountNonLocked=&credentialsNonExpired=", parameters={masked}
2025-04-23 13:43:25.102 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getCurrentUser(UserDetails)
2025-04-23 13:43:25.103 [http-nio-8080-exec-5] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-23 13:43:25.107 [http-nio-8080-exec-5] DEBUG c.e.demo13.controller.UserController - 获取当前用户信息: 23adfa126662
2025-04-23 13:43:25.108 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 获取用户DTO, username: 23adfa126662
2025-04-23 13:43:25.108 [http-nio-8080-exec-5] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-23 13:43:25.111 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-23 13:43:25.111 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=获取成功, data=UserDTO(id=1, username=23adfa126662, email=<EMAIL>, nick (truncated)...]
2025-04-23 13:43:25.112 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-23 13:43:25.112 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-23 14:08:46.022 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-04-23 14:38:46.030 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-04-23 15:08:46.036 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-04-23 15:18:54.355 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]]
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69d2fb0a]
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 15:18:54.356 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 15:18:54.699 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-23 15:18:54.706 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-23 18:43:34.239 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-23 18:43:34.246 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 19980 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-23 18:43:34.246 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-23 18:43:34.246 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-23 18:43:35.104 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-23 18:43:35.106 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-23 18:43:35.132 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-04-23 18:43:35.220 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\AccessLogMapper.class]
2025-04-23 18:43:35.220 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\RoleMapper.class]
2025-04-23 18:43:35.220 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\UserMapper.class]
2025-04-23 18:43:35.220 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\UserRoleMapper.class]
2025-04-23 18:43:35.221 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-23 18:43:35.222 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-23 18:43:35.222 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-23 18:43:35.222 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-23 18:43:35.222 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-23 18:43:35.223 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-23 18:43:35.223 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-04-23 18:43:35.223 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-04-23 18:43:35.622 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-23 18:43:35.627 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-23 18:43:35.628 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-23 18:43:35.628 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-23 18:43:35.704 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-23 18:43:35.704 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1422 ms
2025-04-23 18:43:35.894 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-23 18:43:35.905 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-23 18:43:35.919 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-23 18:43:35.924 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserRoleMapper.xml]'
2025-04-23 18:43:35.935 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-23 18:43:36.039 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-23 18:43:36.413 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-23 18:43:36.565 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)
2025-04-23 18:43:36.568 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-23 18:43:36.576 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.577 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-23 18:43:36.577 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.577 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-23 18:43:36.577 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.577 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-23 18:43:36.577 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 18:43:36.674 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-23 18:43:36.677 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-23 18:43:36.682 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@78b0ec3a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46612bfc, org.springframework.security.web.context.SecurityContextPersistenceFilter@5882b202, org.springframework.security.web.header.HeaderWriterFilter@130a6eb9, org.springframework.security.web.authentication.logout.LogoutFilter@1c74d19, com.example.pure.filter.JwtFilter@76216830, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b506ed0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65f3e805, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f213a2, org.springframework.security.web.session.SessionManagementFilter@6bce4140, org.springframework.security.web.access.ExceptionTranslationFilter@5b742bc8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b441e56]
2025-04-23 18:43:36.684 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-23 18:43:36.685 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-23 18:43:36.841 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-23 18:43:36.855 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-23 18:43:36.899 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 49 mappings in 'requestMappingHandlerMapping'
2025-04-23 18:43:36.906 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-23 18:43:37.197 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-23 18:43:37.281 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-23 18:43:37.300 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-23 18:43:37.300 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-23 18:43:37.301 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-23 18:43:37.302 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-23 18:43:37.302 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@68b7bdcb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d84418a, org.springframework.security.web.context.SecurityContextPersistenceFilter@66020d69, org.springframework.security.web.header.HeaderWriterFilter@7345f97d, org.springframework.web.filter.CorsFilter@45e11627, org.springframework.security.web.authentication.logout.LogoutFilter@63124022, com.example.pure.filter.JwtFilter@76216830, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3751acd7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b832551, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@ec5f944, org.springframework.security.web.session.SessionManagementFilter@554566a8, org.springframework.security.web.access.ExceptionTranslationFilter@14b8a751, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@266da047]
2025-04-23 18:43:37.330 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7f2cfe3f, started on Wed Apr 23 18:43:34 CST 2025
2025-04-23 18:43:37.339 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-23 18:43:37.340 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-23 18:43:37.340 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-23 18:43:37.340 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-23 18:43:37.340 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-23 18:43:37.340 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-23 18:43:37.342 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-23 18:43:37.343 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-23 18:43:37.343 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-23 18:43:37.343 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-23 18:43:37.343 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-23 18:43:37.344 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-23 18:43:37.344 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-23 18:43:37.344 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-23 18:43:37.375 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-23 18:43:37.397 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-23 18:43:37.594 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-23 18:43:37.718 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-23 18:43:37.721 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 18:43:37.721 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 18:43:37.721 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-23 18:43:37.721 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@120411ec]
2025-04-23 18:43:37.721 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@120411ec]
2025-04-23 18:43:37.722 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@120411ec]]
2025-04-23 18:43:37.722 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-23 18:43:37.722 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 18:43:37.722 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 18:43:37.733 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.879 seconds (JVM running for 4.505)
2025-04-23 18:44:29.000 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-23 18:44:29.000 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-23 18:44:29.000 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-23 18:44:29.000 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-23 18:44:29.000 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-23 18:44:29.001 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2b2b2f58
2025-04-23 18:44:29.002 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@422901fe
2025-04-23 18:44:29.002 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-23 18:44:29.002 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-23 18:44:29.010 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/1
2025-04-23 18:44:29.012 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-23 18:44:29.019 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-23 18:44:29.022 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-23 18:44:29.022 [http-nio-8080-exec-2] DEBUG o.s.s.w.s.SessionManagementFilter - Request requested invalid session id 51055D724AF1E61DD3553AF182716DB8
2025-04-23 18:44:29.026 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Failed to authorize filter invocation [GET /api/users/1] with attributes [authenticated]
2025-04-23 18:44:29.027 [http-nio-8080-exec-2] WARN  c.e.d.h.CustomAuthenticationEntryPoint - 认证失败: /api/users/1, URI: Full authentication is required to access this resource
2025-04-23 18:44:29.060 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-23 18:44:37.331 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@120411ec]]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@120411ec]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@120411ec]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 18:53:32.148 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 18:53:32.376 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-23 18:53:32.380 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-23 22:05:28.858 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-23 22:05:28.859 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 25824 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-23 22:05:28.860 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-23 22:05:28.860 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-23 22:05:29.630 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-23 22:05:29.631 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-23 22:05:29.657 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-04-23 22:05:29.732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\AccessLogMapper.class]
2025-04-23 22:05:29.732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\RoleMapper.class]
2025-04-23 22:05:29.732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\UserMapper.class]
2025-04-23 22:05:29.732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\primary\UserRoleMapper.class]
2025-04-23 22:05:29.733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-23 22:05:29.734 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-23 22:05:29.734 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-23 22:05:29.734 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-23 22:05:29.734 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-23 22:05:29.734 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-23 22:05:29.734 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-04-23 22:05:29.735 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-04-23 22:05:30.113 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-23 22:05:30.118 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-23 22:05:30.119 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-23 22:05:30.119 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-23 22:05:30.200 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-23 22:05:30.200 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1305 ms
2025-04-23 22:05:30.385 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-23 22:05:30.395 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-23 22:05:30.407 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-23 22:05:30.411 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserRoleMapper.xml]'
2025-04-23 22:05:30.420 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-23 22:05:30.514 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-23 22:05:30.848 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-23 22:05:30.990 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-23 22:05:30.993 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.000 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-23 22:05:31.001 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.002 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)
2025-04-23 22:05:31.002 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-23 22:05:31.076 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-23 22:05:31.077 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-23 22:05:31.081 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@27ffd9f8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@642c6461, org.springframework.security.web.context.SecurityContextPersistenceFilter@4466cf5d, org.springframework.security.web.header.HeaderWriterFilter@426c0486, org.springframework.security.web.authentication.logout.LogoutFilter@10e9a5fe, com.example.pure.filter.JwtFilter@257f30f7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2450256f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b7facc7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3ace6346, org.springframework.security.web.session.SessionManagementFilter@720f56e2, org.springframework.security.web.access.ExceptionTranslationFilter@5aea8994, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@799f354a]
2025-04-23 22:05:31.082 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-23 22:05:31.083 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-23 22:05:31.218 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-23 22:05:31.233 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-23 22:05:31.279 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 49 mappings in 'requestMappingHandlerMapping'
2025-04-23 22:05:31.286 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-23 22:05:31.572 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-23 22:05:31.656 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-23 22:05:31.675 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-23 22:05:31.676 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-23 22:05:31.677 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b665a30, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@26a45089, org.springframework.security.web.context.SecurityContextPersistenceFilter@72b2c5ed, org.springframework.security.web.header.HeaderWriterFilter@7b5ac347, org.springframework.web.filter.CorsFilter@75ed7512, org.springframework.security.web.authentication.logout.LogoutFilter@1600a8a2, com.example.pure.filter.JwtFilter@257f30f7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@41f785e3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c9a3661, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ab5924c, org.springframework.security.web.session.SessionManagementFilter@57b9389f, org.springframework.security.web.access.ExceptionTranslationFilter@1ab53860, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@844e66d]
2025-04-23 22:05:31.708 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7f2cfe3f, started on Wed Apr 23 22:05:28 CST 2025
2025-04-23 22:05:31.718 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-23 22:05:31.718 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-23 22:05:31.718 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-23 22:05:31.718 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-23 22:05:31.718 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-23 22:05:31.718 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-23 22:05:31.720 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-23 22:05:31.721 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-23 22:05:31.721 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-23 22:05:31.721 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-23 22:05:31.722 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-23 22:05:31.722 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-23 22:05:31.722 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-23 22:05:31.722 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-23 22:05:31.755 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-23 22:05:31.780 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-23 22:05:31.961 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-23 22:05:32.077 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-23 22:05:32.078 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 22:05:32.079 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 22:05:32.079 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-23 22:05:32.079 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@ca2cd5e]
2025-04-23 22:05:32.079 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@ca2cd5e]
2025-04-23 22:05:32.079 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@ca2cd5e]]
2025-04-23 22:05:32.079 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-23 22:05:32.079 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 22:05:32.079 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 22:05:32.090 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.605 seconds (JVM running for 4.234)
2025-04-23 22:06:31.707 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-23 22:36:31.717 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-04-23 22:52:57.660 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@ca2cd5e]]
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@ca2cd5e]
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@ca2cd5e]
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 22:52:57.661 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-23 22:52:57.886 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-23 22:52:57.890 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
