2025-07-11 00:07:46.670 [34mINFO [0;39m 15984 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-Http<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-07-11 00:37:46.677 [34mINFO [0;39m 15984 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-<PERSON>ttp<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-07-11 01:07:46.686 [34mINFO [0;39m 15984 --- [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-07-11 01:37:46.695 [34mINFO [0;39m 15984 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-07-11 02:07:46.695 [34mINFO [0;39m 15984 --- [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-07-11 02:37:46.705 [34mINFO [0;39m 15984 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
2025-07-11 03:07:46.709 [34mINFO [0;39m 15984 --- [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
2025-07-11 03:37:46.720 [34mINFO [0;39m 15984 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
2025-07-11 03:49:32.827 [39mDEBUG[0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-11 03:49:32.827 [39mDEBUG[0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-11 03:49:32.827 [34mINFO [0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-11 03:49:32.828 [34mINFO [0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@53a3ccc]]
2025-07-11 03:49:32.828 [39mDEBUG[0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@53a3ccc]
2025-07-11 03:49:32.828 [39mDEBUG[0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@53a3ccc]
2025-07-11 03:49:32.828 [34mINFO [0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-11 03:49:32.828 [39mDEBUG[0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 03:49:32.828 [39mDEBUG[0;39m 15984 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 03:49:33.353 [34mINFO [0;39m 15984 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-11 03:49:33.363 [34mINFO [0;39m 15984 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-11 03:59:38.159 [34mINFO [0;39m 20740 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 20740 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-11 03:59:38.159 [34mINFO [0;39m 20740 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 03:59:38.161 [39mDEBUG[0;39m 20740 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-11 03:59:38.161 [34mINFO [0;39m 20740 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-11 03:59:39.441 [34mINFO [0;39m 20740 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-11 03:59:39.443 [34mINFO [0;39m 20740 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-11 03:59:39.487 [34mINFO [0;39m 20740 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-11 03:59:39.586 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-11 03:59:39.586 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-11 03:59:39.586 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-11 03:59:39.586 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-11 03:59:39.586 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-11 03:59:39.587 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-11 03:59:39.588 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-11 03:59:39.589 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-11 03:59:39.590 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-11 03:59:39.590 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-11 03:59:39.590 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-11 03:59:39.590 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-11 03:59:39.590 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-11 03:59:39.590 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-11 03:59:39.591 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-11 03:59:39.591 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-11 03:59:39.591 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-11 03:59:39.591 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-11 03:59:39.591 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-11 03:59:39.591 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-11 03:59:39.592 [39mDEBUG[0;39m 20740 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-11 03:59:40.091 [34mINFO [0;39m 20740 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-11 03:59:40.097 [34mINFO [0;39m 20740 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 03:59:40.098 [34mINFO [0;39m 20740 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-11 03:59:40.098 [34mINFO [0;39m 20740 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-11 03:59:40.202 [34mINFO [0;39m 20740 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-11 03:59:40.203 [34mINFO [0;39m 20740 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2008 ms
2025-07-11 03:59:40.559 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-11 03:59:40.570 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-11 03:59:40.576 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-11 03:59:40.582 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-11 03:59:40.595 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-11 03:59:40.600 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-11 03:59:40.606 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-11 03:59:40.611 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-11 03:59:40.617 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-11 03:59:40.625 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-11 03:59:40.636 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-11 03:59:40.642 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-11 03:59:40.647 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-11 03:59:40.652 [39mDEBUG[0;39m 20740 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-11 03:59:40.663 [34mINFO [0;39m 20740 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-11 03:59:41.751 [1;31mERROR[0;39m 20740 --- [main] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:332)
	at org.springframework.boot.jdbc.EmbeddedDatabaseConnection.isEmbedded(EmbeddedDatabaseConnection.java:164)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.isEmbeddedDatabase(DataSourceScriptDatabaseInitializer.java:70)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.isEnabled(AbstractScriptDatabaseInitializer.java:83)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:106)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.pure.PureApplication.main(PureApplication.java:37)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 132 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 135 common frames omitted
2025-07-11 03:59:42.434 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-11 03:59:42.434 [39mDEBUG[0;39m 20740 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-11 03:59:42.742 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-11 03:59:42.745 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.289 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-11 03:59:43.417 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-11 03:59:43.425 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-11 03:59:43.486 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-11 03:59:43.487 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.488 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-11 03:59:43.488 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.488 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-11 03:59:43.489 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.489 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-11 03:59:43.489 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.489 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-11 03:59:43.489 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.490 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-11 03:59:43.490 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.493 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-11 03:59:43.493 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.494 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-11 03:59:43.494 [39mDEBUG[0;39m 20740 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 03:59:43.605 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-11 03:59:43.612 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-11 03:59:43.620 [34mINFO [0;39m 20740 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@6c4e11d0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@29088d3d, org.springframework.security.web.context.SecurityContextPersistenceFilter@26a004ed, org.springframework.security.web.header.HeaderWriterFilter@6c0bf8f4, org.springframework.security.web.authentication.logout.LogoutFilter@7bc3c59f, com.example.pure.filter.JwtFilter@5de243bb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@75b6bd41, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@21457b14, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3284de45, org.springframework.security.web.session.SessionManagementFilter@1013aa94, org.springframework.security.web.access.ExceptionTranslationFilter@1ef7e4c7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6600d07d]
2025-07-11 03:59:43.623 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-11 03:59:43.626 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-11 03:59:43.627 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-11 03:59:43.628 [34mINFO [0;39m 20740 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-11 03:59:43.823 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-11 03:59:43.847 [34mINFO [0;39m 20740 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-11 03:59:43.923 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-11 03:59:43.932 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-11 03:59:44.354 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-11 03:59:44.519 [34mINFO [0;39m 20740 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-11 03:59:44.544 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-11 03:59:44.544 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-11 03:59:44.544 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-11 03:59:44.544 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-11 03:59:44.544 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-11 03:59:44.545 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-11 03:59:44.546 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-11 03:59:44.547 [39mDEBUG[0;39m 20740 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-11 03:59:44.548 [34mINFO [0;39m 20740 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7859fc7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@519e14f6, org.springframework.security.web.context.SecurityContextPersistenceFilter@4fb42efa, org.springframework.security.web.header.HeaderWriterFilter@2b69ff13, org.springframework.web.filter.CorsFilter@10ab976b, org.springframework.security.web.authentication.logout.LogoutFilter@7b9f7087, com.example.pure.filter.JwtFilter@5de243bb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35599228, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@51ac72f7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5da799, org.springframework.security.web.session.SessionManagementFilter@7136ad9a, org.springframework.security.web.access.ExceptionTranslationFilter@46878216, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2e07dc23]
2025-07-11 03:59:44.607 [39mTRACE[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5aa360ea, started on Fri Jul 11 03:59:38 CST 2025
2025-07-11 03:59:44.628 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-11 03:59:44.629 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-11 03:59:44.629 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-11 03:59:44.629 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-11 03:59:44.629 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-11 03:59:44.630 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-11 03:59:44.631 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-11 03:59:44.631 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-11 03:59:44.631 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-11 03:59:44.635 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
2025-07-11 03:59:44.638 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-11 03:59:44.638 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-11 03:59:44.639 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-11 03:59:44.639 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-11 03:59:44.753 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-11 03:59:44.790 [39mDEBUG[0;39m 20740 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-11 03:59:45.062 [34mINFO [0;39m 20740 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 03:59:45.072 [34mINFO [0;39m 20740 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-11 03:59:45.074 [39mDEBUG[0;39m 20740 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-11 03:59:45.074 [39mDEBUG[0;39m 20740 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-11 03:59:45.074 [34mINFO [0;39m 20740 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-11 03:59:45.074 [39mDEBUG[0;39m 20740 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2456716b]
2025-07-11 03:59:45.074 [39mDEBUG[0;39m 20740 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2456716b]
2025-07-11 03:59:45.074 [34mINFO [0;39m 20740 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2456716b]]
2025-07-11 03:59:45.075 [34mINFO [0;39m 20740 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-11 03:59:45.075 [39mDEBUG[0;39m 20740 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 03:59:45.075 [39mDEBUG[0;39m 20740 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 03:59:45.089 [34mINFO [0;39m 20740 --- [main] com.example.pure.PureApplication : Started PureApplication in 7.537 seconds (JVM running for 9.057)
2025-07-11 03:59:47.524 [34mINFO [0;39m 20740 --- [http-nio-8080-exec-3] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 03:59:47.525 [34mINFO [0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-11 03:59:47.525 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-11 03:59:47.525 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-11 03:59:47.525 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-11 03:59:47.527 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@654eaf96
2025-07-11 03:59:47.527 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@233f1144
2025-07-11 03:59:47.527 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-11 03:59:47.527 [34mINFO [0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-07-11 03:59:47.542 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 03:59:47.545 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 03:59:47.554 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 03:59:47.557 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 03:59:47.563 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai-chat/stream] with attributes [permitAll]
2025-07-11 03:59:47.563 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 03:59:47.566 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/api/ai-chat/stream", parameters={}
2025-07-11 03:59:47.568 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 03:59:47.647 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [AiChatRequest(message=你好, sessionId=null, messageType=chat)]
2025-07-11 03:59:47.728 [34mINFO [0;39m 20740 --- [http-nio-8080-exec-3] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: 你好, 会话ID: null
2025-07-11 03:59:47.733 [34mINFO [0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752177587729_fbc92481, 消息: 你好
2025-07-11 03:59:47.755 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送start控制事件 - 会话ID: session_1752177587729_fbc92481
2025-07-11 03:59:47.755 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 1
2025-07-11 03:59:47.842 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 2
2025-07-11 03:59:47.908 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 3
2025-07-11 03:59:47.974 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 4
2025-07-11 03:59:48.056 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 5
2025-07-11 03:59:48.189 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 6
2025-07-11 03:59:48.285 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 7
2025-07-11 03:59:48.426 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 8
2025-07-11 03:59:48.521 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 9
2025-07-11 03:59:48.645 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 10
2025-07-11 03:59:48.740 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 11
2025-07-11 03:59:48.867 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 12
2025-07-11 03:59:48.988 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 13
2025-07-11 03:59:49.130 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 14
2025-07-11 03:59:49.244 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 15
2025-07-11 03:59:49.318 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 16
2025-07-11 03:59:49.466 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 17
2025-07-11 03:59:49.530 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 18
2025-07-11 03:59:49.604 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 19
2025-07-11 03:59:49.728 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 20
2025-07-11 03:59:49.831 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 21
2025-07-11 03:59:49.953 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 22
2025-07-11 03:59:50.057 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752177587729_fbc92481, 内容长度: 23
2025-07-11 03:59:50.177 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送end控制事件 - 会话ID: session_1752177587729_fbc92481
2025-07-11 03:59:50.177 [34mINFO [0;39m 20740 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752177587729_fbc92481
2025-07-11 03:59:50.185 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-11 03:59:50.210 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai-chat/stream
2025-07-11 03:59:50.211 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-11 03:59:50.213 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 03:59:50.215 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 03:59:50.215 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 03:59:50.215 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 03:59:50.215 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 03:59:50.215 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/api/ai-chat/stream", parameters={}
2025-07-11 03:59:50.216 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-11 03:59:50.219 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-11 03:59:50.219 [39mDEBUG[0;39m 20740 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 04:00:01.496 [39mDEBUG[0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-11 04:00:01.496 [39mDEBUG[0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-11 04:00:01.496 [34mINFO [0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-11 04:00:01.496 [34mINFO [0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2456716b]]
2025-07-11 04:00:01.496 [39mDEBUG[0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2456716b]
2025-07-11 04:00:01.496 [39mDEBUG[0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2456716b]
2025-07-11 04:00:01.496 [34mINFO [0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-11 04:00:01.496 [39mDEBUG[0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 04:00:01.496 [39mDEBUG[0;39m 20740 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 16:50:43.025 [34mINFO [0;39m 25548 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-11 16:50:43.025 [34mINFO [0;39m 25548 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 25548 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-11 16:50:43.028 [39mDEBUG[0;39m 25548 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-11 16:50:43.029 [34mINFO [0;39m 25548 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-11 16:50:44.180 [34mINFO [0;39m 25548 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-11 16:50:44.183 [34mINFO [0;39m 25548 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-11 16:50:44.225 [34mINFO [0;39m 25548 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-11 16:50:44.352 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-11 16:50:44.353 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-11 16:50:44.355 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-11 16:50:44.356 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-11 16:50:44.356 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-11 16:50:44.356 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-11 16:50:44.356 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-11 16:50:44.356 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-11 16:50:44.357 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-11 16:50:44.357 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-11 16:50:44.357 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-11 16:50:44.357 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-11 16:50:44.357 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-11 16:50:44.357 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-11 16:50:44.358 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-11 16:50:44.358 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-11 16:50:44.358 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-11 16:50:44.358 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-11 16:50:44.358 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-11 16:50:44.358 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-11 16:50:44.359 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-11 16:50:44.359 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-11 16:50:44.359 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-11 16:50:44.359 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-11 16:50:44.359 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-11 16:50:44.359 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-11 16:50:44.360 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-11 16:50:44.360 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-11 16:50:44.360 [39mDEBUG[0;39m 25548 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-11 16:50:44.890 [34mINFO [0;39m 25548 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-11 16:50:44.898 [34mINFO [0;39m 25548 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-11 16:50:44.900 [34mINFO [0;39m 25548 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-11 16:50:44.900 [34mINFO [0;39m 25548 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-11 16:50:45.014 [34mINFO [0;39m 25548 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-11 16:50:45.014 [34mINFO [0;39m 25548 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1938 ms
2025-07-11 16:50:45.319 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-11 16:50:45.330 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-11 16:50:45.337 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-11 16:50:45.345 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-11 16:50:45.357 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-11 16:50:45.362 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-11 16:50:45.369 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-11 16:50:45.375 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-11 16:50:45.383 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-11 16:50:45.402 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-11 16:50:45.416 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-11 16:50:45.422 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-11 16:50:45.427 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-11 16:50:45.433 [39mDEBUG[0;39m 25548 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-11 16:50:45.448 [34mINFO [0;39m 25548 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-11 16:50:45.801 [34mINFO [0;39m 25548 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-11 16:50:46.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-11 16:50:46.571 [39mDEBUG[0;39m 25548 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-11 16:50:46.854 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-11 16:50:46.856 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.381 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-11 16:50:47.499 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-11 16:50:47.507 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-11 16:50:47.564 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-11 16:50:47.565 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.566 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-11 16:50:47.567 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.568 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-11 16:50:47.568 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.568 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-11 16:50:47.568 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.569 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-11 16:50:47.569 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.569 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-11 16:50:47.569 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.572 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-11 16:50:47.573 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.573 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-11 16:50:47.573 [39mDEBUG[0;39m 25548 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-11 16:50:47.714 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-11 16:50:47.716 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-11 16:50:47.722 [34mINFO [0;39m 25548 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@19f9404d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5481f204, org.springframework.security.web.context.SecurityContextPersistenceFilter@7a0f1f9d, org.springframework.security.web.header.HeaderWriterFilter@7e92e9a2, org.springframework.security.web.authentication.logout.LogoutFilter@61c42e54, com.example.pure.filter.JwtFilter@47f39279, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e97da56, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e8f3b76, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@171b0d3, org.springframework.security.web.session.SessionManagementFilter@5ab70df7, org.springframework.security.web.access.ExceptionTranslationFilter@48543f11, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d5eca06]
2025-07-11 16:50:47.725 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-11 16:50:47.727 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-11 16:50:47.727 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-11 16:50:47.728 [34mINFO [0;39m 25548 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-11 16:50:47.899 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-11 16:50:47.923 [34mINFO [0;39m 25548 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-11 16:50:48.003 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-11 16:50:48.016 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-11 16:50:48.390 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-11 16:50:48.494 [34mINFO [0;39m 25548 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-11 16:50:48.524 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-11 16:50:48.525 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-11 16:50:48.526 [39mDEBUG[0;39m 25548 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-11 16:50:48.527 [34mINFO [0;39m 25548 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@67e12e28, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36109a76, org.springframework.security.web.context.SecurityContextPersistenceFilter@45fa7fb7, org.springframework.security.web.header.HeaderWriterFilter@657e7e8, org.springframework.web.filter.CorsFilter@96271d8, org.springframework.security.web.authentication.logout.LogoutFilter@505a48a2, com.example.pure.filter.JwtFilter@47f39279, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@32eeef08, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@bbf361a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15f3f9cf, org.springframework.security.web.session.SessionManagementFilter@1b4ade78, org.springframework.security.web.access.ExceptionTranslationFilter@32f8a37b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7fec6c2f]
2025-07-11 16:50:48.559 [39mTRACE[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6ac4944a, started on Fri Jul 11 16:50:43 CST 2025
2025-07-11 16:50:48.570 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-11 16:50:48.571 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-11 16:50:48.572 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-11 16:50:48.572 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-11 16:50:48.574 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-07-11 16:50:48.575 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-11 16:50:48.576 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-11 16:50:48.576 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-11 16:50:48.576 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-11 16:50:48.656 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-11 16:50:48.690 [39mDEBUG[0;39m 25548 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-11 16:50:48.909 [34mINFO [0;39m 25548 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-11 16:50:48.917 [34mINFO [0;39m 25548 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-11 16:50:48.919 [39mDEBUG[0;39m 25548 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-11 16:50:48.919 [39mDEBUG[0;39m 25548 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-11 16:50:48.919 [34mINFO [0;39m 25548 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-11 16:50:48.919 [39mDEBUG[0;39m 25548 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2252dc5d]
2025-07-11 16:50:48.919 [39mDEBUG[0;39m 25548 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2252dc5d]
2025-07-11 16:50:48.919 [34mINFO [0;39m 25548 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2252dc5d]]
2025-07-11 16:50:48.919 [34mINFO [0;39m 25548 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-11 16:50:48.919 [39mDEBUG[0;39m 25548 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 16:50:48.919 [39mDEBUG[0;39m 25548 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-11 16:50:48.932 [34mINFO [0;39m 25548 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.477 seconds (JVM running for 7.408)
2025-07-11 16:51:48.548 [34mINFO [0;39m 25548 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-11 16:53:54.919 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 16:53:54.920 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-11 16:53:54.920 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-11 16:53:54.920 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-11 16:53:54.920 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-11 16:53:54.923 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6f26db76
2025-07-11 16:53:54.923 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6158d346
2025-07-11 16:53:54.923 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-11 16:53:54.923 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-07-11 16:53:54.934 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 16:53:54.937 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 16:53:54.945 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 16:53:54.948 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 16:53:54.948 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-11 16:53:54.953 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai-chat/stream] with attributes [permitAll]
2025-07-11 16:53:54.954 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 16:53:54.956 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/ai-chat/stream", parameters={}
2025-07-11 16:53:54.959 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 16:53:55.021 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [AiChatRequest(message=hello!, sessionId=null, messageType=chat)]
2025-07-11 16:53:55.097 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-1] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: hello!, 会话ID: null
2025-07-11 16:53:55.102 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752224035098_963f281d, 消息: hello!
2025-07-11 16:53:55.120 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送start控制事件 - 会话ID: session_1752224035098_963f281d
2025-07-11 16:53:55.120 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 1
2025-07-11 16:53:55.174 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 2
2025-07-11 16:53:55.263 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 3
2025-07-11 16:53:55.327 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 4
2025-07-11 16:53:55.474 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 5
2025-07-11 16:53:55.548 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 6
2025-07-11 16:53:55.604 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 7
2025-07-11 16:53:55.670 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 8
2025-07-11 16:53:55.757 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 9
2025-07-11 16:53:55.835 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 10
2025-07-11 16:53:55.949 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 11
2025-07-11 16:53:56.042 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 12
2025-07-11 16:53:56.165 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 13
2025-07-11 16:53:56.233 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 14
2025-07-11 16:53:56.293 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 15
2025-07-11 16:53:56.436 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 16
2025-07-11 16:53:56.573 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 17
2025-07-11 16:53:56.707 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 18
2025-07-11 16:53:56.808 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 19
2025-07-11 16:53:56.940 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 20
2025-07-11 16:53:57.000 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 21
2025-07-11 16:53:57.142 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 22
2025-07-11 16:53:57.264 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 23
2025-07-11 16:53:57.383 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 24
2025-07-11 16:53:57.451 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 25
2025-07-11 16:53:57.534 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 26
2025-07-11 16:53:57.619 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 27
2025-07-11 16:53:57.744 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 28
2025-07-11 16:53:57.860 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 29
2025-07-11 16:53:58.002 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224035098_963f281d, 内容长度: 30
2025-07-11 16:53:58.123 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送end控制事件 - 会话ID: session_1752224035098_963f281d
2025-07-11 16:53:58.123 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752224035098_963f281d
2025-07-11 16:53:58.140 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-11 16:53:58.152 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai-chat/stream
2025-07-11 16:53:58.153 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-11 16:53:58.154 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 16:53:58.156 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 16:53:58.156 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 16:53:58.156 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 16:53:58.156 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 16:53:58.157 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/api/ai-chat/stream", parameters={}
2025-07-11 16:53:58.158 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-11 16:53:58.160 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-11 16:53:58.161 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 16:54:21.067 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 16:54:21.067 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 16:54:21.067 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 16:54:21.068 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 16:54:21.068 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-11 16:54:21.068 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai-chat/stream] with attributes [permitAll]
2025-07-11 16:54:21.068 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 16:54:21.068 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/api/ai-chat/stream", parameters={}
2025-07-11 16:54:21.068 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 16:54:21.069 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [AiChatRequest(message=hello!, sessionId=null, messageType=chat)]
2025-07-11 16:54:21.070 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-3] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: hello!, 会话ID: null
2025-07-11 16:54:21.070 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752224061070_ba1dda51, 消息: hello!
2025-07-11 16:54:21.070 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送start控制事件 - 会话ID: session_1752224061070_ba1dda51
2025-07-11 16:54:21.070 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 1
2025-07-11 16:54:21.235 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 2
2025-07-11 16:54:21.384 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 3
2025-07-11 16:54:21.507 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 4
2025-07-11 16:54:21.610 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 5
2025-07-11 16:54:21.673 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 6
2025-07-11 16:54:21.785 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 7
2025-07-11 16:54:21.913 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 8
2025-07-11 16:54:21.983 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 9
2025-07-11 16:54:22.055 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 10
2025-07-11 16:54:22.187 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 11
2025-07-11 16:54:22.306 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 12
2025-07-11 16:54:22.378 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 13
2025-07-11 16:54:22.516 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 14
2025-07-11 16:54:22.658 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 15
2025-07-11 16:54:22.800 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 16
2025-07-11 16:54:22.878 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 17
2025-07-11 16:54:23.022 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 18
2025-07-11 16:54:23.151 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 19
2025-07-11 16:54:23.256 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 20
2025-07-11 16:54:23.403 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 21
2025-07-11 16:54:23.486 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 22
2025-07-11 16:54:23.574 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752224061070_ba1dda51, 内容长度: 23
2025-07-11 16:54:23.699 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : 发送end控制事件 - 会话ID: session_1752224061070_ba1dda51
2025-07-11 16:54:23.699 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-3] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752224061070_ba1dda51
2025-07-11 16:54:23.699 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-11 16:54:23.705 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai-chat/stream
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 16:54:23.706 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/api/ai-chat/stream", parameters={}
2025-07-11 16:54:23.707 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-11 16:54:23.707 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-11 16:54:23.707 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 17:21:48.554 [34mINFO [0;39m 25548 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-11 17:51:48.557 [34mINFO [0;39m 25548 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-07-11 18:21:48.559 [34mINFO [0;39m 25548 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-07-11 18:51:48.563 [34mINFO [0;39m 25548 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-07-11 19:08:13.638 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 19:08:13.639 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 19:08:13.643 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 19:08:13.645 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 19:08:13.646 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-11 19:08:13.649 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai-chat/stream] with attributes [permitAll]
2025-07-11 19:08:13.649 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 19:08:13.650 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : POST "/api/ai-chat/stream", parameters={}
2025-07-11 19:08:13.651 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-11 19:08:13.658 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [AiChatRequest(message=hello!, sessionId=null, messageType=chat)]
2025-07-11 19:08:13.663 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-8] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: hello!, 会话ID: null
2025-07-11 19:08:13.664 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752232093663_311f1573, 消息: hello!
2025-07-11 19:08:13.665 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送start控制事件 - 会话ID: session_1752232093663_311f1573
2025-07-11 19:08:13.665 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 1
2025-07-11 19:08:13.758 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 2
2025-07-11 19:08:13.825 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 3
2025-07-11 19:08:13.888 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 4
2025-07-11 19:08:13.974 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 5
2025-07-11 19:08:14.089 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 6
2025-07-11 19:08:14.183 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 7
2025-07-11 19:08:14.281 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 8
2025-07-11 19:08:14.388 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 9
2025-07-11 19:08:14.482 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 10
2025-07-11 19:08:14.618 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 11
2025-07-11 19:08:14.680 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 12
2025-07-11 19:08:14.764 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 13
2025-07-11 19:08:14.816 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 14
2025-07-11 19:08:14.898 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 15
2025-07-11 19:08:14.981 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 16
2025-07-11 19:08:15.129 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 17
2025-07-11 19:08:15.239 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 18
2025-07-11 19:08:15.355 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 19
2025-07-11 19:08:15.467 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 20
2025-07-11 19:08:15.615 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 21
2025-07-11 19:08:15.699 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 22
2025-07-11 19:08:15.764 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 23
2025-07-11 19:08:15.913 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 24
2025-07-11 19:08:15.984 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 25
2025-07-11 19:08:16.098 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 26
2025-07-11 19:08:16.212 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 27
2025-07-11 19:08:16.315 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 28
2025-07-11 19:08:16.371 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 29
2025-07-11 19:08:16.427 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752232093663_311f1573, 内容长度: 30
2025-07-11 19:08:16.570 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : 发送end控制事件 - 会话ID: session_1752232093663_311f1573
2025-07-11 19:08:16.570 [34mINFO [0;39m 25548 --- [http-nio-8080-exec-8] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752232093663_311f1573
2025-07-11 19:08:16.571 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-11 19:08:16.576 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai-chat/stream
2025-07-11 19:08:16.576 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-11 19:08:16.576 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 19:08:16.577 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-11 19:08:16.577 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-11 19:08:16.577 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-11 19:08:16.577 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-11 19:08:16.577 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/api/ai-chat/stream", parameters={}
2025-07-11 19:08:16.577 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-11 19:08:16.578 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-11 19:08:16.578 [39mDEBUG[0;39m 25548 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-11 19:21:48.565 [34mINFO [0;39m 25548 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-07-11 19:51:48.574 [34mINFO [0;39m 25548 --- [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-07-11 20:21:48.577 [34mINFO [0;39m 25548 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-07-11 20:51:48.588 [34mINFO [0;39m 25548 --- [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-07-11 21:21:48.601 [34mINFO [0;39m 25548 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
2025-07-11 21:51:48.614 [34mINFO [0;39m 25548 --- [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
2025-07-11 22:21:48.624 [34mINFO [0;39m 25548 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
2025-07-11 22:51:48.626 [34mINFO [0;39m 25548 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 12]
2025-07-11 23:21:48.635 [34mINFO [0;39m 25548 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 13]
2025-07-11 23:51:48.645 [34mINFO [0;39m 25548 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 14]
