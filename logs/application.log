2025-07-25 03:01:26.812 [34mINFO [0;39m 11360 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 03:01:26.820 [34mINFO [0;39m 11360 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 11360 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-07-25 03:01:26.821 [39mDEBUG[0;39m 11360 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-25 03:01:26.821 [34mINFO [0;39m 11360 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-25 03:01:28.140 [34mINFO [0;39m 11360 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 03:01:28.142 [34mINFO [0;39m 11360 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 03:01:28.190 [34mINFO [0;39m 11360 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-25 03:01:28.330 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-07-25 03:01:28.331 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-07-25 03:01:28.331 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-25 03:01:28.331 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-25 03:01:28.331 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-25 03:01:28.331 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-25 03:01:28.332 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-25 03:01:28.334 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-25 03:01:28.334 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-07-25 03:01:28.334 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-07-25 03:01:28.334 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-07-25 03:01:28.335 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-07-25 03:01:28.335 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-25 03:01:28.335 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-25 03:01:28.335 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-25 03:01:28.335 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-25 03:01:28.335 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-25 03:01:28.336 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-25 03:01:28.336 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-25 03:01:28.336 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-25 03:01:28.336 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-25 03:01:28.336 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-25 03:01:28.336 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-25 03:01:28.337 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-25 03:01:28.337 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-25 03:01:28.337 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-25 03:01:28.337 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-25 03:01:28.337 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-25 03:01:28.337 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-25 03:01:28.338 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-25 03:01:28.338 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-07-25 03:01:28.338 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-07-25 03:01:28.338 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-07-25 03:01:28.339 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-07-25 03:01:28.339 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-25 03:01:28.339 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-25 03:01:28.339 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-25 03:01:28.339 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-25 03:01:28.339 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-25 03:01:28.340 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-25 03:01:28.340 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-25 03:01:28.340 [39mDEBUG[0;39m 11360 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-25 03:01:29.066 [34mINFO [0;39m 11360 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-25 03:01:29.072 [34mINFO [0;39m 11360 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-25 03:01:29.074 [34mINFO [0;39m 11360 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-25 03:01:29.074 [34mINFO [0;39m 11360 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-25 03:01:29.170 [34mINFO [0;39m 11360 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-25 03:01:29.171 [34mINFO [0;39m 11360 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2296 ms
2025-07-25 03:01:29.472 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-25 03:01:29.483 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-07-25 03:01:29.493 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-07-25 03:01:29.502 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-25 03:01:29.507 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-25 03:01:29.512 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-25 03:01:29.521 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-25 03:01:29.525 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-25 03:01:29.532 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-25 03:01:29.536 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-25 03:01:29.541 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-25 03:01:29.554 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-25 03:01:29.560 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-07-25 03:01:29.565 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-07-25 03:01:29.574 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-07-25 03:01:29.580 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-25 03:01:29.584 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-25 03:01:29.589 [39mDEBUG[0;39m 11360 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-25 03:01:29.603 [34mINFO [0;39m 11360 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-25 03:01:30.688 [1;31mERROR[0;39m 11360 --- [main] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:165)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:55)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:833)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:416)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:332)
	at org.springframework.boot.jdbc.EmbeddedDatabaseConnection.isEmbedded(EmbeddedDatabaseConnection.java:164)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.isEmbeddedDatabase(DataSourceScriptDatabaseInitializer.java:70)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.isEnabled(AbstractScriptDatabaseInitializer.java:83)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:106)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.pure.PureApplication.main(PureApplication.java:37)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:142)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:957)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:821)
	... 132 common frames omitted
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53)
	... 135 common frames omitted
2025-07-25 03:01:31.338 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-25 03:01:31.338 [39mDEBUG[0;39m 11360 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-25 03:01:31.644 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-25 03:01:31.645 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.370 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-25 03:01:32.590 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-25 03:01:32.599 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-25 03:01:32.685 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-25 03:01:32.685 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.686 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-25 03:01:32.686 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.686 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-25 03:01:32.687 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.687 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-25 03:01:32.688 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.688 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-25 03:01:32.688 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.688 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-25 03:01:32.688 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.694 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-25 03:01:32.694 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.694 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-25 03:01:32.694 [39mDEBUG[0;39m 11360 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-25 03:01:32.844 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-25 03:01:32.845 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-25 03:01:32.849 [34mINFO [0;39m 11360 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@49b6373f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@20f3291d, org.springframework.security.web.context.SecurityContextPersistenceFilter@62966c9f, org.springframework.security.web.header.HeaderWriterFilter@3f7a7ce6, org.springframework.security.web.authentication.logout.LogoutFilter@7bf997e0, com.example.pure.filter.JwtFilter@6c56fff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6915351c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2ac0cb64, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2f51b100, org.springframework.security.web.session.SessionManagementFilter@10c67c1c, org.springframework.security.web.access.ExceptionTranslationFilter@554a2f3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2e243122]
2025-07-25 03:01:32.851 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-25 03:01:32.853 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-25 03:01:32.854 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-25 03:01:32.854 [34mINFO [0;39m 11360 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-25 03:01:33.083 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-25 03:01:33.109 [34mINFO [0;39m 11360 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-25 03:01:33.181 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 103 mappings in 'requestMappingHandlerMapping'
2025-07-25 03:01:33.188 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-25 03:01:33.686 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-25 03:01:33.805 [34mINFO [0;39m 11360 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-25 03:01:33.823 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-07-25 03:01:33.824 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-25 03:01:33.825 [39mDEBUG[0;39m 11360 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-25 03:01:33.826 [34mINFO [0;39m 11360 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5834fd3f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4446e1d9, org.springframework.security.web.context.SecurityContextPersistenceFilter@1267f832, org.springframework.security.web.header.HeaderWriterFilter@23243bc0, org.springframework.web.filter.CorsFilter@3f13720f, org.springframework.security.web.authentication.logout.LogoutFilter@481eb705, com.example.pure.filter.JwtFilter@6c56fff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@711261c7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@73eaae1e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1df60140, org.springframework.security.web.session.SessionManagementFilter@770c3ca2, org.springframework.security.web.access.ExceptionTranslationFilter@1a7f2d34, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@58df431e]
2025-07-25 03:01:33.858 [39mTRACE[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5733f295, started on Fri Jul 25 03:01:26 CST 2025
2025-07-25 03:01:33.871 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-07-25 03:01:33.872 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-07-25 03:01:33.875 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-25 03:01:33.875 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-07-25 03:01:33.875 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-07-25 03:01:33.875 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-07-25 03:01:33.875 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-07-25 03:01:33.876 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-25 03:01:33.876 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-25 03:01:33.877 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-25 03:01:33.877 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-25 03:01:33.986 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 03:01:34.060 [39mDEBUG[0;39m 11360 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 03:01:34.408 [34mINFO [0;39m 11360 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-25 03:01:34.421 [34mINFO [0;39m 11360 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-25 03:01:34.422 [39mDEBUG[0;39m 11360 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-25 03:01:34.422 [39mDEBUG[0;39m 11360 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-25 03:01:34.423 [34mINFO [0;39m 11360 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-25 03:01:34.423 [39mDEBUG[0;39m 11360 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466f7259]
2025-07-25 03:01:34.423 [39mDEBUG[0;39m 11360 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466f7259]
2025-07-25 03:01:34.423 [34mINFO [0;39m 11360 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466f7259]]
2025-07-25 03:01:34.423 [34mINFO [0;39m 11360 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-25 03:01:34.423 [39mDEBUG[0;39m 11360 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-25 03:01:34.423 [39mDEBUG[0;39m 11360 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-25 03:01:34.438 [34mINFO [0;39m 11360 --- [main] com.example.pure.PureApplication : Started PureApplication in 8.092 seconds (JVM running for 9.307)
2025-07-25 03:02:33.845 [34mINFO [0;39m 11360 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-25 03:06:50.267 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-2] o.a.coyote.http11.Http11Processor : Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x000xf30x010x000x000xef0x030x030xc8m0x0990xa4w0xfe90xa440x090x86^{0xc90x060xbc0xda_0xf20x830xd20xb9v0x88X+0x97&0xd80x06q ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:407)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:263)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:07:35.538 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-4] o.a.coyote.http11.Http11Processor : Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x000xf30x010x000x000xef0x030x03,G0xdc0x800xfd0xa30xa90xc40xb8n0x1bY:0xad0x15P0xb9(0xf3-0x7f0x8ej0x9f0xa6TM0x06F-0x950x9d ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:407)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:263)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:56.645 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-8] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 03:08:56.646 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-25 03:08:56.646 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-25 03:08:56.646 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-25 03:08:56.646 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-25 03:08:56.648 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4d90f1b5
2025-07-25 03:08:56.648 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@30b76133
2025-07-25 03:08:56.648 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-25 03:08:56.648 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-07-25 03:08:56.660 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-25 03:08:56.663 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-25 03:08:56.671 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-25 03:08:56.673 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-25 03:08:56.673 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-25 03:08:56.678 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-07-25 03:08:56.678 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-25 03:08:56.681 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-07-25 03:08:56.683 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-25 03:08:56.766 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: 你是什么模型？
2025-07-25 03:08:56.779 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=user, content (truncated)...]
2025-07-25 03:08:56.854 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiRequestServiceImpl : 收到OpenAI兼容聊天请求 - 模型: gemini-2.5-pro, 流式: true, 消息数: 1
2025-07-25 03:08:56.855 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiRequestServiceImpl : 处理流式聊天请求 - 模型: gemini-2.5-pro
2025-07-25 03:08:56.855 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 处理OpenAI兼容流式聊天请求 - 模型: gemini-2.5-pro
2025-07-25 03:08:56.865 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:08:56.870 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3de247e4] was not registered for synchronization because synchronization is not active
2025-07-25 03:08:56.876 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-8] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-25 03:08:57.889 [1;31mERROR[0;39m 11360 --- [http-nio-8080-exec-8] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:165)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:55)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:833)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:416)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:345)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy107.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy146.selectById(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.validateCompatibleApiKey(OpenAiCompatibleServiceImpl.java:405)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:100)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy148.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:142)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:957)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:821)
	... 161 common frames omitted
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53)
	... 164 common frames omitted
2025-07-25 03:08:57.892 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3de247e4]
2025-07-25 03:08:57.893 [1;31mERROR[0;39m 11360 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 验证兼容API密钥失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy107.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:142)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy2/jdk.proxy2.$Proxy146.selectById(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.validateCompatibleApiKey(OpenAiCompatibleServiceImpl.java:405)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:100)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy148.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:156)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 130 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:345)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:333)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	... 138 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:165)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:55)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:833)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:416)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 148 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:52)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:95)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:140)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:79)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:142)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:957)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:821)
	... 161 common frames omitted
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:144)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:53)
	... 164 common frames omitted
2025-07-25 03:08:57.908 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-25 03:08:57.921 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.c.r.async.WebAsyncManager : Async error, dispatch to /v1/chat/completions
2025-07-25 03:08:57.922 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-25 03:08:57.923 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-25 03:08:57.925 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-25 03:08:57.925 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-25 03:08:57.925 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-25 03:08:57.925 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-25 03:08:57.925 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-07-25 03:08:57.926 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result [java.lang.RuntimeException: Invalid API key: Validation error: nested exception is org.apache.ibatis (truncated)...]
2025-07-25 03:08:57.931 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-07-25 03:08:57.931 [1;31mERROR[0;39m 11360 --- [http-nio-8080-exec-8] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.RuntimeException: Invalid API key: Validation error: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.sendErrorAndComplete(OpenAiCompatibleServiceImpl.java:876)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:104)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy148.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:57.936 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-25 03:08:57.937 [31mWARN [0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.example.pure.common.Result] with preset Content-Type 'text/event-stream'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:106)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:87)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.doDispatch(ApplicationDispatcher.java:589)
	at org.apache.catalina.core.ApplicationDispatcher.dispatch(ApplicationDispatcher.java:558)
	at org.apache.catalina.core.AsyncContextImpl$AsyncRunnable.run(AsyncContextImpl.java:569)
	at org.apache.catalina.core.AsyncContextImpl.doInternalDispatch(AsyncContextImpl.java:339)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:166)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:237)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:57.938 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Unresolved failure from "ASYNC" dispatch: java.lang.RuntimeException: Invalid API key: Validation error: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-07-25 03:08:57.938 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-25 03:08:57.942 [1;31mERROR[0;39m 11360 --- [http-nio-8080-exec-8] o.a.c.c.C.[.[.[.[dispatcherServlet] : Servlet.service() for servlet [dispatcherServlet] threw exception
java.lang.RuntimeException: Invalid API key: Validation error: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.sendErrorAndComplete(OpenAiCompatibleServiceImpl.java:876)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:104)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy148.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:57.943 [1;31mERROR[0;39m 11360 --- [http-nio-8080-exec-8] o.a.c.c.C.[.[.[.[dispatcherServlet] : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.RuntimeException: Invalid API key: Validation error: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.] with root cause
java.lang.RuntimeException: Invalid API key: Validation error: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]
### The error may involve com.example.pure.mapper.primary.UserApiKeyMapper.selectById
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.sendErrorAndComplete(OpenAiCompatibleServiceImpl.java:876)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:104)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy148.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:57.945 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : "INCLUDE" dispatch for POST "/error", parameters={}
2025-07-25 03:08:57.947 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)
2025-07-25 03:08:57.949 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-25 03:08:57.949 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-07-25 03:08:57.949 [1;31mERROR[0;39m 11360 --- [http-nio-8080-exec-8] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'text/event-stream'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.doInclude(ApplicationDispatcher.java:537)
	at org.apache.catalina.core.ApplicationDispatcher.include(ApplicationDispatcher.java:480)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:358)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:237)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:323)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:164)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:237)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:57.949 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-25 03:08:57.949 [31mWARN [0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.example.pure.common.Result] with preset Content-Type 'text/event-stream'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.doInclude(ApplicationDispatcher.java:537)
	at org.apache.catalina.core.ApplicationDispatcher.include(ApplicationDispatcher.java:480)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:358)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:237)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:323)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:164)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:237)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-25 03:08:57.950 [31mWARN [0;39m 11360 --- [http-nio-8080-exec-8] o.s.w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'text/event-stream']
2025-07-25 03:08:57.950 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Exiting from "INCLUDE" dispatch, status 200
2025-07-25 03:10:23.638 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-25 03:10:23.638 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-25 03:10:23.638 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-25 03:10:23.638 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-25 03:10:23.638 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-25 03:10:23.639 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-07-25 03:10:23.639 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-25 03:10:23.639 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-07-25 03:10:23.639 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-25 03:10:23.640 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: 你是什么模型？
2025-07-25 03:10:23.640 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=user, content (truncated)...]
2025-07-25 03:10:23.641 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.OpenAiRequestServiceImpl : 收到OpenAI兼容聊天请求 - 模型: gemini-2.5-pro, 流式: true, 消息数: 1
2025-07-25 03:10:23.641 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.OpenAiRequestServiceImpl : 处理流式聊天请求 - 模型: gemini-2.5-pro
2025-07-25 03:10:23.641 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 处理OpenAI兼容流式聊天请求 - 模型: gemini-2.5-pro
2025-07-25 03:10:23.641 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:10:23.641 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45dac91c] was not registered for synchronization because synchronization is not active
2025-07-25 03:10:23.641 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-25 03:10:23.897 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-25 03:10:23.900 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@27b3722] will not be managed by Spring
2025-07-25 03:10:23.904 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectById : ==>  Preparing: SELECT id, user_id, provider, key_name, api_key_encrypted, is_active, priority, usage_count, last_used_at, created_at, updated_at FROM user_api_keys WHERE id = ?
2025-07-25 03:10:23.924 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectById : ==> Parameters: 3(Long)
2025-07-25 03:10:23.953 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectById : <==      Total: 1
2025-07-25 03:10:23.957 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45dac91c]
2025-07-25 03:10:23.958 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始异步处理流式聊天 - 用户ID: 1, 密钥ID: 3
2025-07-25 03:10:23.958 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.impl.AiConfigServiceImpl : 获取用户AI配置 - 用户ID: 1
2025-07-25 03:10:23.958 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:10:23.958 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f9827e4] was not registered for synchronization because synchronization is not active
2025-07-25 03:10:23.958 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2035020133 wrapping com.mysql.cj.jdbc.ConnectionImpl@27b3722] will not be managed by Spring
2025-07-25 03:10:23.958 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config WHERE user_id = ?
2025-07-25 03:10:23.959 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-07-25 03:10:23.963 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-07-25 03:10:23.963 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f9827e4]
2025-07-25 03:10:23.965 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择最佳API密钥 - 用户ID: 1, 提供商: GOOGLE
2025-07-25 03:10:23.966 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:10:23.966 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d2d5322] was not registered for synchronization because synchronization is not active
2025-07-25 03:10:23.966 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@879798452 wrapping com.mysql.cj.jdbc.ConnectionImpl@27b3722] will not be managed by Spring
2025-07-25 03:10:23.966 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, provider, key_name, api_key_encrypted, is_active, priority, usage_count, last_used_at, created_at, updated_at FROM user_api_keys WHERE user_id = ? AND provider = ? AND is_active = TRUE ORDER BY priority ASC, created_at ASC
2025-07-25 03:10:23.966 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-07-25 03:10:23.970 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.U.selectActiveByUserIdAndProvider : <==      Total: 2
2025-07-25 03:10:23.970 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d2d5322]
2025-07-25 03:10:23.970 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:10:23.970 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c36e073] was not registered for synchronization because synchronization is not active
2025-07-25 03:10:23.971 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@27b3722] will not be managed by Spring
2025-07-25 03:10:23.971 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, provider, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE user_id = ? AND provider = ? AND is_healthy = TRUE ORDER BY current_requests ASC, total_requests ASC
2025-07-25 03:10:23.971 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-07-25 03:10:23.974 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : <==      Total: 2
2025-07-25 03:10:23.974 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c36e073]
2025-07-25 03:10:23.974 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择API密钥 - ID: 3, 当前请求数: 0
2025-07-25 03:10:23.975 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.pure.util.MultimodalContentUtils : 纯文本消息 - 长度: 7 字符
2025-07-25 03:10:23.982 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.LoadBalancerServiceImpl : 开始使用API密钥 - ID: 3
2025-07-25 03:10:23.982 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:10:23.982 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21b4343d]
2025-07-25 03:10:23.983 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@572458920 wrapping com.mysql.cj.jdbc.ConnectionImpl@27b3722] will be managed by Spring
2025-07-25 03:10:23.984 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.A.incrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = current_requests + 1, total_requests = total_requests + 1, updated_at = NOW() WHERE api_key_id = ?
2025-07-25 03:10:23.984 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.A.incrementRequests : ==> Parameters: 3(Long)
2025-07-25 03:10:23.987 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.m.p.A.incrementRequests : <==    Updates: 1
2025-07-25 03:10:23.987 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21b4343d]
2025-07-25 03:10:23.988 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21b4343d]
2025-07-25 03:10:23.988 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21b4343d]
2025-07-25 03:10:23.988 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21b4343d]
2025-07-25 03:10:24.010 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-793e0a5c, 数据大小: 232 bytes
2025-07-25 03:10:24.011 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-07-25 03:10:24.011 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : 为GOOGLE添加stream_options参数
2025-07-25 03:10:24.011 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 流式请求详情 ===
2025-07-25 03:10:24.011 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-25 03:10:24.011 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-07-25 03:10:24.011 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {top_p=1.0, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=user, content=你是什么模型？}], model=gemini-2.5-pro, stream_options=OpenAiChatRequest.StreamOptions(includeUsage=true)}
2025-07-25 03:10:24.056 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-25 03:10:24.057 [34mINFO [0;39m 11360 --- [http-nio-8080-exec-9] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始流式聊天 - 响应ID: chatcmpl-793e0a5c, 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-07-25 03:10:24.074 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.w.r.f.client.ExchangeFunctions : [364383c3] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-25 03:10:24.230 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-25 03:10:24.232 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-25 03:10:24.232 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-25 03:10:24.942 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [364383c3] Encoding [{top_p=1.0, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=user, content=你是什么模型？}],  (truncated)...]
2025-07-25 03:10:36.841 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [364383c3] [c4692720-1] Response 200 OK
2025-07-25 03:10:36.862 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"我是一个大型语言模型，","role":"assistant"},"index":0}],"created":1753384237,"id":"I4WCaOvmAqmJz7IP68vBqAs","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":6,"prompt_tokens":5,"total_tokens":928}}
2025-07-25 03:10:36.862 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"我是一个大型语言模型，","role":"assistant"},"index":0}],"created":1753384237,"id":"I4WCaOvmAqmJz7IP68vBqAs","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":6,"prompt_tokens":5,"total_tokens":928}}
2025-07-25 03:10:36.862 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 257, 响应ID: chatcmpl-793e0a5c
2025-07-25 03:10:36.862 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 257 bytes
2025-07-25 03:10:37.016 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"由 Google 训练。","role":"assistant"},"finish_reason":"stop","index":0}],"created":1753384237,"id":"I4WCaOvmAqmJz7IP68vBqAs","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":11,"prompt_tokens":5,"total_tokens":933}}
2025-07-25 03:10:37.016 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"由 Google 训练。","role":"assistant"},"finish_reason":"stop","index":0}],"created":1753384237,"id":"I4WCaOvmAqmJz7IP68vBqAs","model":"gemini-2.5-pro","object":"chat.completion.chunk","usage":{"completion_tokens":11,"prompt_tokens":5,"total_tokens":933}}
2025-07-25 03:10:37.016 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 282, 响应ID: chatcmpl-793e0a5c
2025-07-25 03:10:37.016 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 282 bytes
2025-07-25 03:10:37.018 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: [DONE]
2025-07-25 03:10:37.018 [31mWARN [0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 过滤掉非数据行: [DONE]
2025-07-25 03:10:37.022 [34mINFO [0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求完成
2025-07-25 03:10:37.022 [34mINFO [0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式响应正常完成 - 响应ID: chatcmpl-793e0a5c, 模型: gemini-2.5-pro
2025-07-25 03:10:37.024 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-793e0a5c, 数据大小: 281 bytes
2025-07-25 03:10:37.024 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE完成事件: [DONE]
2025-07-25 03:10:37.025 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /v1/chat/completions
2025-07-25 03:10:37.025 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : SseEmitter安全完成 - 响应ID: chatcmpl-793e0a5c
2025-07-25 03:10:37.026 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-25 03:10:37.026 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-25 03:10:37.026 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-25 03:10:37.026 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-25 03:10:37.026 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-07-25 03:10:37.026 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 结束使用API密钥 - ID: 3, 成功: true
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c]
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@728461115 wrapping com.mysql.cj.jdbc.ConnectionImpl@27b3722] will be managed by Spring
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = GREATEST(current_requests - 1, 0), updated_at = NOW() WHERE api_key_id = ?
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-25 03:10:37.028 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : ==> Parameters: 3(Long)
2025-07-25 03:10:37.031 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : <==    Updates: 1
2025-07-25 03:10:37.031 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c]
2025-07-25 03:10:37.032 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c] from current transaction
2025-07-25 03:10:37.032 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : ==>  Preparing: UPDATE user_api_keys SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?
2025-07-25 03:10:37.032 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : ==> Parameters: 3(Long)
2025-07-25 03:10:37.034 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : <==    Updates: 1
2025-07-25 03:10:37.034 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c]
2025-07-25 03:10:37.035 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c]
2025-07-25 03:10:37.035 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c]
2025-07-25 03:10:37.035 [39mDEBUG[0;39m 11360 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24fc815c]
2025-07-25 03:10:37.042 [34mINFO [0;39m 11360 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式聊天完成 - 响应ID: chatcmpl-793e0a5c, API密钥ID: 3
2025-07-25 03:18:37.812 [39mDEBUG[0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-25 03:18:37.812 [39mDEBUG[0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-25 03:18:37.812 [34mINFO [0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-25 03:18:37.812 [34mINFO [0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466f7259]]
2025-07-25 03:18:37.812 [39mDEBUG[0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466f7259]
2025-07-25 03:18:37.812 [39mDEBUG[0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@466f7259]
2025-07-25 03:18:37.812 [34mINFO [0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-25 03:18:37.812 [39mDEBUG[0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-25 03:18:37.812 [39mDEBUG[0;39m 11360 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-25 03:18:39.915 [34mINFO [0;39m 11360 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-25 03:18:39.920 [34mINFO [0;39m 11360 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
