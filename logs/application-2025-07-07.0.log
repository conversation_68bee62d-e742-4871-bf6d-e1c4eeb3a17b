2025-07-07 00:10:53.894 [34mINFO [0;39m 10760 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-Http<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 14]
2025-07-07 00:40:53.907 [34mINFO [0;39m 10760 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-Http<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 15]
2025-07-07 01:10:53.912 [34mINFO [0;39m 10760 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 16]
2025-07-07 01:16:17.840 [34mINFO [0;39m 10760 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-07 01:16:17.840 [34mINFO [0;39m 10760 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7200702]]
2025-07-07 01:16:17.840 [34mINFO [0;39m 10760 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-07 01:16:20.859 [34mINFO [0;39m 10760 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-07 01:16:20.869 [34mINFO [0;39m 10760 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-07 19:43:28.053 [34mINFO [0;39m 16080 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 19:43:28.054 [34mINFO [0;39m 16080 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 16080 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-07 19:43:28.055 [39mDEBUG[0;39m 16080 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-07 19:43:28.056 [34mINFO [0;39m 16080 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-07 19:43:29.160 [34mINFO [0;39m 16080 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 19:43:29.162 [34mINFO [0;39m 16080 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 19:43:29.206 [34mINFO [0;39m 16080 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-07 19:43:29.331 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-07 19:43:29.332 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-07 19:43:29.332 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-07 19:43:29.332 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-07 19:43:29.333 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-07 19:43:29.334 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-07 19:43:29.334 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-07 19:43:29.334 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-07 19:43:29.334 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-07 19:43:29.335 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-07 19:43:29.335 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-07 19:43:29.335 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-07 19:43:29.335 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-07 19:43:29.335 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-07 19:43:29.335 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-07 19:43:29.336 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-07 19:43:29.337 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-07 19:43:29.338 [39mDEBUG[0;39m 16080 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-07 19:43:29.828 [34mINFO [0;39m 16080 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-07 19:43:29.833 [34mINFO [0;39m 16080 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 19:43:29.834 [34mINFO [0;39m 16080 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-07 19:43:29.834 [34mINFO [0;39m 16080 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 19:43:29.940 [34mINFO [0;39m 16080 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-07 19:43:29.940 [34mINFO [0;39m 16080 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1847 ms
2025-07-07 19:43:30.173 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-07 19:43:30.188 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-07 19:43:30.195 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-07 19:43:30.203 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-07 19:43:30.215 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-07 19:43:30.220 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-07 19:43:30.229 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-07 19:43:30.235 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-07 19:43:30.244 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-07 19:43:30.254 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-07 19:43:30.268 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-07 19:43:30.283 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-07 19:43:30.287 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-07 19:43:30.292 [39mDEBUG[0;39m 16080 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-07 19:43:30.303 [34mINFO [0;39m 16080 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-07 19:43:30.623 [34mINFO [0;39m 16080 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-07 19:43:31.290 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-07 19:43:31.291 [39mDEBUG[0;39m 16080 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-07 19:43:31.590 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-07 19:43:31.592 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.069 [34mINFO [0;39m 16080 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-07 19:43:32.182 [34mINFO [0;39m 16080 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-07 19:43:32.189 [34mINFO [0;39m 16080 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-07 19:43:32.242 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-07 19:43:32.243 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.244 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-07 19:43:32.245 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.245 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-07 19:43:32.245 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.245 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-07 19:43:32.245 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.246 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-07 19:43:32.246 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.246 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-07 19:43:32.246 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.249 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-07 19:43:32.250 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.250 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-07 19:43:32.250 [39mDEBUG[0;39m 16080 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-07 19:43:32.342 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-07 19:43:32.343 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-07 19:43:32.347 [34mINFO [0;39m 16080 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@4df8443f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@79aa675b, org.springframework.security.web.context.SecurityContextPersistenceFilter@25814d3c, org.springframework.security.web.header.HeaderWriterFilter@539f2fec, org.springframework.security.web.authentication.logout.LogoutFilter@c6db00d, com.example.pure.filter.JwtFilter@4e343265, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@427128a6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@701c413, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3fe59f84, org.springframework.security.web.session.SessionManagementFilter@6821d4e1, org.springframework.security.web.access.ExceptionTranslationFilter@2d5b549b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4865434e]
2025-07-07 19:43:32.348 [34mINFO [0;39m 16080 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-07 19:43:32.350 [34mINFO [0;39m 16080 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-07 19:43:32.350 [34mINFO [0;39m 16080 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-07 19:43:32.495 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-07 19:43:32.514 [34mINFO [0;39m 16080 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-07 19:43:32.574 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 78 mappings in 'requestMappingHandlerMapping'
2025-07-07 19:43:32.582 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-07 19:43:32.899 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-07 19:43:32.993 [34mINFO [0;39m 16080 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-07 19:43:33.012 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-07 19:43:33.012 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-07 19:43:33.012 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-07 19:43:33.012 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-07 19:43:33.012 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-07 19:43:33.013 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-07 19:43:33.014 [39mDEBUG[0;39m 16080 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-07 19:43:33.015 [34mINFO [0;39m 16080 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7b6c3b37, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a8964c1, org.springframework.security.web.context.SecurityContextPersistenceFilter@2c56e044, org.springframework.security.web.header.HeaderWriterFilter@54a98358, org.springframework.web.filter.CorsFilter@38320819, org.springframework.security.web.authentication.logout.LogoutFilter@53cba89f, com.example.pure.filter.JwtFilter@4e343265, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@d970881, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@153cf928, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16c3388e, org.springframework.security.web.session.SessionManagementFilter@1f7853af, org.springframework.security.web.access.ExceptionTranslationFilter@510e0dd2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6b58a0f9]
2025-07-07 19:43:33.047 [39mTRACE[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@71652c98, started on Mon Jul 07 19:43:28 CST 2025
2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-07 19:43:33.073 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-07 19:43:33.074 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-07 19:43:33.074 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-07 19:43:33.076 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-07 19:43:33.077 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
2025-07-07 19:43:33.078 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-07 19:43:33.079 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-07 19:43:33.079 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-07 19:43:33.079 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-07 19:43:33.166 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-07 19:43:33.194 [39mDEBUG[0;39m 16080 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-07 19:43:33.415 [34mINFO [0;39m 16080 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 19:43:33.424 [34mINFO [0;39m 16080 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 19:43:33.426 [39mDEBUG[0;39m 16080 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-07 19:43:33.426 [39mDEBUG[0;39m 16080 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-07 19:43:33.426 [34mINFO [0;39m 16080 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-07 19:43:33.426 [39mDEBUG[0;39m 16080 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3a2b1f24]
2025-07-07 19:43:33.426 [39mDEBUG[0;39m 16080 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3a2b1f24]
2025-07-07 19:43:33.426 [34mINFO [0;39m 16080 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3a2b1f24]]
2025-07-07 19:43:33.426 [34mINFO [0;39m 16080 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-07 19:43:33.426 [39mDEBUG[0;39m 16080 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-07 19:43:33.426 [39mDEBUG[0;39m 16080 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-07 19:43:33.438 [34mINFO [0;39m 16080 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.805 seconds (JVM running for 7.102)
2025-07-07 19:44:33.032 [34mINFO [0;39m 16080 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-07 19:47:09.700 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 19:47:09.700 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-07 19:47:09.700 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-07 19:47:09.700 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-07 19:47:09.700 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-07 19:47:09.702 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6e2bdfae
2025-07-07 19:47:09.702 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@31a5cd06
2025-07-07 19:47:09.703 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-07 19:47:09.703 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-07-07 19:47:09.713 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/files/upload
2025-07-07 19:47:09.716 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:47:09.727 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.h.SimpleUrlHandlerMapping : Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-07-07 19:47:09.783 [31mWARN [0;39m 16080 --- [http-nio-8080-exec-2] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-04-04T13:50:21Z. Current time: 2025-07-07T11:47:09Z, a difference of 8114208781 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-07 19:47:09.784 [31mWARN [0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/files/upload
2025-07-07 19:47:09.815 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 19:47:15.743 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/files/upload
2025-07-07 19:47:15.743 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:47:15.744 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.w.s.h.SimpleUrlHandlerMapping : Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-07-07 19:47:15.748 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-07 19:47:15.748 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-07 19:47:15.762 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Failed to authorize filter invocation [GET /api/files/upload] with attributes [authenticated]
2025-07-07 19:47:15.763 [31mWARN [0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: Full authentication is required to access this resource, URI: /api/files/upload
2025-07-07 19:47:15.764 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 19:47:51.297 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing GET /api/files/upload
2025-07-07 19:47:51.297 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:47:51.298 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.w.s.h.SimpleUrlHandlerMapping : Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-07-07 19:47:51.751 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-07 19:47:51.768 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-07 19:47:51.771 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7]
2025-07-07 19:47:51.778 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@383369941 wrapping com.mysql.cj.jdbc.ConnectionImpl@2a3bea03] will be managed by Spring
2025-07-07 19:47:51.780 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-07 19:47:51.797 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-07-07 19:47:51.818 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-07 19:47:51.819 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7]
2025-07-07 19:47:51.820 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-07 19:47:51.821 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7] from current transaction
2025-07-07 19:47:51.821 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-07 19:47:51.821 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-07 19:47:51.825 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-07 19:47:51.825 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7]
2025-07-07 19:47:51.827 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-07 19:47:51.827 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7]
2025-07-07 19:47:51.827 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7]
2025-07-07 19:47:51.828 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55d98af7]
2025-07-07 19:47:51.848 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-07 19:47:51.848 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/files/upload] with attributes [authenticated]
2025-07-07 19:47:51.849 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured GET /api/files/upload
2025-07-07 19:47:51.851 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : GET "/api/files/upload", parameters={multipart}
2025-07-07 19:47:59.069 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-07-07 19:47:59.071 [1;31mERROR[0;39m 16080 --- [http-nio-8080-exec-6] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:260)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-07 19:47:59.079 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-07 19:47:59.086 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-07-07T11:47:59.073 (truncated)...]
2025-07-07 19:47:59.094 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-07-07 19:47:59.158 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-07-07 19:47:59.160 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 19:48:29.220 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /api/files/upload
2025-07-07 19:48:29.220 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:48:29.222 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:48:29.304 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-07 19:48:29.337 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-07 19:48:29.337 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-07 19:48:29.337 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5140c087]
2025-07-07 19:48:29.337 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@879596365 wrapping com.mysql.cj.jdbc.ConnectionImpl@2a3bea03] will be managed by Spring
2025-07-07 19:48:29.337 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-07 19:48:29.338 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-07 19:48:29.340 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-07 19:48:29.340 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5140c087]
2025-07-07 19:48:29.340 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-07 19:48:29.340 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5140c087]
2025-07-07 19:48:29.340 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5140c087]
2025-07-07 19:48:29.340 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5140c087]
2025-07-07 19:48:29.342 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-07 19:48:29.343 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/files/upload] with attributes [authenticated]
2025-07-07 19:48:29.343 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /api/files/upload
2025-07-07 19:48:29.343 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : POST "/api/files/upload", parameters={multipart}
2025-07-07 19:48:37.865 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:48:37.892 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-07-07 19:48:37.892 [31mWARN [0;39m 16080 --- [http-nio-8080-exec-8] c.e.p.e.GlobalExceptionHandler : 业务异常: 文件大小超过限制，最大允许 1024 MB
2025-07-07 19:48:37.893 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-07 19:48:37.893 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=601, message=文件大小超过限制，最大允许 1024 MB, success=false, data=null, time=2025-07-07T11:48:37.8 (truncated)...]
2025-07-07 19:48:37.894 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 文件大小超过限制，最大允许 1024 MB]
2025-07-07 19:48:37.968 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-07-07 19:48:37.969 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 19:49:26.393 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing POST /api/files/upload
2025-07-07 19:49:26.393 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:49:26.394 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:49:26.474 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-07 19:49:26.477 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-07 19:49:26.477 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-07 19:49:26.477 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@96b29d3]
2025-07-07 19:49:26.478 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1785071630 wrapping com.mysql.cj.jdbc.ConnectionImpl@2a3bea03] will be managed by Spring
2025-07-07 19:49:26.478 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-07 19:49:26.478 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-07 19:49:26.479 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-07 19:49:26.480 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@96b29d3]
2025-07-07 19:49:26.480 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-07 19:49:26.480 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@96b29d3]
2025-07-07 19:49:26.480 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@96b29d3]
2025-07-07 19:49:26.480 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@96b29d3]
2025-07-07 19:49:26.483 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-07 19:49:26.483 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/files/upload] with attributes [authenticated]
2025-07-07 19:49:26.483 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured POST /api/files/upload
2025-07-07 19:49:26.483 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : POST "/api/files/upload", parameters={multipart}
2025-07-07 19:49:27.622 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:49:27.623 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-07-07 19:49:27.623 [31mWARN [0;39m 16080 --- [http-nio-8080-exec-10] c.e.p.e.GlobalExceptionHandler : 业务异常: 文件名不合法
2025-07-07 19:49:27.624 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-07 19:49:27.624 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=601, message=文件名不合法, success=false, data=null, time=2025-07-07T11:49:27.623268900Z)]
2025-07-07 19:49:27.625 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 文件名不合法]
2025-07-07 19:49:27.637 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-07-07 19:49:27.638 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 19:51:33.758 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/files/upload
2025-07-07 19:51:33.759 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:51:33.759 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:51:33.839 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-07 19:51:33.843 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-07 19:51:33.843 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-07 19:51:33.843 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b3f68dc]
2025-07-07 19:51:33.843 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1541498766 wrapping com.mysql.cj.jdbc.ConnectionImpl@2a3bea03] will be managed by Spring
2025-07-07 19:51:33.843 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-07 19:51:33.843 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-07 19:51:33.845 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-07 19:51:33.845 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b3f68dc]
2025-07-07 19:51:33.845 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-07 19:51:33.845 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b3f68dc]
2025-07-07 19:51:33.846 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b3f68dc]
2025-07-07 19:51:33.846 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b3f68dc]
2025-07-07 19:51:33.848 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-07 19:51:33.848 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/files/upload] with attributes [authenticated]
2025-07-07 19:51:33.848 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/files/upload
2025-07-07 19:51:33.849 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/files/upload", parameters={multipart}
2025-07-07 19:51:34.806 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:51:34.807 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-07-07 19:51:34.807 [31mWARN [0;39m 16080 --- [http-nio-8080-exec-2] c.e.p.e.GlobalExceptionHandler : 业务异常: 不支持的文件类型: application/x-msdos-program
2025-07-07 19:51:34.808 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-07 19:51:34.808 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=601, message=不支持的文件类型: application/x-msdos-program, success=false, data=null, time=2025- (truncated)...]
2025-07-07 19:51:34.809 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 不支持的文件类型: application/x-msdos-program]
2025-07-07 19:51:34.823 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-07-07 19:51:34.824 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 19:53:17.941 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing POST /api/files/upload
2025-07-07 19:53:17.941 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-07 19:53:17.942 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:53:18.024 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-07 19:53:18.027 [34mINFO [0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-07 19:53:18.027 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-07 19:53:18.027 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29869639]
2025-07-07 19:53:18.027 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1575950016 wrapping com.mysql.cj.jdbc.ConnectionImpl@2a3bea03] will be managed by Spring
2025-07-07 19:53:18.027 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-07 19:53:18.028 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-07 19:53:18.030 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-07 19:53:18.030 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29869639]
2025-07-07 19:53:18.030 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-07 19:53:18.030 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29869639]
2025-07-07 19:53:18.030 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29869639]
2025-07-07 19:53:18.030 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29869639]
2025-07-07 19:53:18.032 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-07 19:53:18.032 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/files/upload] with attributes [authenticated]
2025-07-07 19:53:18.032 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured POST /api/files/upload
2025-07-07 19:53:18.033 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : POST "/api/files/upload", parameters={multipart}
2025-07-07 19:53:20.045 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.file.user.FileManagerController#uploadFileAsync(MultipartFile, String)
2025-07-07 19:53:20.049 [39mDEBUG[0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.s.impl.FileUploadServiceImpl : 开始异步上传文件（带进度监控）: 18.mp4, 用户: Haohao268826, 类型: default
2025-07-07 19:53:20.152 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : 开始upload文件: 18.mp4 (288.16 MB)
2025-07-07 19:53:20.168 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 10.1% | 29.00 MB / 288.16 MB | 当前速度: 1812.50 MB/s | 平均速度: 1812.50 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.183 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 20.0% | 57.75 MB / 288.16 MB | 当前速度: 1796.88 MB/s | 平均速度: 1804.69 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.197 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 30.0% | 86.50 MB / 288.16 MB | 当前速度: 2053.57 MB/s | 平均速度: 1880.43 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.212 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 40.1% | 115.50 MB / 288.16 MB | 当前速度: 250.00 MB/s | 平均速度: 1893.44 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.226 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 50.1% | 144.25 MB / 288.16 MB | 当前速度: 2053.57 MB/s | 平均速度: 1923.33 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.240 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 60.0% | 173.00 MB / 288.16 MB | 当前速度: 2211.54 MB/s | 平均速度: 1965.91 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.254 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 70.0% | 201.75 MB / 288.16 MB | 当前速度: 250.00 MB/s | 平均速度: 1958.74 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.270 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 80.1% | 230.75 MB / 288.16 MB | 当前速度: 1812.50 MB/s | 平均速度: 1939.08 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.290 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 90.1% | 259.50 MB / 288.16 MB | 当前速度: 1437.50 MB/s | 平均速度: 1866.91 MB/s | 已用时间: 0秒 | 预计剩余: 0秒
2025-07-07 19:53:20.311 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload进度: 100.0% | 288.16 MB / 288.16 MB | 当前速度: 1433.19 MB/s | 平均速度: 1812.35 MB/s | 已用时间: 0秒 | 预计剩余: 计算中...
2025-07-07 19:53:20.311 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.m.LocalFileTransferProgressMonitor : upload完成: 18.mp4 | 总大小: 288.16 MB | 耗时: 0秒 | 平均速度: 1801.02 MB/s
2025-07-07 19:53:20.311 [39mDEBUG[0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.util.ChunkedFileTransferUtil : 文件传输完成: 18.mp4 -> 文件: D:\upload\Haohao268826\default\5ab37a6c-6711-467b-9506-0cd73a1693d6.mp4, 传输: 302161596 bytes, 耗时: 160 ms, 平均速度: 1801.02 MB/s
2025-07-07 19:53:20.311 [39mDEBUG[0;39m 16080 --- [ioIntensiveTask-async-1] c.e.pure.transfer.FileOutputTarget : 文件输出目标已关闭: 5ab37a6c-6711-467b-9506-0cd73a1693d6.mp4, 总写入: 302161596 bytes
2025-07-07 19:53:20.311 [34mINFO [0;39m 16080 --- [ioIntensiveTask-async-1] c.e.p.s.impl.FileUploadServiceImpl : 文件异步上传完成（带进度监控）: 18.mp4 -> 5ab37a6c-6711-467b-9506-0cd73a1693d6.mp4, 实际传输: 302161596 bytes
2025-07-07 19:53:20.313 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-07 19:53:20.313 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=文件上传请求已接收, success=true, data={path=D:\upload\Haohao268826\default\5ab37a6c (truncated)...]
2025-07-07 19:53:20.342 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-07 19:53:20.342 [39mDEBUG[0;39m 16080 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-07 20:14:33.045 [34mINFO [0;39m 16080 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-07 20:44:33.049 [34mINFO [0;39m 16080 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-07-07 21:14:33.056 [34mINFO [0;39m 16080 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-07-07 21:44:33.071 [34mINFO [0;39m 16080 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-07-07 22:14:33.072 [34mINFO [0;39m 16080 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-07-07 22:44:33.087 [34mINFO [0;39m 16080 --- [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-07-07 23:14:33.088 [34mINFO [0;39m 16080 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-07-07 23:43:54.675 [31mWARN [0;39m 16080 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=27m20s895ms518µs600ns).
