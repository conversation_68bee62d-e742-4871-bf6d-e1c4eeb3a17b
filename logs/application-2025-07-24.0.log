2025-07-24 03:29:42.836 [34mINFO [0;39m 2572 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-24 03:29:42.838 [34mINFO [0;39m 2572 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 2572 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-07-24 03:29:42.838 [39mDEBUG[0;39m 2572 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-24 03:29:42.838 [34mINFO [0;39m 2572 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-24 03:29:43.859 [34mINFO [0;39m 2572 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-24 03:29:43.862 [34mINFO [0;39m 2572 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-24 03:29:43.909 [34mINFO [0;39m 2572 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-24 03:29:44.016 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-24 03:29:44.017 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-24 03:29:44.019 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-24 03:29:44.019 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-07-24 03:29:44.019 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-07-24 03:29:44.019 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-07-24 03:29:44.019 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-07-24 03:29:44.019 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-24 03:29:44.020 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-24 03:29:44.021 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-24 03:29:44.022 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-24 03:29:44.022 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-07-24 03:29:44.022 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-07-24 03:29:44.022 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-07-24 03:29:44.022 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-07-24 03:29:44.022 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-24 03:29:44.023 [39mDEBUG[0;39m 2572 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-24 03:29:44.789 [34mINFO [0;39m 2572 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-24 03:29:44.797 [34mINFO [0;39m 2572 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 03:29:44.799 [34mINFO [0;39m 2572 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-24 03:29:44.799 [34mINFO [0;39m 2572 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-24 03:29:44.906 [34mINFO [0;39m 2572 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-24 03:29:44.907 [34mINFO [0;39m 2572 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2026 ms
2025-07-24 03:29:45.212 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-24 03:29:45.234 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-07-24 03:29:45.244 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-07-24 03:29:45.252 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-24 03:29:45.257 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-24 03:29:45.262 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-24 03:29:45.271 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-24 03:29:45.276 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-24 03:29:45.281 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-24 03:29:45.285 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-24 03:29:45.290 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-24 03:29:45.295 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-24 03:29:45.301 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-07-24 03:29:45.307 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-07-24 03:29:45.316 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-07-24 03:29:45.321 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-24 03:29:45.324 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-24 03:29:45.329 [39mDEBUG[0;39m 2572 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-24 03:29:45.343 [34mINFO [0;39m 2572 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-24 03:29:45.733 [34mINFO [0;39m 2572 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-24 03:29:46.330 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-24 03:29:46.330 [39mDEBUG[0;39m 2572 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-24 03:29:46.673 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-24 03:29:46.675 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.212 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-24 03:29:47.448 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-24 03:29:47.456 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-24 03:29:47.548 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-24 03:29:47.548 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.549 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-24 03:29:47.549 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.550 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-24 03:29:47.550 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.550 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-24 03:29:47.551 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.551 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-24 03:29:47.551 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.552 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-24 03:29:47.552 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.557 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-24 03:29:47.558 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.558 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-24 03:29:47.558 [39mDEBUG[0;39m 2572 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-24 03:29:47.705 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-24 03:29:47.706 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-24 03:29:47.710 [34mINFO [0;39m 2572 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@3be2ef56, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a8149f6, org.springframework.security.web.context.SecurityContextPersistenceFilter@22ea09b7, org.springframework.security.web.header.HeaderWriterFilter@20bf7206, org.springframework.security.web.authentication.logout.LogoutFilter@7bbe5548, com.example.pure.filter.JwtFilter@4abdd5e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@36a7586f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4265cbed, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@672549f3, org.springframework.security.web.session.SessionManagementFilter@3a641f6, org.springframework.security.web.access.ExceptionTranslationFilter@30a99b85, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@60cbba57]
2025-07-24 03:29:47.712 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-24 03:29:47.714 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-24 03:29:47.715 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-24 03:29:47.715 [34mINFO [0;39m 2572 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-24 03:29:47.913 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-24 03:29:47.937 [34mINFO [0;39m 2572 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-24 03:29:48.027 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 103 mappings in 'requestMappingHandlerMapping'
2025-07-24 03:29:48.036 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-24 03:29:48.429 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-24 03:29:48.554 [34mINFO [0;39m 2572 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-24 03:29:48.576 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-24 03:29:48.576 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-24 03:29:48.577 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-24 03:29:48.578 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-07-24 03:29:48.579 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-24 03:29:48.579 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-24 03:29:48.579 [39mDEBUG[0;39m 2572 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-24 03:29:48.579 [34mINFO [0;39m 2572 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50508ed3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d319d2c, org.springframework.security.web.context.SecurityContextPersistenceFilter@25bd68ea, org.springframework.security.web.header.HeaderWriterFilter@42fc744, org.springframework.web.filter.CorsFilter@23acd55e, org.springframework.security.web.authentication.logout.LogoutFilter@7ae75ba6, com.example.pure.filter.JwtFilter@4abdd5e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7915111a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@37aec9b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@eaf3dd0, org.springframework.security.web.session.SessionManagementFilter@3db145f7, org.springframework.security.web.access.ExceptionTranslationFilter@2b3ee95e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@557f0a39]
2025-07-24 03:29:48.617 [39mTRACE[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5733f295, started on Thu Jul 24 03:29:42 CST 2025
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-07-24 03:29:48.632 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-07-24 03:29:48.633 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-07-24 03:29:48.636 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
2025-07-24 03:29:48.637 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-07-24 03:29:48.637 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-07-24 03:29:48.637 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-07-24 03:29:48.637 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-07-24 03:29:48.639 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-24 03:29:48.639 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-24 03:29:48.639 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-24 03:29:48.639 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-24 03:29:48.740 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-24 03:29:48.776 [39mDEBUG[0;39m 2572 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-24 03:29:49.028 [34mINFO [0;39m 2572 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 03:29:49.040 [34mINFO [0;39m 2572 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-24 03:29:49.040 [39mDEBUG[0;39m 2572 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-24 03:29:49.041 [39mDEBUG[0;39m 2572 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-24 03:29:49.041 [34mINFO [0;39m 2572 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-24 03:29:49.041 [39mDEBUG[0;39m 2572 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@c358f32]
2025-07-24 03:29:49.041 [39mDEBUG[0;39m 2572 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@c358f32]
2025-07-24 03:29:49.041 [34mINFO [0;39m 2572 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@c358f32]]
2025-07-24 03:29:49.041 [34mINFO [0;39m 2572 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-24 03:29:49.041 [39mDEBUG[0;39m 2572 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-24 03:29:49.041 [39mDEBUG[0;39m 2572 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-24 03:29:49.055 [34mINFO [0;39m 2572 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.656 seconds (JVM running for 7.773)
2025-07-24 03:30:48.601 [34mINFO [0;39m 2572 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-24 03:41:12.028 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 03:41:12.028 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-24 03:41:12.028 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-24 03:41:12.028 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-24 03:41:12.028 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-24 03:41:12.030 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1e48cfd5
2025-07-24 03:41:12.030 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@7b5dcebc
2025-07-24 03:41:12.030 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-24 03:41:12.030 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-07-24 03:41:12.044 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-24 03:41:12.049 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-24 03:41:12.063 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-24 03:41:12.068 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 03:41:12.068 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-24 03:41:12.077 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-07-24 03:41:12.078 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-24 03:41:12.081 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-07-24 03:41:12.083 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-24 03:41:12.168 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: java简单代码一个例子生成
2025-07-24 03:41:12.176 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=user, content (truncated)...]
2025-07-24 03:41:12.251 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.OpenAiRequestServiceImpl : 收到OpenAI兼容聊天请求 - 模型: gemini-2.5-pro, 流式: true, 消息数: 1
2025-07-24 03:41:12.252 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.OpenAiRequestServiceImpl : 处理流式聊天请求 - 模型: gemini-2.5-pro
2025-07-24 03:41:12.252 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 处理OpenAI兼容流式聊天请求 - 模型: gemini-2.5-pro
2025-07-24 03:41:12.261 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:41:12.264 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ae653] was not registered for synchronization because synchronization is not active
2025-07-24 03:41:12.274 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@139912285 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:41:12.277 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectById : ==>  Preparing: SELECT id, user_id, provider, key_name, api_key_encrypted, is_active, priority, usage_count, last_used_at, created_at, updated_at FROM user_api_keys WHERE id = ?
2025-07-24 03:41:12.308 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectById : ==> Parameters: 3(Long)
2025-07-24 03:41:12.342 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectById : <==      Total: 1
2025-07-24 03:41:12.344 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ae653]
2025-07-24 03:41:12.345 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始异步处理流式聊天 - 用户ID: 1, 密钥ID: 3
2025-07-24 03:41:12.345 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.impl.AiConfigServiceImpl : 获取用户AI配置 - 用户ID: 1
2025-07-24 03:41:12.345 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:41:12.345 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@725c901c] was not registered for synchronization because synchronization is not active
2025-07-24 03:41:12.346 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1667670563 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:41:12.346 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config WHERE user_id = ?
2025-07-24 03:41:12.346 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-07-24 03:41:12.350 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-07-24 03:41:12.351 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@725c901c]
2025-07-24 03:41:12.353 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择最佳API密钥 - 用户ID: 1, 提供商: GOOGLE
2025-07-24 03:41:12.354 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:41:12.354 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29971e5c] was not registered for synchronization because synchronization is not active
2025-07-24 03:41:12.354 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@372384127 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:41:12.354 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, provider, key_name, api_key_encrypted, is_active, priority, usage_count, last_used_at, created_at, updated_at FROM user_api_keys WHERE user_id = ? AND provider = ? AND is_active = TRUE ORDER BY priority ASC, created_at ASC
2025-07-24 03:41:12.355 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-07-24 03:41:12.362 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.U.selectActiveByUserIdAndProvider : <==      Total: 2
2025-07-24 03:41:12.362 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29971e5c]
2025-07-24 03:41:12.362 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:41:12.362 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4de3f3b2] was not registered for synchronization because synchronization is not active
2025-07-24 03:41:12.363 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@444903948 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:41:12.363 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, provider, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE user_id = ? AND provider = ? AND is_healthy = TRUE ORDER BY current_requests ASC, total_requests ASC
2025-07-24 03:41:12.363 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-07-24 03:41:12.367 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : <==      Total: 2
2025-07-24 03:41:12.368 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4de3f3b2]
2025-07-24 03:41:12.368 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择API密钥 - ID: 3, 当前请求数: 0
2025-07-24 03:41:12.369 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.pure.util.MultimodalContentUtils : 纯文本消息 - 长度: 14 字符
2025-07-24 03:41:12.375 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.LoadBalancerServiceImpl : 开始使用API密钥 - ID: 3
2025-07-24 03:41:12.375 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:41:12.375 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2941af2]
2025-07-24 03:41:12.377 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@970626017 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will be managed by Spring
2025-07-24 03:41:12.377 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.A.incrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = current_requests + 1, total_requests = total_requests + 1, updated_at = NOW() WHERE api_key_id = ?
2025-07-24 03:41:12.377 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.A.incrementRequests : ==> Parameters: 3(Long)
2025-07-24 03:41:12.383 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.m.p.A.incrementRequests : <==    Updates: 1
2025-07-24 03:41:12.383 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2941af2]
2025-07-24 03:41:12.384 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2941af2]
2025-07-24 03:41:12.384 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2941af2]
2025-07-24 03:41:12.384 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2941af2]
2025-07-24 03:41:12.406 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-c1ca0477, 数据大小: 232 bytes
2025-07-24 03:41:12.407 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-07-24 03:41:12.407 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 流式请求详情 ===
2025-07-24 03:41:12.407 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-24 03:41:12.407 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-07-24 03:41:12.407 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {top_p=1.0, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=user, content=java简单代码一个例子生成}], model=gemini-2.5-pro}
2025-07-24 03:41:12.444 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-24 03:41:12.444 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-1] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始流式聊天 - 响应ID: chatcmpl-c1ca0477, 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-07-24 03:41:12.462 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.w.r.f.client.ExchangeFunctions : [1a0ec5a2] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-24 03:41:12.648 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-24 03:41:12.653 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-24 03:41:12.653 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-24 03:41:13.453 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [1a0ec5a2] Encoding [{top_p=1.0, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=user, content=java简单代码一个例 (truncated)...]
2025-07-24 03:41:34.193 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [1a0ec5a2] [edc7045e-1] Response 200 OK
2025-07-24 03:41:34.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"好的，这是一个非常适合初学者的简单Java代码示例。","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"好的，这是一个非常适合初学者的简单Java代码示例。","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 203, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:34.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 203 bytes
2025-07-24 03:41:34.490 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n这个例子将涵盖Java最基本的一些元素：\n1.  **类 (Class)**：Java程序的基本构建","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.490 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n这个例子将涵盖Java最基本的一些元素：\n1.  **类 (Class)**：Java程序的基本构建","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.490 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 232, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:34.490 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 232 bytes
2025-07-24 03:41:34.771 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"块。\n2.  **主方法 (main method)**：程序的入口点。\n3.  **打印输出到","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.771 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"块。\n2.  **主方法 (main method)**：程序的入口点。\n3.  **打印输出到","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.771 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 227, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:34.771 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 227 bytes
2025-07-24 03:41:34.969 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"控制台**：显示信息。\n4.  **变量 (Variables)**：存储数据。\n5.  **简单的","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.969 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"控制台**：显示信息。\n4.  **变量 (Variables)**：存储数据。\n5.  **简单的","role":"assistant"},"index":0}],"created":1753299694,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:34.970 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 229, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:34.970 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 229 bytes
2025-07-24 03:41:35.245 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"条件语句 (if-else)**：根据条件执行不同的代码。\n\n---\n\n### 示例代码：`HelloWorld","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.245 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"条件语句 (if-else)**：根据条件执行不同的代码。\n\n---\n\n### 示例代码：`HelloWorld","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.245 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 237, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:35.245 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 237 bytes
2025-07-24 03:41:35.521 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":".java`\n\n```java\n// 定义一个公开的类，类名必须和文件名相同（这里是 HelloWorld）","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.521 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":".java`\n\n```java\n// 定义一个公开的类，类名必须和文件名相同（这里是 HelloWorld）","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.521 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 234, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:35.521 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 234 bytes
2025-07-24 03:41:35.789 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\npublic class HelloWorld {\n\n    // 这是程序的主方法，程序从这里开始执行\n    public static void main(String[]","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.789 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\npublic class HelloWorld {\n\n    // 这是程序的主方法，程序从这里开始执行\n    public static void main(String[]","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.789 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 271, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:35.789 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 271 bytes
2025-07-24 03:41:35.983 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" args) {\n        \n        // 1. 在控制台打印 \"Hello, World!\"\n        System.","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.983 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" args) {\n        \n        // 1. 在控制台打印 \"Hello, World!\"\n        System.","role":"assistant"},"index":0}],"created":1753299695,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:35.984 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 252, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:35.984 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 252 bytes
2025-07-24 03:41:36.282 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"out.println(\"Hello, World!\");\n\n        // 2. 声明并初始化一个整型变量\n        int my","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:36.282 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"out.println(\"Hello, World!\");\n\n        // 2. 声明并初始化一个整型变量\n        int my","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:36.282 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 254, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:36.282 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 254 bytes
2025-07-24 03:41:36.537 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"Number = 10;\n        \n        // 3. 声明一个字符串变量\n        String myName = \"Java","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:36.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"Number = 10;\n        \n        // 3. 声明一个字符串变量\n        String myName = \"Java","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:36.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 256, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:36.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 256 bytes
2025-07-24 03:41:36.812 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\";\n\n        // 4. 将变量和字符串拼接在一起输出\n        System.out.println(\"我的名字是 \"","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:36.812 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\";\n\n        // 4. 将变量和字符串拼接在一起输出\n        System.out.println(\"我的名字是 \"","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:36.812 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 251, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:36.812 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 251 bytes
2025-07-24 03:41:37.068 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" + myName + \"，我的幸运数字是 \" + myNumber);\n\n        // 5. 使用 if-else 条件","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.068 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" + myName + \"，我的幸运数字是 \" + myNumber);\n\n        // 5. 使用 if-else 条件","role":"assistant"},"index":0}],"created":1753299696,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.068 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 246, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:37.068 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 246 bytes
2025-07-24 03:41:37.379 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"语句进行判断\n        if (myNumber > 5) {\n            // 如果 myNumber 大于 5，执行","role":"assistant"},"index":0}],"created":1753299697,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.379 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"语句进行判断\n        if (myNumber > 5) {\n            // 如果 myNumber 大于 5，执行","role":"assistant"},"index":0}],"created":1753299697,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.380 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 248, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:37.380 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 248 bytes
2025-07-24 03:41:37.662 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"这里的代码\n            System.out.println(\"幸运数字大于5，今天会是美好的一天！\");\n        } else","role":"assistant"},"index":0}],"created":1753299697,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.663 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"这里的代码\n            System.out.println(\"幸运数字大于5，今天会是美好的一天！\");\n        } else","role":"assistant"},"index":0}],"created":1753299697,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.663 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 255, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:37.663 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 255 bytes
2025-07-24 03:41:37.853 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" {\n            // 否则，执行这里的代码\n            System.out.println(\"幸运数字不大于5。","role":"assistant"},"index":0}],"created":1753299697,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.853 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" {\n            // 否则，执行这里的代码\n            System.out.println(\"幸运数字不大于5。","role":"assistant"},"index":0}],"created":1753299697,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:37.853 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 250, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:37.853 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 250 bytes
2025-07-24 03:41:38.167 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\");\n        }\n\n        // 6. 调用一个简单的方法\n        sayGoodbye();\n    }\n\n    /**","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.167 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\");\n        }\n\n        // 6. 调用一个简单的方法\n        sayGoodbye();\n    }\n\n    /**","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.168 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 260, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:38.168 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 260 bytes
2025-07-24 03:41:38.631 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n     * 这是一个简单的方法，用于打印告别信息\n     */\n    public static void sayGoodbye() {\n        ","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.631 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n     * 这是一个简单的方法，用于打印告别信息\n     */\n    public static void sayGoodbye() {\n        ","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.631 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 262, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:38.631 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 262 bytes
2025-07-24 03:41:38.633 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"System.out.println(\"程序结束，再见！\");\n    }\n}\n```\n\n### 代码讲解\n\n","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.633 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"System.out.println(\"程序结束，再见！\");\n    }\n}\n```\n\n### 代码讲解\n\n","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.634 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 241, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:38.634 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 241 bytes
2025-07-24 03:41:38.816 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"1.  **`public class HelloWorld`**\n    *   `public`: 这是一个访问修饰符，表示这个","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.816 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"1.  **`public class HelloWorld`**\n    *   `public`: 这是一个访问修饰符，表示这个","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:38.816 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 244, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:38.816 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 244 bytes
2025-07-24 03:41:39.046 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"类可以被任何其他类访问。\n    *   `class`: 关键字，用来定义一个类。\n","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.046 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"类可以被任何其他类访问。\n    *   `class`: 关键字，用来定义一个类。\n","role":"assistant"},"index":0}],"created":1753299698,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.046 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 222, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:39.046 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 222 bytes
2025-07-24 03:41:39.326 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"    *   `HelloWorld`: 这是类的名称。**在Java中，文件名必须与公共类（public class）的","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.326 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"    *   `HelloWorld`: 这是类的名称。**在Java中，文件名必须与公共类（public class）的","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.326 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 239, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:39.326 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 239 bytes
2025-07-24 03:41:39.564 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"名称完全相同**，所以这个文件必须保存为 `HelloWorld.java`。\n\n2.  **`public static","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.564 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"名称完全相同**，所以这个文件必须保存为 `HelloWorld.java`。\n\n2.  **`public static","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.564 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 240, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:39.564 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 240 bytes
2025-07-24 03:41:39.869 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" void main(String[] args)`**\n    *   这是Java程序的**主方法**，也就是程序的起点。当你","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.869 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" void main(String[] args)`**\n    *   这是Java程序的**主方法**，也就是程序的起点。当你","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:39.869 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 243, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:39.869 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 243 bytes
2025-07-24 03:41:40.076 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"运行这个Java程序时，代码会从这里开始执行。\n    *   `static`: 表明这个方法属于","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.076 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"运行这个Java程序时，代码会从这里开始执行。\n    *   `static`: 表明这个方法属于","role":"assistant"},"index":0}],"created":1753299699,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.076 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 228, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:40.076 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 228 bytes
2025-07-24 03:41:40.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"类本身，而不是类的实例。\n    *   `void`: 表明这个方法没有返回值。\n    *   ","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"类本身，而不是类的实例。\n    *   `void`: 表明这个方法没有返回值。\n    *   ","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 229, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:40.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 229 bytes
2025-07-24 03:41:40.584 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"`String[] args`: 用来接收命令行参数。\n\n3.  **`System.out.println(\"...\")`**","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.584 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"`String[] args`: 用来接收命令行参数。\n\n3.  **`System.out.println(\"...\")`**","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.584 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 245, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:40.584 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 245 bytes
2025-07-24 03:41:40.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    *   这是一个非常常用的命令，用于将括号内的内容打印到控制台，并换行。\n\n4","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    *   这是一个非常常用的命令，用于将括号内的内容打印到控制台，并换行。\n\n4","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:40.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 224, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:40.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 224 bytes
2025-07-24 03:41:41.038 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":".  **`int myNumber = 10;`** 和 **`String myName = \"Java\";","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.039 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":".  **`int myNumber = 10;`** 和 **`String myName = \"Java\";","role":"assistant"},"index":0}],"created":1753299700,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.039 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 235, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:41.039 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 235 bytes
2025-07-24 03:41:41.300 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"`**\n    *   这叫做**变量声明和初始化**。\n    *   `int`: 表示 `","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"`**\n    *   这叫做**变量声明和初始化**。\n    *   `int`: 表示 `","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 227, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:41.301 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 227 bytes
2025-07-24 03:41:41.547 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"myNumber` 是一个整数（integer）类型的变量。\n    *   `String`: 表示 `myName` 是一个字符串","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.547 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"myNumber` 是一个整数（integer）类型的变量。\n    *   `String`: 表示 `myName` 是一个字符串","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.547 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 245, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:41.547 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 245 bytes
2025-07-24 03:41:41.821 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"（String）类型的变量。\n    *   `=` 是赋值操作符，将右边的值赋给左边的变量。","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.821 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"（String）类型的变量。\n    *   `=` 是赋值操作符，将右边的值赋给左边的变量。","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:41.821 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 225, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:41.821 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 225 bytes
2025-07-24 03:41:42.062 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n5.  **`if (myNumber > 5) { ... } else { ... }`**\n    *","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.062 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n5.  **`if (myNumber > 5) { ... } else { ... }`**\n    *","role":"assistant"},"index":0}],"created":1753299701,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.062 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 236, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:42.062 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 236 bytes
2025-07-24 03:41:42.368 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"   这是一个**条件语句**。\n    *   程序会检查 `if` 后括号里的条件（`myNumber >","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.368 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"   这是一个**条件语句**。\n    *   程序会检查 `if` 后括号里的条件（`myNumber >","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.368 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 233, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:42.368 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 233 bytes
2025-07-24 03:41:42.554 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 5`）是否为真。\n    *   如果为真，就执行 `if` 大括号 `{","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.555 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 5`）是否为真。\n    *   如果为真，就执行 `if` 大括号 `{","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.555 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 216, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:42.555 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 216 bytes
2025-07-24 03:41:42.804 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"}` 里的代码。\n    *   如果为假，就执行 `else` 大括号 `{}` 里的代码。","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.804 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"}` 里的代码。\n    *   如果为假，就执行 `else` 大括号 `{}` 里的代码。","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:42.804 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 225, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:42.804 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 225 bytes
2025-07-24 03:41:43.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n6.  **`public static void sayGoodbye()`**\n    *   这是一个我们自己定义的**方法**。","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:43.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n6.  **`public static void sayGoodbye()`**\n    *   这是一个我们自己定义的**方法**。","role":"assistant"},"index":0}],"created":1753299702,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:43.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 250, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:43.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 250 bytes
2025-07-24 03:41:43.350 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"它可以把一些代码打包起来，方便重复调用。\n    *   在 `main` 方法的最后，我们通过 `","role":"assistant"},"index":0}],"created":1753299703,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:43.350 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"它可以把一些代码打包起来，方便重复调用。\n    *   在 `main` 方法的最后，我们通过 `","role":"assistant"},"index":0}],"created":1753299703,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:43.350 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 228, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:43.350 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 228 bytes
2025-07-24 03:41:43.643 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"sayGoodbye();` 来调用它。\n\n### 如何运行这个程序\n\n1.  **安装Java开发工具包 (JDK)**","role":"assistant"},"index":0}],"created":1753299703,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:43.643 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"sayGoodbye();` 来调用它。\n\n### 如何运行这个程序\n\n1.  **安装Java开发工具包 (JDK)**","role":"assistant"},"index":0}],"created":1753299703,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:43.643 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 242, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:43.643 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 242 bytes
2025-07-24 03:41:44.067 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"：如果你的电脑上还没有安装，需要先去Oracle官网或采用OpenJDK来安装JDK。\n\n2.  **","role":"assistant"},"index":0}],"created":1753299703,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.067 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"：如果你的电脑上还没有安装，需要先去Oracle官网或采用OpenJDK来安装JDK。\n\n2.  **","role":"assistant"},"index":0}],"created":1753299703,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.067 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 230, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:44.067 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 230 bytes
2025-07-24 03:41:44.454 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"保存代码**：将上面的代码复制到一个文本编辑器（如记事本、VS Code、IntelliJ IDEA","role":"assistant"},"index":0}],"created":1753299704,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.454 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"保存代码**：将上面的代码复制到一个文本编辑器（如记事本、VS Code、IntelliJ IDEA","role":"assistant"},"index":0}],"created":1753299704,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.454 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 227, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:44.454 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 227 bytes
2025-07-24 03:41:44.675 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"等）中，并将文件保存为 `HelloWorld.java`。**文件名必须和类名完全一致！**\n\n3","role":"assistant"},"index":0}],"created":1753299704,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.675 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"等）中，并将文件保存为 `HelloWorld.java`。**文件名必须和类名完全一致！**\n\n3","role":"assistant"},"index":0}],"created":1753299704,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.675 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 229, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:44.675 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 229 bytes
2025-07-24 03:41:44.930 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":".  **打开命令行/终端**：\n    *   Windows: 打开 `cmd` 或 `PowerShell`。","role":"assistant"},"index":0}],"created":1753299704,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.930 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":".  **打开命令行/终端**：\n    *   Windows: 打开 `cmd` 或 `PowerShell`。","role":"assistant"},"index":0}],"created":1753299704,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:44.930 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 236, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:44.930 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 236 bytes
2025-07-24 03:41:45.160 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    *   macOS/Linux: 打开 `终端 (Terminal)`。\n\n4.  **导航到文件","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.160 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    *   macOS/Linux: 打开 `终端 (Terminal)`。\n\n4.  **导航到文件","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.160 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 234, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:45.160 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 234 bytes
2025-07-24 03:41:45.416 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"目录**：使用 `cd` 命令切换到你保存 `HelloWorld.java` 文件的目录。例如：\n    ```bash","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.416 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"目录**：使用 `cd` 命令切换到你保存 `HelloWorld.java` 文件的目录。例如：\n    ```bash","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.416 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 239, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:45.416 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 239 bytes
2025-07-24 03:41:45.670 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    cd C:\\Users\\<USER>\\Desktop\n    ```\n\n5.  **编译 (Compile)**：在","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.671 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    cd C:\\Users\\<USER>\\Desktop\n    ```\n\n5.  **编译 (Compile)**：在","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.671 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 249, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:45.671 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 249 bytes
2025-07-24 03:41:45.980 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"命令行中输入以下命令，将 `.java` 源文件编译成 `.class` 字节码文件。\n    ```bash","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.980 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"命令行中输入以下命令，将 `.java` 源文件编译成 `.class` 字节码文件。\n    ```bash","role":"assistant"},"index":0}],"created":1753299705,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:45.980 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 233, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:45.980 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 233 bytes
2025-07-24 03:41:46.228 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    javac HelloWorld.java\n    ```\n    如果没有任何错误，你会在同一目录下看到一个新生成的文件：","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:46.228 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    javac HelloWorld.java\n    ```\n    如果没有任何错误，你会在同一目录下看到一个新生成的文件：","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:46.228 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 247, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:46.228 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 247 bytes
2025-07-24 03:41:46.474 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"`HelloWorld.class`。\n\n6.  **运行 (Run)**：在命令行中输入以下命令来运行你的","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:46.474 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"`HelloWorld.class`。\n\n6.  **运行 (Run)**：在命令行中输入以下命令来运行你的","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:46.474 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 233, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:46.474 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 233 bytes
2025-07-24 03:41:46.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"程序。注意，这里不需要写 `.class` 后缀。\n    ```bash\n    java HelloWorld\n    ```\n\n###","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:46.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"程序。注意，这里不需要写 `.class` 后缀。\n    ```bash\n    java HelloWorld\n    ```\n\n###","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:46.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 252, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:46.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 252 bytes
2025-07-24 03:41:47.019 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 预期输出\n\n运行后，你会在控制台看到以下输出：\n\n```\nHello, World!\n我的名字是","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:47.019 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 预期输出\n\n运行后，你会在控制台看到以下输出：\n\n```\nHello, World!\n我的名字是","role":"assistant"},"index":0}],"created":1753299706,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:47.019 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 232, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:47.019 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 232 bytes
2025-07-24 03:41:47.253 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" Java，我的幸运数字是 10\n幸运数字大于5，今天会是美好的一天！\n程序结束，再","role":"assistant"},"index":0}],"created":1753299707,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:47.253 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" Java，我的幸运数字是 10\n幸运数字大于5，今天会是美好的一天！\n程序结束，再","role":"assistant"},"index":0}],"created":1753299707,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:47.253 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 221, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:47.253 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 221 bytes
2025-07-24 03:41:47.479 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"见！\n```","role":"assistant"},"finish_reason":"stop","index":0}],"created":1753299707,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:47.479 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"见！\n```","role":"assistant"},"finish_reason":"stop","index":0}],"created":1753299707,"id":"2zqBaLWpBdHQz7IPy42m0Qw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:41:47.479 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 207, 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:47.479 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 207 bytes
2025-07-24 03:41:47.491 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: [DONE]
2025-07-24 03:41:47.491 [31mWARN [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 过滤掉非数据行: [DONE]
2025-07-24 03:41:47.493 [34mINFO [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求完成
2025-07-24 03:41:47.493 [34mINFO [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式响应正常完成 - 响应ID: chatcmpl-c1ca0477, 模型: gemini-2.5-pro
2025-07-24 03:41:47.495 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-c1ca0477, 数据大小: 281 bytes
2025-07-24 03:41:47.496 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE完成事件: [DONE]
2025-07-24 03:41:47.496 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /v1/chat/completions
2025-07-24 03:41:47.497 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : SseEmitter安全完成 - 响应ID: chatcmpl-c1ca0477
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 结束使用API密钥 - ID: 3, 成功: true
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b]
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1560023031 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will be managed by Spring
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = GREATEST(current_requests - 1, 0), updated_at = NOW() WHERE api_key_id = ?
2025-07-24 03:41:47.499 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : ==> Parameters: 3(Long)
2025-07-24 03:41:47.500 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-24 03:41:47.504 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-24 03:41:47.505 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-24 03:41:47.505 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-24 03:41:47.513 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : <==    Updates: 1
2025-07-24 03:41:47.513 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b]
2025-07-24 03:41:47.513 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b] from current transaction
2025-07-24 03:41:47.514 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : ==>  Preparing: UPDATE user_api_keys SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?
2025-07-24 03:41:47.514 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : ==> Parameters: 3(Long)
2025-07-24 03:41:47.516 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : <==    Updates: 1
2025-07-24 03:41:47.516 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b]
2025-07-24 03:41:47.516 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b]
2025-07-24 03:41:47.516 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b]
2025-07-24 03:41:47.516 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@19f89c8b]
2025-07-24 03:41:47.523 [34mINFO [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式聊天完成 - 响应ID: chatcmpl-c1ca0477, API密钥ID: 3
2025-07-24 03:44:19.551 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-24 03:44:19.551 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-24 03:44:19.551 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-24 03:44:19.551 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 03:44:19.551 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-24 03:44:19.552 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-07-24 03:44:19.552 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-24 03:44:19.552 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-07-24 03:44:19.552 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-24 03:44:19.553 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] c.e.pure.config.ContentDeserializer : 反序列化数组格式内容
2025-07-24 03:44:19.555 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=user, content (truncated)...]
2025-07-24 03:44:19.556 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-6] c.e.p.s.o.i.OpenAiRequestServiceImpl : 收到OpenAI兼容聊天请求 - 模型: gemini-2.5-pro, 流式: true, 消息数: 1
2025-07-24 03:44:19.556 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] c.e.p.s.o.i.OpenAiRequestServiceImpl : 处理流式聊天请求 - 模型: gemini-2.5-pro
2025-07-24 03:44:19.556 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-6] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 处理OpenAI兼容流式聊天请求 - 模型: gemini-2.5-pro
2025-07-24 03:44:19.557 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-24 03:44:19.557 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.c.r.async.WebAsyncManager : Async error, dispatch to /v1/chat/completions
2025-07-24 03:44:19.557 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-24 03:44:19.557 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-24 03:44:19.557 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-24 03:44:19.557 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-24 03:44:19.558 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 03:44:19.558 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-24 03:44:19.558 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-07-24 03:44:19.558 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result [java.lang.RuntimeException: Invalid API key: Invalid API key format]
2025-07-24 03:44:19.560 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-07-24 03:44:19.564 [1;31mERROR[0;39m 2572 --- [http-nio-8080-exec-6] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.RuntimeException: Invalid API key: Invalid API key format
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.sendErrorAndComplete(OpenAiCompatibleServiceImpl.java:850)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:101)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy150.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-24 03:44:19.567 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-24 03:44:19.568 [31mWARN [0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.example.pure.common.Result] with preset Content-Type 'text/event-stream'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:106)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:87)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.doDispatch(ApplicationDispatcher.java:589)
	at org.apache.catalina.core.ApplicationDispatcher.dispatch(ApplicationDispatcher.java:558)
	at org.apache.catalina.core.AsyncContextImpl$AsyncRunnable.run(AsyncContextImpl.java:569)
	at org.apache.catalina.core.AsyncContextImpl.doInternalDispatch(AsyncContextImpl.java:339)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:166)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:237)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-24 03:44:19.568 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Unresolved failure from "ASYNC" dispatch: java.lang.RuntimeException: Invalid API key: Invalid API key format
2025-07-24 03:44:19.569 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-24 03:44:19.569 [1;31mERROR[0;39m 2572 --- [http-nio-8080-exec-6] o.a.c.c.C.[.[.[.[dispatcherServlet] : Servlet.service() for servlet [dispatcherServlet] threw exception
java.lang.RuntimeException: Invalid API key: Invalid API key format
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.sendErrorAndComplete(OpenAiCompatibleServiceImpl.java:850)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:101)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy150.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-24 03:44:19.570 [1;31mERROR[0;39m 2572 --- [http-nio-8080-exec-6] o.a.c.c.C.[.[.[.[dispatcherServlet] : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.RuntimeException: Invalid API key: Invalid API key format] with root cause
java.lang.RuntimeException: Invalid API key: Invalid API key format
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.sendErrorAndComplete(OpenAiCompatibleServiceImpl.java:850)
	at com.example.pure.service.openai.impl.OpenAiCompatibleServiceImpl.streamChatCompletions(OpenAiCompatibleServiceImpl.java:101)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at jdk.proxy2/jdk.proxy2.$Proxy150.streamChatCompletions(Unknown Source)
	at com.example.pure.service.openai.impl.OpenAiRequestServiceImpl.processChatCompletions(OpenAiRequestServiceImpl.java:40)
	at com.example.pure.controller.openai.OpenAiCompatibleController.chatCompletions(OpenAiCompatibleController.java:103)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$FastClassBySpringCGLIB$$9755d011.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.openai.OpenAiCompatibleController$$EnhancerBySpringCGLIB$$e557d243.chatCompletions(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-24 03:44:19.572 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : "INCLUDE" dispatch for POST "/error", parameters={}
2025-07-24 03:44:19.574 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)
2025-07-24 03:44:19.576 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-24 03:44:19.576 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-07-24 03:44:19.576 [1;31mERROR[0;39m 2572 --- [http-nio-8080-exec-6] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'text/event-stream'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.doInclude(ApplicationDispatcher.java:537)
	at org.apache.catalina.core.ApplicationDispatcher.include(ApplicationDispatcher.java:480)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:358)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:237)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:323)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:164)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:237)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-24 03:44:19.576 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-24 03:44:19.576 [31mWARN [0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class com.example.pure.common.Result] with preset Content-Type 'text/event-stream'
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.doInclude(ApplicationDispatcher.java:537)
	at org.apache.catalina.core.ApplicationDispatcher.include(ApplicationDispatcher.java:480)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:358)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:237)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:323)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:164)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:237)
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-24 03:44:19.577 [31mWARN [0;39m 2572 --- [http-nio-8080-exec-6] o.s.w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'text/event-stream']
2025-07-24 03:44:19.577 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Exiting from "INCLUDE" dispatch, status 200
2025-07-24 03:44:34.102 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-24 03:44:34.102 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-24 03:44:34.103 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-24 03:44:34.103 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 03:44:34.103 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-24 03:44:34.103 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /v1/chat/completions] with attributes [permitAll]
2025-07-24 03:44:34.103 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-24 03:44:34.103 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : POST "/v1/chat/completions", parameters={}
2025-07-24 03:44:34.104 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.OpenAiCompatibleController#chatCompletions(OpenAiChatRequest, HttpServletRequest)
2025-07-24 03:44:34.104 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.pure.config.ContentDeserializer : 反序列化数组格式内容
2025-07-24 03:44:34.105 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [OpenAiChatRequest(model=gemini-2.5-pro, messages=[OpenAiChatRequest.OpenAiMessage(role=user, content (truncated)...]
2025-07-24 03:44:34.106 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiRequestServiceImpl : 收到OpenAI兼容聊天请求 - 模型: gemini-2.5-pro, 流式: true, 消息数: 1
2025-07-24 03:44:34.106 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiRequestServiceImpl : 处理流式聊天请求 - 模型: gemini-2.5-pro
2025-07-24 03:44:34.106 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 处理OpenAI兼容流式聊天请求 - 模型: gemini-2.5-pro
2025-07-24 03:44:34.106 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:44:34.106 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65833551] was not registered for synchronization because synchronization is not active
2025-07-24 03:44:34.107 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@20750024 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:44:34.108 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectById : ==>  Preparing: SELECT id, user_id, provider, key_name, api_key_encrypted, is_active, priority, usage_count, last_used_at, created_at, updated_at FROM user_api_keys WHERE id = ?
2025-07-24 03:44:34.108 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectById : ==> Parameters: 3(Long)
2025-07-24 03:44:34.111 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectById : <==      Total: 1
2025-07-24 03:44:34.111 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65833551]
2025-07-24 03:44:34.111 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始异步处理流式聊天 - 用户ID: 1, 密钥ID: 3
2025-07-24 03:44:34.111 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.impl.AiConfigServiceImpl : 获取用户AI配置 - 用户ID: 1
2025-07-24 03:44:34.112 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:44:34.112 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62fde960] was not registered for synchronization because synchronization is not active
2025-07-24 03:44:34.112 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@31601435 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:44:34.112 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config WHERE user_id = ?
2025-07-24 03:44:34.112 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-07-24 03:44:34.114 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-07-24 03:44:34.114 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62fde960]
2025-07-24 03:44:34.114 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择最佳API密钥 - 用户ID: 1, 提供商: GOOGLE
2025-07-24 03:44:34.115 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:44:34.115 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67268dc7] was not registered for synchronization because synchronization is not active
2025-07-24 03:44:34.115 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:44:34.115 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, provider, key_name, api_key_encrypted, is_active, priority, usage_count, last_used_at, created_at, updated_at FROM user_api_keys WHERE user_id = ? AND provider = ? AND is_active = TRUE ORDER BY priority ASC, created_at ASC
2025-07-24 03:44:34.115 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectActiveByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-07-24 03:44:34.119 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.U.selectActiveByUserIdAndProvider : <==      Total: 2
2025-07-24 03:44:34.119 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67268dc7]
2025-07-24 03:44:34.119 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:44:34.119 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@560cc34b] was not registered for synchronization because synchronization is not active
2025-07-24 03:44:34.119 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will not be managed by Spring
2025-07-24 03:44:34.119 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==>  Preparing: SELECT id, user_id, provider, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE user_id = ? AND provider = ? AND is_healthy = TRUE ORDER BY current_requests ASC, total_requests ASC
2025-07-24 03:44:34.120 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : ==> Parameters: 1(Long), GOOGLE(String)
2025-07-24 03:44:34.123 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.A.selectHealthyByUserIdAndProvider : <==      Total: 2
2025-07-24 03:44:34.123 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@560cc34b]
2025-07-24 03:44:34.123 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.LoadBalancerServiceImpl : 选择API密钥 - ID: 3, 当前请求数: 0
2025-07-24 03:44:34.124 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.pure.util.MultimodalContentUtils : 纯文本消息 - 长度: 15 字符
2025-07-24 03:44:34.125 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.LoadBalancerServiceImpl : 开始使用API密钥 - ID: 3
2025-07-24 03:44:34.125 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:44:34.125 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ab8ea6f]
2025-07-24 03:44:34.125 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2083238608 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will be managed by Spring
2025-07-24 03:44:34.125 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.A.incrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = current_requests + 1, total_requests = total_requests + 1, updated_at = NOW() WHERE api_key_id = ?
2025-07-24 03:44:34.125 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.A.incrementRequests : ==> Parameters: 3(Long)
2025-07-24 03:44:34.128 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.m.p.A.incrementRequests : <==    Updates: 1
2025-07-24 03:44:34.129 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ab8ea6f]
2025-07-24 03:44:34.129 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ab8ea6f]
2025-07-24 03:44:34.129 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ab8ea6f]
2025-07-24 03:44:34.129 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ab8ea6f]
2025-07-24 03:44:34.135 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-f99088b0, 数据大小: 232 bytes
2025-07-24 03:44:34.135 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-07-24 03:44:34.136 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 流式请求详情 ===
2025-07-24 03:44:34.136 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-24 03:44:34.136 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-07-24 03:44:34.136 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {top_p=1.0, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=user, content=请你生成一个java的简单代码}], model=gemini-2.5-pro}
2025-07-24 03:44:34.137 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-24 03:44:34.137 [34mINFO [0;39m 2572 --- [http-nio-8080-exec-8] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 开始流式聊天 - 响应ID: chatcmpl-f99088b0, 提供商: GOOGLE, 模型: gemini-2.5-pro
2025-07-24 03:44:34.137 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.w.r.f.client.ExchangeFunctions : [49694250] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-07-24 03:44:34.138 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-24 03:44:34.138 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [49694250] Encoding [{top_p=1.0, stream=true, max_tokens=6000, temperature=0.8, messages=[{role=user, content=请你生成一个java的 (truncated)...]
2025-07-24 03:44:34.139 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-24 03:44:34.139 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-24 03:44:51.487 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [49694250] [edc7045e-2] Response 200 OK
2025-07-24 03:44:51.488 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"当然！这里为您提供几个不同复杂度的 Java 简单代码示例，从最经典的 \"Hello, World!\" 开始。","role":"assistant"},"index":0}],"created":1753299891,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:51.488 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"当然！这里为您提供几个不同复杂度的 Java 简单代码示例，从最经典的 \"Hello, World!\" 开始。","role":"assistant"},"index":0}],"created":1753299891,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:51.488 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 234, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:51.488 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 234 bytes
2025-07-24 03:44:51.748 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n### 示例 1：经典的 \"Hello, World!\"\n\n这是学习任何编程语言的第一个程序。它只","role":"assistant"},"index":0}],"created":1753299891,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:51.749 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n### 示例 1：经典的 \"Hello, World!\"\n\n这是学习任何编程语言的第一个程序。它只","role":"assistant"},"index":0}],"created":1753299891,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:51.749 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 234, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:51.749 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 234 bytes
2025-07-24 03:44:52.044 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"做一件事：在控制台打印出 \"Hello, World!\"。\n\n**代码 (`HelloWorld.java`):**\n\n```","role":"assistant"},"index":0}],"created":1753299891,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.045 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"做一件事：在控制台打印出 \"Hello, World!\"。\n\n**代码 (`HelloWorld.java`):**\n\n```","role":"assistant"},"index":0}],"created":1753299891,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.045 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 246, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:52.045 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 246 bytes
2025-07-24 03:44:52.304 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"java\n// 定义一个名为 HelloWorld 的公共类\npublic class HelloWorld {\n    // 这是程序的入口点 - main 方法\n","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.304 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"java\n// 定义一个名为 HelloWorld 的公共类\npublic class HelloWorld {\n    // 这是程序的入口点 - main 方法\n","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.304 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 264, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:52.304 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 264 bytes
2025-07-24 03:44:52.519 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"    public static void main(String[] args) {\n        // 使用 System.out.println() 在控制台打印一行","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.519 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"    public static void main(String[] args) {\n        // 使用 System.out.println() 在控制台打印一行","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.519 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 266, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:52.519 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 266 bytes
2025-07-24 03:44:52.770 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"文本\n        System.out.println(\"Hello, World!\");\n    }\n}\n```\n\n**代码解释","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.770 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"文本\n        System.out.println(\"Hello, World!\");\n    }\n}\n```\n\n**代码解释","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:52.770 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 252, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:52.770 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 252 bytes
2025-07-24 03:44:53.017 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":":**\n\n*   `public class HelloWorld`: 定义了一个名为 `HelloWorld` 的类。在 Java 中，所有代码都必须存在","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.017 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":":**\n\n*   `public class HelloWorld`: 定义了一个名为 `HelloWorld` 的类。在 Java 中，所有代码都必须存在","role":"assistant"},"index":0}],"created":1753299892,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.017 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 257, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:53.017 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 257 bytes
2025-07-24 03:44:53.233 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"于类中。文件名必须与类名完全匹配，即 `HelloWorld.java`。\n*   `public","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.233 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"于类中。文件名必须与类名完全匹配，即 `HelloWorld.java`。\n*   `public","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.233 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 227, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:53.233 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 227 bytes
2025-07-24 03:44:53.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" static void main(String[] args)`: 这是 Java 程序的**主方法**（main method）。程序从这里开始","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" static void main(String[] args)`: 这是 Java 程序的**主方法**（main method）。程序从这里开始","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 251, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:53.538 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 251 bytes
2025-07-24 03:44:53.823 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"执行。\n*   `System.out.println(\"Hello, World!\");`: 这行代码会将括号内的字符串","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.824 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"执行。\n*   `System.out.println(\"Hello, World!\");`: 这行代码会将括号内的字符串","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:53.824 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 241, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:53.824 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 241 bytes
2025-07-24 03:44:54.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"（\"Hello, World!\"）输出到控制台，并换行。\n\n---\n\n### 示例 2：变量","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"（\"Hello, World!\"）输出到控制台，并换行。\n\n---\n\n### 示例 2：变量","role":"assistant"},"index":0}],"created":1753299893,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 229, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:54.020 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 229 bytes
2025-07-24 03:44:54.321 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"和简单计算\n\n这个例子展示了如何声明变量、进行简单的数学运算并打印结果。\n\n**代码 (`","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.321 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"和简单计算\n\n这个例子展示了如何声明变量、进行简单的数学运算并打印结果。\n\n**代码 (`","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.321 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 226, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:54.321 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 226 bytes
2025-07-24 03:44:54.540 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"SimpleCalculator.java`):**\n\n```java\npublic class SimpleCalculator {\n    public static void main(String[] args)","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.540 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"SimpleCalculator.java`):**\n\n```java\npublic class SimpleCalculator {\n    public static void main(String[] args)","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.540 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 291, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:54.540 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 291 bytes
2025-07-24 03:44:54.768 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" {\n        // 1. 声明两个整数变量并赋值\n        int numberA = 10;\n        int","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.768 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" {\n        // 1. 声明两个整数变量并赋值\n        int numberA = 10;\n        int","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.768 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 246, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:54.768 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 246 bytes
2025-07-24 03:44:54.969 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" numberB = 25;\n\n        // 2. 计算它们的和\n        int sum = numberA + numberB;","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.969 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" numberB = 25;\n\n        // 2. 计算它们的和\n        int sum = numberA + numberB;","role":"assistant"},"index":0}],"created":1753299894,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:54.969 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 253, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:54.969 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 253 bytes
2025-07-24 03:44:55.177 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n        // 3. 打印结果\n        // 这里的 '+' 用于连接字符串和变量\n        System.","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.177 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n        // 3. 打印结果\n        // 这里的 '+' 用于连接字符串和变量\n        System.","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.177 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 247, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:55.177 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 247 bytes
2025-07-24 03:44:55.492 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"out.println(\"数字 \" + numberA + \" 和 \" + numberB + \" 的和是: \" + sum);","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.492 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"out.println(\"数字 \" + numberA + \" 和 \" + numberB + \" 的和是: \" + sum);","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.492 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 247, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:55.492 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 247 bytes
2025-07-24 03:44:55.725 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n        // 4. 计算它们的乘积\n        int product = numberA * numberB;\n        System.","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.725 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n        // 4. 计算它们的乘积\n        int product = numberA * numberB;\n        System.","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.725 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 261, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:55.725 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 261 bytes
2025-07-24 03:44:55.973 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"out.println(\"它们的乘积是: \" + product);\n    }\n}\n```\n\n**代码解释:**\n\n","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.973 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"out.println(\"它们的乘积是: \" + product);\n    }\n}\n```\n\n**代码解释:**\n\n","role":"assistant"},"index":0}],"created":1753299895,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:55.973 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 245, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:55.973 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 245 bytes
2025-07-24 03:44:56.235 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"*   `int numberA = 10;`: 声明一个名为 `numberA` 的整型（","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.235 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"*   `int numberA = 10;`: 声明一个名为 `numberA` 的整型（","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.235 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 223, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:56.235 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 223 bytes
2025-07-24 03:44:56.481 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"`int`）变量，并给它赋值为 `10`。\n*   `int sum = number","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.481 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"`int`）变量，并给它赋值为 `10`。\n*   `int sum = number","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.481 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 221, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:56.481 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 221 bytes
2025-07-24 03:44:56.702 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"A + numberB;`: 将 `numberA` 和 `numberB` 的值相加，并将结果存储在新的","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.702 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"A + numberB;`: 将 `numberA` 和 `numberB` 的值相加，并将结果存储在新的","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.702 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 230, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:56.702 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 230 bytes
2025-07-24 03:44:56.906 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"整型变量 `sum` 中。\n*   `System.out.println(\"...\" + sum);`: ","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.906 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"整型变量 `sum` 中。\n*   `System.out.println(\"...\" + sum);`: ","role":"assistant"},"index":0}],"created":1753299896,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:56.906 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 234, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:56.906 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 234 bytes
2025-07-24 03:44:57.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"演示了如何将文本字符串和变量的值连接在一起进行输出。\n\n---\n\n### 示例 3：与","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"演示了如何将文本字符串和变量的值连接在一起进行输出。\n\n---\n\n### 示例 3：与","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 224, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:57.212 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 224 bytes
2025-07-24 03:44:57.480 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"用户交互\n\n这个例子会提示用户输入他们的名字，然后程序会向他们问好。这需要使用 `Scanner","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.480 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"用户交互\n\n这个例子会提示用户输入他们的名字，然后程序会向他们问好。这需要使用 `Scanner","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.480 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 227, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:57.480 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 227 bytes
2025-07-24 03:44:57.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"` 类来读取用户的输入。\n\n**代码 (`GreetUser.java`):**\n\n```java\n// 需要","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"` 类来读取用户的输入。\n\n**代码 (`GreetUser.java`):**\n\n```java\n// 需要","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 237, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:57.737 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 237 bytes
2025-07-24 03:44:57.985 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"导入 Scanner 类才能使用它\nimport java.util.Scanner;\n\npublic class GreetUser {\n    public static void","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.985 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"导入 Scanner 类才能使用它\nimport java.util.Scanner;\n\npublic class GreetUser {\n    public static void","role":"assistant"},"index":0}],"created":1753299897,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:57.985 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 273, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:57.985 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 273 bytes
2025-07-24 03:44:58.226 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" main(String[] args) {\n        // 创建一个 Scanner 对象，用于从键盘读取输入\n        Scanner scanner = new","role":"assistant"},"index":0}],"created":1753299898,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:58.226 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" main(String[] args) {\n        // 创建一个 Scanner 对象，用于从键盘读取输入\n        Scanner scanner = new","role":"assistant"},"index":0}],"created":1753299898,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:58.226 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 268, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:58.226 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 268 bytes
2025-07-24 03:44:58.403 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" Scanner(System.in);\n\n        // 提示用户输入名字\n        System.out.print(\"请输入您的","role":"assistant"},"index":0}],"created":1753299898,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:58.403 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" Scanner(System.in);\n\n        // 提示用户输入名字\n        System.out.print(\"请输入您的","role":"assistant"},"index":0}],"created":1753299898,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:58.403 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 254, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:58.403 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 254 bytes
2025-07-24 03:44:58.923 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"名字: \");\n\n        // 读取用户输入的一整行文本，并将其存储在 'name' 变量中\n        String","role":"assistant"},"index":0}],"created":1753299898,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:58.924 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"名字: \");\n\n        // 读取用户输入的一整行文本，并将其存储在 'name' 变量中\n        String","role":"assistant"},"index":0}],"created":1753299898,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:58.924 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 246, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:58.924 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 246 bytes
2025-07-24 03:44:59.187 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" name = scanner.nextLine();\n\n        // 向用户打印欢迎信息\n        System.out.println(\"你好, \" + name","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.187 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" name = scanner.nextLine();\n\n        // 向用户打印欢迎信息\n        System.out.println(\"你好, \" + name","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.187 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 272, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:59.187 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 272 bytes
2025-07-24 03:44:59.535 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" + \"! 欢迎学习 Java。\");\n\n        // 关闭 scanner 以释放资源，这是一个好习惯\n        scanner.close();\n","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.535 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" + \"! 欢迎学习 Java。\");\n\n        // 关闭 scanner 以释放资源，这是一个好习惯\n        scanner.close();\n","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.535 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 265, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:59.535 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 265 bytes
2025-07-24 03:44:59.729 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"    }\n}\n```\n\n**代码解释:**\n\n*   `import java.util.Scanner;`: ","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.729 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"    }\n}\n```\n\n**代码解释:**\n\n*   `import java.util.Scanner;`: ","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.729 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 240, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:59.729 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 240 bytes
2025-07-24 03:44:59.951 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"告诉编译器我们要使用 `java.util` 包中的 `Scanner` 类。\n*   `Scanner scanner = new Scanner(System","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.951 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"告诉编译器我们要使用 `java.util` 包中的 `Scanner` 类。\n*   `Scanner scanner = new Scanner(System","role":"assistant"},"index":0}],"created":1753299899,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:44:59.951 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 259, 响应ID: chatcmpl-f99088b0
2025-07-24 03:44:59.951 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 259 bytes
2025-07-24 03:45:00.261 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":".in);`: 创建（实例化）一个 `Scanner` 对象，并将其连接到标准输入流（`System.","role":"assistant"},"index":0}],"created":1753299900,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:00.261 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":".in);`: 创建（实例化）一个 `Scanner` 对象，并将其连接到标准输入流（`System.","role":"assistant"},"index":0}],"created":1753299900,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:00.261 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 228, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:00.261 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 228 bytes
2025-07-24 03:45:00.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"in`），也就是键盘。\n*   `System.out.print(...)`: 与 `println` 不同，`print`","role":"assistant"},"index":0}],"created":1753299900,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:00.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"in`），也就是键盘。\n*   `System.out.print(...)`: 与 `println` 不同，`print`","role":"assistant"},"index":0}],"created":1753299900,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:00.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 241, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:00.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 241 bytes
2025-07-24 03:45:00.935 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 不会在输出后换行，所以用户的输入会紧跟在提示语后面。\n*   `String name =","role":"assistant"},"index":0}],"created":1753299900,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:00.935 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 不会在输出后换行，所以用户的输入会紧跟在提示语后面。\n*   `String name =","role":"assistant"},"index":0}],"created":1753299900,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:00.936 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 224, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:00.936 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 224 bytes
2025-07-24 03:45:01.268 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" scanner.nextLine();`: 调用 `scanner` 对象的 `nextLine()` 方法，程序会在此暂停，等待用户输入一行","role":"assistant"},"index":0}],"created":1753299901,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:01.268 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" scanner.nextLine();`: 调用 `scanner` 对象的 `nextLine()` 方法，程序会在此暂停，等待用户输入一行","role":"assistant"},"index":0}],"created":1753299901,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:01.268 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 249, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:01.268 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 249 bytes
2025-07-24 03:45:01.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"文字并按回车。用户输入的内容会被赋值给字符串变量 `name`。\n*   `scanner.close","role":"assistant"},"index":0}],"created":1753299901,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:01.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"文字并按回车。用户输入的内容会被赋值给字符串变量 `name`。\n*   `scanner.close","role":"assistant"},"index":0}],"created":1753299901,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:01.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 229, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:01.560 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 229 bytes
2025-07-24 03:45:01.963 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"();`: 当你不再需要 `Scanner` 对象时，应该关闭它。\n\n### 如何运行这些代码？\n\n","role":"assistant"},"index":0}],"created":1753299901,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:01.963 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"();`: 当你不再需要 `Scanner` 对象时，应该关闭它。\n\n### 如何运行这些代码？\n\n","role":"assistant"},"index":0}],"created":1753299901,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:01.963 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 231, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:01.963 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 231 bytes
2025-07-24 03:45:02.281 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"1.  **安装 Java 开发工具包 (JDK)**：确保你的电脑上安装了 JDK。\n2.  **","role":"assistant"},"index":0}],"created":1753299902,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:02.281 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"1.  **安装 Java 开发工具包 (JDK)**：确保你的电脑上安装了 JDK。\n2.  **","role":"assistant"},"index":0}],"created":1753299902,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:02.281 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 228, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:02.281 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 228 bytes
2025-07-24 03:45:02.678 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"保存文件**：将以上任一代码块保存为 `.java` 文件。**文件名必须与 `public class","role":"assistant"},"index":0}],"created":1753299902,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:02.678 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"保存文件**：将以上任一代码块保存为 `.java` 文件。**文件名必须与 `public class","role":"assistant"},"index":0}],"created":1753299902,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:02.678 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 229, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:02.678 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 229 bytes
2025-07-24 03:45:02.884 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"` 的名称完全相同**（例如，第一个示例必须保存为 `HelloWorld.java`）。\n3.  ","role":"assistant"},"index":0}],"created":1753299902,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:02.884 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"` 的名称完全相同**（例如，第一个示例必须保存为 `HelloWorld.java`）。\n3.  ","role":"assistant"},"index":0}],"created":1753299902,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:02.884 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 228, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:02.884 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 228 bytes
2025-07-24 03:45:03.174 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"**打开终端或命令提示符**：\n    *   在 Windows 上是 `cmd` 或 `PowerShell`。\n","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.174 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"**打开终端或命令提示符**：\n    *   在 Windows 上是 `cmd` 或 `PowerShell`。\n","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.174 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 238, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:03.174 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 238 bytes
2025-07-24 03:45:03.380 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"    *   在 macOS 或 Linux 上是 `Terminal`。\n4.  **编译代码**：使用 `javac` 命令","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.380 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"    *   在 macOS 或 Linux 上是 `Terminal`。\n4.  **编译代码**：使用 `javac` 命令","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.380 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 243, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:03.380 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 243 bytes
2025-07-24 03:45:03.635 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"将 `.java` 源文件编译成 `.class` 字节码文件。\n    ```bash\n    # 编译 HelloWorld.","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.635 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"将 `.java` 源文件编译成 `.class` 字节码文件。\n    ```bash\n    # 编译 HelloWorld.","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.635 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 244, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:03.635 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 244 bytes
2025-07-24 03:45:03.914 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"java\n    javac HelloWorld.java\n    ```\n    如果没有任何错误，这会在同一目录下生成一个 `HelloWorld.class`","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.914 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"java\n    javac HelloWorld.java\n    ```\n    如果没有任何错误，这会在同一目录下生成一个 `HelloWorld.class`","role":"assistant"},"index":0}],"created":1753299903,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:03.914 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 263, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:03.914 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 263 bytes
2025-07-24 03:45:04.180 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":" 文件。\n\n5.  **运行程序**：使用 `java` 命令运行编译好的类。\n    ```bash","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:04.180 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":" 文件。\n\n5.  **运行程序**：使用 `java` 命令运行编译好的类。\n    ```bash","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:04.180 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 231, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:04.180 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 231 bytes
2025-07-24 03:45:04.531 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n    # 运行 HelloWorld 类 (注意，不要加 .class 后缀)\n    java HelloWorld\n    ```\n\n    ","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:04.532 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n    # 运行 HelloWorld 类 (注意，不要加 .class 后缀)\n    java HelloWorld\n    ```\n\n    ","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:04.532 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 257, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:04.532 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 257 bytes
2025-07-24 03:45:04.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"对于其他示例，只需替换文件名即可：\n    ```bash\n    javac SimpleCalculator.java\n    java SimpleCalculator","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:04.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"对于其他示例，只需替换文件名即可：\n    ```bash\n    javac SimpleCalculator.java\n    java SimpleCalculator","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:04.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 267, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:04.856 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 267 bytes
2025-07-24 03:45:05.099 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"\n\n    javac GreetUser.java\n    java GreetUser\n    ```\n\n希望这些简单的例子能帮助你","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:05.099 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"\n\n    javac GreetUser.java\n    java GreetUser\n    ```\n\n希望这些简单的例子能帮助你","role":"assistant"},"index":0}],"created":1753299904,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:05.099 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 251, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:05.099 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 251 bytes
2025-07-24 03:45:05.562 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: {"choices":[{"delta":{"content":"开始 Java 编程之旅！","role":"assistant"},"finish_reason":"stop","index":0}],"created":1753299905,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:05.562 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"delta":{"content":"开始 Java 编程之旅！","role":"assistant"},"finish_reason":"stop","index":0}],"created":1753299905,"id":"ozuBaJy_K92Fz7IPw-6W0Aw","model":"gemini-2.5-pro","object":"chat.completion.chunk"}
2025-07-24 03:45:05.562 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : GOOGLE直接转发原始数据 - 长度: 213, 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:05.562 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送原始SSE数据 - 数据大小: 213 bytes
2025-07-24 03:45:05.571 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google原始响应行: [DONE]
2025-07-24 03:45:05.571 [31mWARN [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 过滤掉非数据行: [DONE]
2025-07-24 03:45:05.572 [34mINFO [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google流式请求完成
2025-07-24 03:45:05.572 [34mINFO [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式响应正常完成 - 响应ID: chatcmpl-f99088b0, 模型: gemini-2.5-pro
2025-07-24 03:45:05.572 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE事件 - 响应ID: chatcmpl-f99088b0, 数据大小: 281 bytes
2025-07-24 03:45:05.572 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 发送SSE完成事件: [DONE]
2025-07-24 03:45:05.572 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /v1/chat/completions
2025-07-24 03:45:05.572 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : SseEmitter安全完成 - 响应ID: chatcmpl-f99088b0
2025-07-24 03:45:05.573 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing POST /v1/chat/completions
2025-07-24 03:45:05.573 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-24 03:45:05.573 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-24 03:45:05.573 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured POST /v1/chat/completions
2025-07-24 03:45:05.573 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/v1/chat/completions", parameters={}
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 结束使用API密钥 - ID: 3, 成功: true
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3]
2025-07-24 03:45:05.574 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@531234886 wrapping com.mysql.cj.jdbc.ConnectionImpl@6b7c6c7c] will be managed by Spring
2025-07-24 03:45:05.575 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : ==>  Preparing: UPDATE api_key_load_balance SET current_requests = GREATEST(current_requests - 1, 0), updated_at = NOW() WHERE api_key_id = ?
2025-07-24 03:45:05.575 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : ==> Parameters: 3(Long)
2025-07-24 03:45:05.578 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.A.decrementRequests : <==    Updates: 1
2025-07-24 03:45:05.578 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3]
2025-07-24 03:45:05.578 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3] from current transaction
2025-07-24 03:45:05.578 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : ==>  Preparing: UPDATE user_api_keys SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?
2025-07-24 03:45:05.578 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : ==> Parameters: 3(Long)
2025-07-24 03:45:05.580 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] c.e.p.m.p.U.updateUsageStats : <==    Updates: 1
2025-07-24 03:45:05.580 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3]
2025-07-24 03:45:05.580 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3]
2025-07-24 03:45:05.580 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3]
2025-07-24 03:45:05.580 [39mDEBUG[0;39m 2572 --- [reactor-http-nio-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42a1f8a3]
2025-07-24 03:45:05.596 [34mINFO [0;39m 2572 --- [reactor-http-nio-2] c.e.p.s.o.i.OpenAiCompatibleServiceImpl : 流式聊天完成 - 响应ID: chatcmpl-f99088b0, API密钥ID: 3
2025-07-24 04:00:48.609 [34mINFO [0;39m 2572 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-24 04:17:26.549 [39mDEBUG[0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-24 04:17:26.549 [39mDEBUG[0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-24 04:17:26.549 [34mINFO [0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-24 04:17:26.550 [34mINFO [0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@c358f32]]
2025-07-24 04:17:26.550 [39mDEBUG[0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@c358f32]
2025-07-24 04:17:26.550 [39mDEBUG[0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@c358f32]
2025-07-24 04:17:26.550 [34mINFO [0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-24 04:17:26.550 [39mDEBUG[0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-24 04:17:26.550 [39mDEBUG[0;39m 2572 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-24 04:17:28.692 [34mINFO [0;39m 2572 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-24 04:17:28.697 [34mINFO [0;39m 2572 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
