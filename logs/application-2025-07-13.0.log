2025-07-13 23:45:15.808 [34mINFO [0;39m 1608 --- [main] com.example.pure.controller.test : Starting test using Java 11.0.27 on DESKTOP-DQ33ANO with PID 1608 (started by <PERSON><PERSON> in C:\Users\<USER>\IdeaProjects\pure)
2025-07-13 23:45:15.810 [39mDEBUG[0;39m 1608 --- [main] com.example.pure.controller.test : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-13 23:45:15.811 [34mINFO [0;39m 1608 --- [main] com.example.pure.controller.test : The following 1 profile is active: "dev"
2025-07-13 23:45:15.823 [34mINFO [0;39m 1608 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-13 23:45:15.862 [39mDEBUG[0;39m 1608 --- [main] o.s.w.c.s.GenericWebApplicationContext : Refreshing org.springframework.web.context.support.GenericWebApplicationContext@736f3e9e
2025-07-13 23:45:17.538 [34mINFO [0;39m 1608 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-13 23:45:17.542 [34mINFO [0;39m 1608 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-13 23:45:17.603 [34mINFO [0;39m 1608 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-13 23:45:17.775 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-13 23:45:17.776 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-13 23:45:17.776 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-13 23:45:17.776 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-13 23:45:17.776 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-13 23:45:17.776 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-13 23:45:17.776 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-13 23:45:17.777 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-13 23:45:17.779 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-13 23:45:17.779 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-13 23:45:17.779 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-13 23:45:17.779 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-13 23:45:17.780 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-13 23:45:17.780 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-13 23:45:17.780 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-13 23:45:17.780 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-13 23:45:17.780 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-13 23:45:17.780 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-13 23:45:17.781 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-13 23:45:17.781 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-13 23:45:17.781 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-13 23:45:17.781 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-13 23:45:17.781 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-13 23:45:17.781 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-13 23:45:17.782 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-13 23:45:17.782 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-13 23:45:17.782 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-13 23:45:17.782 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-13 23:45:17.782 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-13 23:45:17.782 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-13 23:45:17.783 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-13 23:45:17.783 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-13 23:45:17.783 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-13 23:45:17.783 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-13 23:45:17.783 [39mDEBUG[0;39m 1608 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-13 23:45:20.464 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-13 23:45:20.491 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-13 23:45:20.506 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-13 23:45:20.524 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-13 23:45:20.555 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-13 23:45:20.565 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-13 23:45:20.579 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-13 23:45:20.597 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-13 23:45:20.620 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-13 23:45:20.662 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-13 23:45:20.723 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-13 23:45:20.747 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-13 23:45:20.762 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-13 23:45:20.781 [39mDEBUG[0;39m 1608 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-13 23:45:20.897 [34mINFO [0;39m 1608 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-13 23:45:21.659 [34mINFO [0;39m 1608 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-13 23:45:22.945 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-13 23:45:22.948 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:23.823 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-13 23:45:23.985 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-13 23:45:23.999 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-13 23:45:24.123 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-13 23:45:24.124 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.125 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-13 23:45:24.126 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.126 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-13 23:45:24.126 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.127 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-13 23:45:24.127 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.127 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-13 23:45:24.127 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.128 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-13 23:45:24.128 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.133 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-13 23:45:24.134 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.134 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-13 23:45:24.134 [39mDEBUG[0;39m 1608 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:45:24.336 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-13 23:45:24.338 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-13 23:45:24.353 [34mINFO [0;39m 1608 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f6ede9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4583a186, org.springframework.security.web.context.SecurityContextPersistenceFilter@5f92e5eb, org.springframework.security.web.header.HeaderWriterFilter@67836d4, org.springframework.security.web.authentication.logout.LogoutFilter@2643c33f, com.example.pure.filter.JwtFilter@55854382, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18d8da77, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@502dc93a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7843b65e, org.springframework.security.web.session.SessionManagementFilter@78c9c38a, org.springframework.security.web.access.ExceptionTranslationFilter@37e967df, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@112f8f8f]
2025-07-13 23:45:24.358 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-13 23:45:24.366 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-13 23:45:24.367 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-13 23:45:24.368 [34mINFO [0;39m 1608 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-13 23:45:24.728 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-13 23:45:24.818 [34mINFO [0;39m 1608 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-13 23:45:24.936 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-13 23:45:24.951 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-13 23:45:25.628 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-13 23:45:25.909 [34mINFO [0;39m 1608 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-13 23:45:25.949 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-13 23:45:25.950 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-13 23:45:25.951 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-13 23:45:25.952 [39mDEBUG[0;39m 1608 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-13 23:45:25.953 [34mINFO [0;39m 1608 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@67fdc1e9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@33a38f07, org.springframework.security.web.context.SecurityContextPersistenceFilter@2cc7b63d, org.springframework.security.web.header.HeaderWriterFilter@440e2406, org.springframework.web.filter.CorsFilter@4e7bf371, org.springframework.security.web.authentication.logout.LogoutFilter@7ea91c39, com.example.pure.filter.JwtFilter@55854382, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6179551b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@54626326, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5fe08, org.springframework.security.web.session.SessionManagementFilter@56adb75e, org.springframework.security.web.access.ExceptionTranslationFilter@1f9a5d3b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4b90a402]
2025-07-13 23:45:26.034 [39mTRACE[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.web.context.support.GenericWebApplicationContext@736f3e9e, started on Sun Jul 13 23:45:15 CST 2025
2025-07-13 23:45:26.056 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-13 23:45:26.056 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-13 23:45:26.056 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-13 23:45:26.056 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-13 23:45:26.056 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-13 23:45:26.057 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-13 23:45:26.058 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-13 23:45:26.058 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-13 23:45:26.063 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-07-13 23:45:26.064 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-13 23:45:26.065 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-13 23:45:26.065 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-13 23:45:26.065 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-13 23:45:26.343 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-13 23:45:26.428 [39mDEBUG[0;39m 1608 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-13 23:45:26.965 [39mDEBUG[0;39m 1608 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-13 23:45:26.965 [39mDEBUG[0;39m 1608 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-13 23:45:26.965 [34mINFO [0;39m 1608 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-13 23:45:26.965 [39mDEBUG[0;39m 1608 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@62ae5180]
2025-07-13 23:45:26.965 [39mDEBUG[0;39m 1608 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@62ae5180]
2025-07-13 23:45:26.965 [34mINFO [0;39m 1608 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@62ae5180]]
2025-07-13 23:45:26.966 [34mINFO [0;39m 1608 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-13 23:45:26.966 [39mDEBUG[0;39m 1608 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-13 23:45:26.966 [39mDEBUG[0;39m 1608 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-13 23:45:26.979 [34mINFO [0;39m 1608 --- [main] com.example.pure.controller.test : Started test in 11.679 seconds (JVM running for 14.186)
2025-07-13 23:45:27.418 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.w.c.s.GenericWebApplicationContext : Closing org.springframework.web.context.support.GenericWebApplicationContext@736f3e9e, started on Sun Jul 13 23:45:15 CST 2025
2025-07-13 23:45:27.421 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-13 23:45:27.422 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-13 23:45:27.422 [34mINFO [0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-13 23:45:27.422 [34mINFO [0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@62ae5180]]
2025-07-13 23:45:27.422 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@62ae5180]
2025-07-13 23:45:27.422 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@62ae5180]
2025-07-13 23:45:27.422 [34mINFO [0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-13 23:45:27.422 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-13 23:45:27.422 [39mDEBUG[0;39m 1608 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-13 23:45:27.467 [34mINFO [0;39m 1608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-13 23:45:27.480 [34mINFO [0;39m 1608 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-13 23:47:59.443 [34mINFO [0;39m 5204 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-13 23:47:59.443 [34mINFO [0;39m 5204 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 5204 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-13 23:47:59.445 [39mDEBUG[0;39m 5204 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-13 23:47:59.446 [34mINFO [0;39m 5204 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-13 23:48:00.415 [34mINFO [0;39m 5204 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-13 23:48:00.416 [34mINFO [0;39m 5204 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-13 23:48:00.454 [34mINFO [0;39m 5204 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-07-13 23:48:00.562 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-13 23:48:00.563 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-13 23:48:00.564 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-13 23:48:00.566 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-13 23:48:00.566 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-13 23:48:00.566 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-13 23:48:00.566 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-13 23:48:00.566 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-13 23:48:00.567 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-13 23:48:00.567 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-13 23:48:00.567 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-13 23:48:00.567 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-13 23:48:00.567 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-13 23:48:00.567 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-13 23:48:00.568 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-13 23:48:00.569 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-13 23:48:00.570 [39mDEBUG[0;39m 5204 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-13 23:48:01.173 [34mINFO [0;39m 5204 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-13 23:48:01.179 [34mINFO [0;39m 5204 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-13 23:48:01.180 [34mINFO [0;39m 5204 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-13 23:48:01.180 [34mINFO [0;39m 5204 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-13 23:48:01.282 [34mINFO [0;39m 5204 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-13 23:48:01.282 [34mINFO [0;39m 5204 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1797 ms
2025-07-13 23:48:01.552 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-13 23:48:01.563 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-13 23:48:01.569 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-13 23:48:01.576 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-13 23:48:01.588 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-13 23:48:01.592 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-13 23:48:01.598 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-13 23:48:01.602 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-13 23:48:01.609 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-13 23:48:01.617 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-13 23:48:01.628 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-13 23:48:01.635 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-13 23:48:01.639 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-13 23:48:01.645 [39mDEBUG[0;39m 5204 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-13 23:48:01.655 [34mINFO [0;39m 5204 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-13 23:48:01.886 [34mINFO [0;39m 5204 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-13 23:48:02.499 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-13 23:48:02.500 [39mDEBUG[0;39m 5204 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-13 23:48:02.821 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-13 23:48:02.823 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.238 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-13 23:48:03.355 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-13 23:48:03.362 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-13 23:48:03.419 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-13 23:48:03.420 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.421 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-13 23:48:03.421 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.421 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-13 23:48:03.421 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.421 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-13 23:48:03.422 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.422 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-13 23:48:03.422 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.422 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-13 23:48:03.422 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.425 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-13 23:48:03.425 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.426 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-13 23:48:03.426 [39mDEBUG[0;39m 5204 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-13 23:48:03.540 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-13 23:48:03.543 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-13 23:48:03.552 [34mINFO [0;39m 5204 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@55159007, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@edf4973, org.springframework.security.web.context.SecurityContextPersistenceFilter@6cfe32cd, org.springframework.security.web.header.HeaderWriterFilter@6195ce27, org.springframework.security.web.authentication.logout.LogoutFilter@40273969, com.example.pure.filter.JwtFilter@3f5ac587, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3f31cf8f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@28721794, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2a49753, org.springframework.security.web.session.SessionManagementFilter@18356d9e, org.springframework.security.web.access.ExceptionTranslationFilter@65cf8da0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@19f9404d]
2025-07-13 23:48:03.556 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-13 23:48:03.559 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-13 23:48:03.560 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-13 23:48:03.560 [34mINFO [0;39m 5204 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-13 23:48:03.738 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-13 23:48:03.763 [34mINFO [0;39m 5204 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-13 23:48:03.831 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-07-13 23:48:03.838 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-13 23:48:04.235 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-13 23:48:04.361 [34mINFO [0;39m 5204 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-13 23:48:04.382 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-13 23:48:04.383 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-13 23:48:04.384 [39mDEBUG[0;39m 5204 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-13 23:48:04.384 [34mINFO [0;39m 5204 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@530aa75c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@593d0173, org.springframework.security.web.context.SecurityContextPersistenceFilter@505a48a2, org.springframework.security.web.header.HeaderWriterFilter@bbf361a, org.springframework.web.filter.CorsFilter@4a0a93ce, org.springframework.security.web.authentication.logout.LogoutFilter@7fb8d720, com.example.pure.filter.JwtFilter@3f5ac587, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@15096b0e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@67748053, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@64404db6, org.springframework.security.web.session.SessionManagementFilter@46cb800b, org.springframework.security.web.access.ExceptionTranslationFilter@32eeef08, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@497cf31a]
2025-07-13 23:48:04.423 [39mTRACE[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@492fc69e, started on Sun Jul 13 23:47:59 CST 2025
2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-13 23:48:04.436 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-13 23:48:04.437 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-13 23:48:04.441 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-13 23:48:04.442 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-13 23:48:04.443 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-13 23:48:04.443 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-13 23:48:04.443 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-13 23:48:04.522 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-13 23:48:04.551 [39mDEBUG[0;39m 5204 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-13 23:48:04.815 [34mINFO [0;39m 5204 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-13 23:48:04.825 [34mINFO [0;39m 5204 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-13 23:48:04.826 [39mDEBUG[0;39m 5204 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-13 23:48:04.827 [39mDEBUG[0;39m 5204 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-13 23:48:04.827 [34mINFO [0;39m 5204 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-13 23:48:04.827 [39mDEBUG[0;39m 5204 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41dcfd0]
2025-07-13 23:48:04.827 [39mDEBUG[0;39m 5204 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41dcfd0]
2025-07-13 23:48:04.827 [34mINFO [0;39m 5204 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@41dcfd0]]
2025-07-13 23:48:04.827 [34mINFO [0;39m 5204 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-13 23:48:04.827 [39mDEBUG[0;39m 5204 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-13 23:48:04.827 [39mDEBUG[0;39m 5204 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-13 23:48:04.840 [34mINFO [0;39m 5204 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.874 seconds (JVM running for 6.715)
2025-07-13 23:49:04.409 [34mINFO [0;39m 5204 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-13 23:49:07.226 [34mINFO [0;39m 5204 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-13 23:49:07.226 [34mINFO [0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-13 23:49:07.226 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-13 23:49:07.226 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-13 23:49:07.226 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-13 23:49:07.229 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2341d99f
2025-07-13 23:49:07.230 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@44f100f8
2025-07-13 23:49:07.230 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-13 23:49:07.230 [34mINFO [0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-07-13 23:49:07.241 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-13 23:49:07.243 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-13 23:49:07.250 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-13 23:49:07.253 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-13 23:49:07.253 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-13 23:49:07.258 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai-chat/stream] with attributes [permitAll]
2025-07-13 23:49:07.258 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-13 23:49:07.261 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/ai-chat/stream", parameters={}
2025-07-13 23:49:07.262 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-13 23:49:07.323 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [AiChatRequest(message=hello!, sessionId=null, messageType=chat)]
2025-07-13 23:49:07.390 [34mINFO [0;39m 5204 --- [http-nio-8080-exec-1] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: hello!, 会话ID: null
2025-07-13 23:49:07.394 [34mINFO [0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752421747390_328ce02b, 消息: hello!
2025-07-13 23:49:07.408 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送start控制事件 - 会话ID: session_1752421747390_328ce02b
2025-07-13 23:49:07.408 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 1
2025-07-13 23:49:07.546 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 2
2025-07-13 23:49:07.668 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 3
2025-07-13 23:49:07.740 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 4
2025-07-13 23:49:07.843 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 5
2025-07-13 23:49:07.950 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 6
2025-07-13 23:49:08.012 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 7
2025-07-13 23:49:08.142 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 8
2025-07-13 23:49:08.255 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 9
2025-07-13 23:49:08.346 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 10
2025-07-13 23:49:08.470 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 11
2025-07-13 23:49:08.619 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 12
2025-07-13 23:49:08.761 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 13
2025-07-13 23:49:08.897 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 14
2025-07-13 23:49:09.040 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 15
2025-07-13 23:49:09.136 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 16
2025-07-13 23:49:09.194 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 17
2025-07-13 23:49:09.282 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 18
2025-07-13 23:49:09.361 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 19
2025-07-13 23:49:09.495 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 20
2025-07-13 23:49:09.624 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 21
2025-07-13 23:49:09.703 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 22
2025-07-13 23:49:09.787 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 23
2025-07-13 23:49:09.919 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 24
2025-07-13 23:49:10.018 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752421747390_328ce02b, 内容长度: 25
2025-07-13 23:49:10.114 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : 发送end控制事件 - 会话ID: session_1752421747390_328ce02b
2025-07-13 23:49:10.114 [34mINFO [0;39m 5204 --- [http-nio-8080-exec-1] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752421747390_328ce02b
2025-07-13 23:49:10.132 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-13 23:49:10.143 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai-chat/stream
2025-07-13 23:49:10.145 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-13 23:49:10.146 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-13 23:49:10.149 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-13 23:49:10.149 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-13 23:49:10.149 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-13 23:49:10.149 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-13 23:49:10.149 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/api/ai-chat/stream", parameters={}
2025-07-13 23:49:10.150 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-13 23:49:10.153 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-13 23:49:10.153 [39mDEBUG[0;39m 5204 --- [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
