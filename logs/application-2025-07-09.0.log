2025-07-09 00:08:09.247 [34mINFO [0;39m 4408 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-Http<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-07-09 00:38:09.260 [34mINFO [0;39m 4408 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-<PERSON>ttp<PERSON>oll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-07-09 01:08:09.274 [34mINFO [0;39m 4408 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-07-09 01:38:09.280 [34mINFO [0;39m 4408 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-07-09 02:08:09.291 [34mINFO [0;39m 4408 --- [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-07-09 02:38:09.296 [34mINFO [0;39m 4408 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-07-09 02:57:31.519 [39mDEBUG[0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-09 02:57:31.519 [39mDEBUG[0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-09 02:57:31.519 [34mINFO [0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-09 02:57:31.519 [34mINFO [0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4f67d628]]
2025-07-09 02:57:31.519 [39mDEBUG[0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4f67d628]
2025-07-09 02:57:31.519 [39mDEBUG[0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4f67d628]
2025-07-09 02:57:31.519 [34mINFO [0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-09 02:57:31.519 [39mDEBUG[0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 02:57:31.519 [39mDEBUG[0;39m 4408 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 02:57:31.931 [34mINFO [0;39m 4408 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-09 02:57:31.941 [34mINFO [0;39m 4408 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-09 13:59:14.518 [34mINFO [0;39m 24524 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 13:59:14.521 [34mINFO [0;39m 24524 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 24524 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-09 13:59:14.522 [39mDEBUG[0;39m 24524 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 13:59:14.522 [34mINFO [0;39m 24524 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-09 13:59:15.577 [34mINFO [0;39m 24524 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-09 13:59:15.579 [34mINFO [0;39m 24524 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-09 13:59:15.616 [34mINFO [0;39m 24524 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-09 13:59:15.704 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-09 13:59:15.705 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-09 13:59:15.706 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-09 13:59:15.707 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-09 13:59:15.707 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-09 13:59:15.707 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-09 13:59:15.707 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-09 13:59:15.707 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-09 13:59:15.708 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-09 13:59:15.708 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-09 13:59:15.708 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-09 13:59:15.709 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-09 13:59:15.709 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-09 13:59:15.709 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-09 13:59:15.709 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-09 13:59:15.709 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-09 13:59:15.709 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-09 13:59:15.710 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-09 13:59:15.710 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-09 13:59:15.710 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-09 13:59:15.710 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-09 13:59:15.710 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-09 13:59:15.710 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-09 13:59:15.711 [39mDEBUG[0;39m 24524 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-09 13:59:16.234 [34mINFO [0;39m 24524 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-09 13:59:16.240 [34mINFO [0;39m 24524 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 13:59:16.241 [34mINFO [0;39m 24524 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-09 13:59:16.241 [34mINFO [0;39m 24524 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 13:59:16.338 [34mINFO [0;39m 24524 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-09 13:59:16.338 [34mINFO [0;39m 24524 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1780 ms
2025-07-09 13:59:16.969 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-09 13:59:16.986 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-09 13:59:16.997 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-09 13:59:17.007 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-09 13:59:17.028 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-09 13:59:17.036 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-09 13:59:17.048 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-09 13:59:17.068 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-09 13:59:17.077 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-09 13:59:17.088 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-09 13:59:17.102 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-09 13:59:17.111 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-09 13:59:17.118 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-09 13:59:17.126 [39mDEBUG[0;39m 24524 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-09 13:59:17.143 [34mINFO [0;39m 24524 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-09 13:59:18.225 [1;31mERROR[0;39m 24524 --- [main] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:332)
	at org.springframework.boot.jdbc.EmbeddedDatabaseConnection.isEmbedded(EmbeddedDatabaseConnection.java:164)
	at org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer.isEmbeddedDatabase(DataSourceScriptDatabaseInitializer.java:70)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.isEnabled(AbstractScriptDatabaseInitializer.java:83)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applyScripts(AbstractScriptDatabaseInitializer.java:106)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.applySchemaScripts(AbstractScriptDatabaseInitializer.java:97)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.initializeDatabase(AbstractScriptDatabaseInitializer.java:75)
	at org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer.afterPropertiesSet(AbstractScriptDatabaseInitializer.java:65)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1616)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.resolveStream(DefaultListableBeanFactory.java:2119)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.orderedStream(DefaultListableBeanFactory.java:2113)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.addBinders(MeterRegistryConfigurer.java:87)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryConfigurer.configure(MeterRegistryConfigurer.java:68)
	at org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryPostProcessor.postProcessAfterInitialization(MeterRegistryPostProcessor.java:64)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:455)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1808)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:536)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:204)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:98)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.pure.PureApplication.main(PureApplication.java:37)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 132 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.base/java.net.PlainSocketImpl.waitForConnect(Native Method)
	at java.base/java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:107)
	at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
	at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
	at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.base/java.net.Socket.connect(Socket.java:609)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 135 common frames omitted
2025-07-09 13:59:18.904 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-09 13:59:18.904 [39mDEBUG[0;39m 24524 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-09 13:59:18.971 [34mINFO [0;39m 24524 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-09 13:59:19.238 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-09 13:59:19.241 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.755 [34mINFO [0;39m 24524 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-09 13:59:19.860 [34mINFO [0;39m 24524 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-09 13:59:19.867 [34mINFO [0;39m 24524 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-09 13:59:19.913 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-09 13:59:19.913 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.914 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-09 13:59:19.915 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.915 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-09 13:59:19.915 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.915 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-09 13:59:19.915 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.916 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-09 13:59:19.916 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.916 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-09 13:59:19.916 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.919 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-09 13:59:19.919 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:19.919 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-09 13:59:19.919 [39mDEBUG[0;39m 24524 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-09 13:59:20.019 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-09 13:59:20.021 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-09 13:59:20.027 [34mINFO [0;39m 24524 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@1b0fc8b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@61c644fe, org.springframework.security.web.context.SecurityContextPersistenceFilter@6d5fea64, org.springframework.security.web.header.HeaderWriterFilter@255482cb, org.springframework.security.web.authentication.logout.LogoutFilter@28d37d43, com.example.pure.filter.JwtFilter@6081f330, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@155767a7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5418a659, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@cbaabfb, org.springframework.security.web.session.SessionManagementFilter@7b6b8cea, org.springframework.security.web.access.ExceptionTranslationFilter@ab5dde4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d8751de]
2025-07-09 13:59:20.028 [34mINFO [0;39m 24524 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-09 13:59:20.028 [34mINFO [0;39m 24524 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-09 13:59:20.176 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-09 13:59:20.195 [34mINFO [0;39m 24524 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-09 13:59:20.262 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-07-09 13:59:20.274 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-09 13:59:20.683 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-09 13:59:20.828 [34mINFO [0;39m 24524 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-09 13:59:20.850 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-09 13:59:20.851 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-09 13:59:20.852 [39mDEBUG[0;39m 24524 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-09 13:59:20.853 [34mINFO [0;39m 24524 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@12921ee1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7695184b, org.springframework.security.web.context.SecurityContextPersistenceFilter@2a738d47, org.springframework.security.web.header.HeaderWriterFilter@41041c31, org.springframework.web.filter.CorsFilter@29390110, org.springframework.security.web.authentication.logout.LogoutFilter@2eb0932c, com.example.pure.filter.JwtFilter@6081f330, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6939f18a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@8bd9d08, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b89515b, org.springframework.security.web.session.SessionManagementFilter@34a482d0, org.springframework.security.web.access.ExceptionTranslationFilter@30ce78e3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@c2ddfeb]
2025-07-09 13:59:20.889 [39mTRACE[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@308a6984, started on Wed Jul 09 13:59:14 CST 2025
2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AIModelController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-09 13:59:20.905 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-09 13:59:20.906 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-09 13:59:20.906 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-09 13:59:20.908 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-07-09 13:59:20.909 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-09 13:59:20.909 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-09 13:59:20.909 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-09 13:59:20.909 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-09 13:59:20.909 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-09 13:59:20.909 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-09 13:59:20.910 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-09 13:59:20.910 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-09 13:59:20.911 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-09 13:59:20.911 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-09 13:59:20.911 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-09 13:59:20.990 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-09 13:59:21.017 [39mDEBUG[0;39m 24524 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-09 13:59:21.243 [34mINFO [0;39m 24524 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 13:59:21.252 [34mINFO [0;39m 24524 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 13:59:21.254 [39mDEBUG[0;39m 24524 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-09 13:59:21.254 [39mDEBUG[0;39m 24524 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-09 13:59:21.254 [34mINFO [0;39m 24524 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-09 13:59:21.254 [39mDEBUG[0;39m 24524 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2620264e]
2025-07-09 13:59:21.254 [39mDEBUG[0;39m 24524 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2620264e]
2025-07-09 13:59:21.254 [34mINFO [0;39m 24524 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2620264e]]
2025-07-09 13:59:21.254 [34mINFO [0;39m 24524 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-09 13:59:21.254 [39mDEBUG[0;39m 24524 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 13:59:21.254 [39mDEBUG[0;39m 24524 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 13:59:21.266 [34mINFO [0;39m 24524 --- [main] com.example.pure.PureApplication : Started PureApplication in 7.266 seconds (JVM running for 8.221)
2025-07-09 13:59:53.836 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 13:59:53.837 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-09 13:59:53.837 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-09 13:59:53.837 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-09 13:59:53.837 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-09 13:59:53.838 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7089f717
2025-07-09 13:59:53.838 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@6b49ee65
2025-07-09 13:59:53.838 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-09 13:59:53.838 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 1 ms
2025-07-09 13:59:53.851 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing GET /api/ai/stream
2025-07-09 13:59:53.854 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-09 13:59:53.865 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AIModelController#getAIModelStream(String, Long)
2025-07-09 13:59:53.917 [31mWARN [0;39m 24524 --- [http-nio-8080-exec-2] com.example.pure.util.JwtUtil : JWT已过期: JWT expired at 2025-04-04T13:50:21Z. Current time: 2025-07-09T05:59:53Z, a difference of 8266172915 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-09 13:59:53.918 [31mWARN [0;39m 24524 --- [http-nio-8080-exec-2] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: JWT令牌已过期, URI: /api/ai/stream
2025-07-09 13:59:53.949 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-09 14:00:15.388 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing GET /api/ai/stream
2025-07-09 14:00:15.388 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-09 14:00:15.389 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AIModelController#getAIModelStream(String, Long)
2025-07-09 14:00:15.706 [1;31mERROR[0;39m 24524 --- [http-nio-8080-exec-4] c.e.p.service.impl.DeviceServiceImpl : 在Redis中查找匹配的哈希令牌时出错: Key - user:devices:accessToken:Haohao268826
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1093)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:421)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultHashOperations.keys(DefaultHashOperations.java:164)
	at com.example.pure.util.RedisUtil.getHashKeys(RedisUtil.java:241)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.findMatchingHashedToken(DeviceServiceImpl.java:230)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.isAccessTokenValid(DeviceServiceImpl.java:156)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.base/java.util.Optional.orElseGet(Optional.java:369)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 74 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:777)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-07-09 14:00:15.707 [31mWARN [0;39m 24524 --- [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 的Token无效，此设备已失效
2025-07-09 14:00:15.707 [31mWARN [0;39m 24524 --- [http-nio-8080-exec-4] c.e.p.h.CustomAuthenticationEntryPoint : 认证失败: 您的账号已在其他设备登录，当前设备已失效, URI: /api/ai/stream
2025-07-09 14:00:15.708 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-09 14:00:20.877 [34mINFO [0;39m 24524 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-09 14:01:12.091 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing GET /api/ai/stream
2025-07-09 14:01:12.091 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-09 14:01:12.091 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AIModelController#getAIModelStream(String, Long)
2025-07-09 14:01:12.246 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-8] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-09 14:01:12.481 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-8] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-09 14:01:12.489 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: Haohao268826
2025-07-09 14:01:12.506 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-09 14:01:12.509 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d]
2025-07-09 14:01:12.516 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1918372787 wrapping com.mysql.cj.jdbc.ConnectionImpl@3a4aef59] will be managed by Spring
2025-07-09 14:01:12.519 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-09 14:01:12.536 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: Haohao268826(String)
2025-07-09 14:01:12.558 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-09 14:01:12.561 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d]
2025-07-09 14:01:12.562 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 23,password: $2a$10$H7BoMWfx02YdgE/Gv5mm4u0OpF89c/F//rX55RVrdRnvJ6.kPFCn6
2025-07-09 14:01:12.562 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d] from current transaction
2025-07-09 14:01:12.562 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-09 14:01:12.563 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 23(Long)
2025-07-09 14:01:12.566 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-09 14:01:12.567 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d]
2025-07-09 14:01:12.568 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 Haohao268826 的角色信息已加载, 角色数量: 1
2025-07-09 14:01:12.568 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d]
2025-07-09 14:01:12.568 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d]
2025-07-09 14:01:12.568 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17836e5d]
2025-07-09 14:01:12.591 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'Haohao268826' 认证成功
2025-07-09 14:01:12.596 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [GET /api/ai/stream] with attributes [authenticated]
2025-07-09 14:01:12.596 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured GET /api/ai/stream
2025-07-09 14:01:12.599 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : GET "/api/ai/stream", parameters={}
2025-07-09 14:01:12.601 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.AIModelController#getAIModelStream(String, Long)
2025-07-09 14:01:12.665 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.controller.AIModelController : 接收到AI模型SSE请求 - 消息: [无消息], 延迟: 100ms
2025-07-09 14:01:12.665 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-8] c.e.p.s.impl.AIModelServiceImpl : 开始AI模型SSE响应 - 用户消息: [无消息], 延迟: 100ms
2025-07-09 14:01:12.674 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-09 14:01:12.679 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-09 14:01:12.680 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-09 14:01:22.465 [39mDEBUG[0;39m 24524 --- [ioIntensiveTask-async-1] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai/stream
2025-07-09 14:01:22.466 [34mINFO [0;39m 24524 --- [ioIntensiveTask-async-1] c.e.p.s.impl.AIModelServiceImpl : AI模型SSE响应发送完成 - 总字符数: 91
2025-07-09 14:01:22.468 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing GET /api/ai/stream
2025-07-09 14:01:22.468 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-09 14:01:22.468 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 14:01:22.468 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured GET /api/ai/stream
2025-07-09 14:01:22.468 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for GET "/api/ai/stream", parameters={}
2025-07-09 14:01:22.469 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-09 14:01:22.471 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-09 14:01:22.471 [39mDEBUG[0;39m 24524 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-09 14:01:22.472 [34mINFO [0;39m 24524 --- [http-nio-8080-exec-5] c.e.p.s.impl.AIModelServiceImpl : AI模型SSE响应完成
2025-07-09 14:02:58.133 [39mDEBUG[0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-09 14:02:58.135 [39mDEBUG[0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-09 14:02:58.135 [34mINFO [0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-09 14:02:58.135 [34mINFO [0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2620264e]]
2025-07-09 14:02:58.135 [39mDEBUG[0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2620264e]
2025-07-09 14:02:58.135 [39mDEBUG[0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2620264e]
2025-07-09 14:02:58.135 [34mINFO [0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-09 14:02:58.135 [39mDEBUG[0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 14:02:58.135 [39mDEBUG[0;39m 24524 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 14:02:58.546 [34mINFO [0;39m 24524 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-09 14:02:58.555 [34mINFO [0;39m 24524 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-09 14:24:46.808 [34mINFO [0;39m 8636 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 8636 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-09 14:24:46.808 [34mINFO [0;39m 8636 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 14:24:46.812 [39mDEBUG[0;39m 8636 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 14:24:46.812 [34mINFO [0;39m 8636 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-09 14:24:48.075 [34mINFO [0;39m 8636 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-09 14:24:48.077 [34mINFO [0;39m 8636 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-09 14:24:48.111 [34mINFO [0;39m 8636 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-09 14:24:48.204 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-09 14:24:48.206 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-09 14:24:48.207 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-09 14:24:48.208 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-09 14:24:48.209 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-09 14:24:48.210 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-09 14:24:48.210 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-09 14:24:48.210 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-09 14:24:48.210 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-09 14:24:48.210 [39mDEBUG[0;39m 8636 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-09 14:24:48.683 [34mINFO [0;39m 8636 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-09 14:24:48.688 [34mINFO [0;39m 8636 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 14:24:48.689 [34mINFO [0;39m 8636 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-09 14:24:48.689 [34mINFO [0;39m 8636 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 14:24:48.787 [34mINFO [0;39m 8636 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-09 14:24:48.787 [34mINFO [0;39m 8636 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1922 ms
2025-07-09 14:24:49.041 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-09 14:24:49.057 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-09 14:24:49.065 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-09 14:24:49.076 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-09 14:24:49.089 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-09 14:24:49.096 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-09 14:24:49.106 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-09 14:24:49.113 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-09 14:24:49.131 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-09 14:24:49.140 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-09 14:24:49.157 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-09 14:24:49.163 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-09 14:24:49.169 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-09 14:24:49.175 [39mDEBUG[0;39m 8636 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-09 14:24:49.197 [34mINFO [0;39m 8636 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-09 14:24:49.547 [34mINFO [0;39m 8636 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-09 14:24:50.342 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-09 14:24:50.343 [39mDEBUG[0;39m 8636 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-09 14:24:50.604 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-09 14:24:50.606 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.008 [34mINFO [0;39m 8636 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-09 14:24:51.122 [34mINFO [0;39m 8636 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-09 14:24:51.129 [34mINFO [0;39m 8636 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-09 14:24:51.183 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-09 14:24:51.183 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.184 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-09 14:24:51.185 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.185 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-09 14:24:51.186 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.186 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-09 14:24:51.186 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.186 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-09 14:24:51.186 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.186 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-09 14:24:51.187 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.190 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-09 14:24:51.190 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.190 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-09 14:24:51.190 [39mDEBUG[0;39m 8636 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 14:24:51.288 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-09 14:24:51.290 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-09 14:24:51.295 [34mINFO [0;39m 8636 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@4e3bc13b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c989d82, org.springframework.security.web.context.SecurityContextPersistenceFilter@162a5c4c, org.springframework.security.web.header.HeaderWriterFilter@69d9d322, org.springframework.security.web.authentication.logout.LogoutFilter@6e8f3b76, com.example.pure.filter.JwtFilter@d1d85d0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6f9e6a85, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2e17a9e6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2e04c89d, org.springframework.security.web.session.SessionManagementFilter@235d46b2, org.springframework.security.web.access.ExceptionTranslationFilter@5a0b550a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@77430c79]
2025-07-09 14:24:51.297 [34mINFO [0;39m 8636 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-09 14:24:51.298 [34mINFO [0;39m 8636 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-09 14:24:51.299 [34mINFO [0;39m 8636 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-09 14:24:51.435 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-09 14:24:51.451 [34mINFO [0;39m 8636 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-09 14:24:51.513 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-07-09 14:24:51.524 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-09 14:24:51.856 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-09 14:24:51.951 [34mINFO [0;39m 8636 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-09 14:24:51.970 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-09 14:24:51.971 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-09 14:24:51.972 [39mDEBUG[0;39m 8636 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-09 14:24:51.973 [34mINFO [0;39m 8636 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@490dfe25, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7aff1db3, org.springframework.security.web.context.SecurityContextPersistenceFilter@6037748a, org.springframework.security.web.header.HeaderWriterFilter@c7aac7c, org.springframework.web.filter.CorsFilter@46374b8, org.springframework.security.web.authentication.logout.LogoutFilter@1483c738, com.example.pure.filter.JwtFilter@d1d85d0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4d87e7f3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@14c7ab73, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@29ceefb3, org.springframework.security.web.session.SessionManagementFilter@7ab8f93, org.springframework.security.web.access.ExceptionTranslationFilter@7e4a6afd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ed87b1c]
2025-07-09 14:24:52.005 [39mTRACE[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5a12c728, started on Wed Jul 09 14:24:46 CST 2025
2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-09 14:24:52.016 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-09 14:24:52.017 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-09 14:24:52.017 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-09 14:24:52.017 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-09 14:24:52.017 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-09 14:24:52.017 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-09 14:24:52.017 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-09 14:24:52.019 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-09 14:24:52.020 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-07-09 14:24:52.021 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-09 14:24:52.021 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-09 14:24:52.021 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-09 14:24:52.021 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-09 14:24:52.100 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-09 14:24:52.133 [39mDEBUG[0;39m 8636 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-09 14:24:52.380 [34mINFO [0;39m 8636 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 14:24:52.389 [34mINFO [0;39m 8636 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 14:24:52.390 [39mDEBUG[0;39m 8636 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-09 14:24:52.390 [39mDEBUG[0;39m 8636 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-09 14:24:52.390 [34mINFO [0;39m 8636 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-09 14:24:52.391 [39mDEBUG[0;39m 8636 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@86abdbd]
2025-07-09 14:24:52.391 [39mDEBUG[0;39m 8636 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@86abdbd]
2025-07-09 14:24:52.391 [34mINFO [0;39m 8636 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@86abdbd]]
2025-07-09 14:24:52.391 [34mINFO [0;39m 8636 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-09 14:24:52.391 [39mDEBUG[0;39m 8636 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 14:24:52.391 [39mDEBUG[0;39m 8636 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 14:24:52.403 [34mINFO [0;39m 8636 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.08 seconds (JVM running for 6.883)
2025-07-09 14:25:51.990 [34mINFO [0;39m 8636 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-09 14:55:51.999 [34mINFO [0;39m 8636 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-09 15:15:47.171 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 15:15:47.172 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-09 15:15:47.173 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 1 ms
2025-07-09 15:15:47.319 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-2] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: hello!, 会话ID: null
2025-07-09 15:15:47.323 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752045347320_131c8ca5, 消息: hello!
2025-07-09 15:15:49.732 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752045347320_131c8ca5
2025-07-09 15:16:26.002 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-5] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: java, 会话ID: null
2025-07-09 15:16:26.002 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-5] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752045386002_6710c524, 消息: java
2025-07-09 15:16:31.436 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-5] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752045386002_6710c524
2025-07-09 15:16:55.566 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-6] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: 你好, 会话ID: null
2025-07-09 15:16:55.566 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-6] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752045415566_b0596e3c, 消息: 你好
2025-07-09 15:16:58.126 [34mINFO [0;39m 8636 --- [http-nio-8080-exec-6] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752045415566_b0596e3c
2025-07-09 15:25:52.014 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-07-09 15:55:52.025 [34mINFO [0;39m 8636 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-07-09 16:25:52.034 [34mINFO [0;39m 8636 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-07-09 16:55:52.043 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-07-09 17:25:52.049 [34mINFO [0;39m 8636 --- [MessageBroker-4] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-07-09 17:55:52.056 [34mINFO [0;39m 8636 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 8, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-07-09 18:25:52.065 [34mINFO [0;39m 8636 --- [MessageBroker-5] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 9, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-07-09 18:55:52.076 [34mINFO [0;39m 8636 --- [MessageBroker-3] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
2025-07-09 19:25:52.082 [34mINFO [0;39m 8636 --- [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
2025-07-09 19:55:52.094 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 11]
2025-07-09 20:38:05.826 [31mWARN [0;39m 8636 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=20m10s760ms996µs).
2025-07-09 20:45:32.732 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 12]
2025-07-09 21:15:32.739 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 13]
2025-07-09 21:45:32.749 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 14]
2025-07-09 22:15:32.763 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 15]
2025-07-09 22:45:32.770 [34mINFO [0;39m 8636 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 16]
2025-07-09 22:55:27.342 [34mINFO [0;39m 8636 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-09 22:55:27.343 [34mINFO [0;39m 8636 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@86abdbd]]
2025-07-09 22:55:27.343 [34mINFO [0;39m 8636 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-09 22:55:27.934 [34mINFO [0;39m 8636 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-09 22:55:27.945 [34mINFO [0;39m 8636 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-09 22:55:35.343 [34mINFO [0;39m 16004 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 16004 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-07-09 22:55:35.345 [34mINFO [0;39m 16004 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-09 22:55:35.345 [39mDEBUG[0;39m 16004 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 22:55:35.346 [34mINFO [0;39m 16004 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-09 22:55:36.285 [34mINFO [0;39m 16004 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-09 22:55:36.288 [34mINFO [0;39m 16004 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-09 22:55:36.325 [34mINFO [0;39m 16004 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-09 22:55:36.435 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-09 22:55:36.436 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-09 22:55:36.438 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-09 22:55:36.438 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-09 22:55:36.438 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-09 22:55:36.438 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-09 22:55:36.438 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-09 22:55:36.438 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-09 22:55:36.439 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-09 22:55:36.439 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-09 22:55:36.439 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-09 22:55:36.439 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-09 22:55:36.439 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-09 22:55:36.439 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-09 22:55:36.440 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-09 22:55:36.441 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-09 22:55:36.441 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-09 22:55:36.441 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-09 22:55:36.441 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-09 22:55:36.441 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-09 22:55:36.441 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-09 22:55:36.442 [39mDEBUG[0;39m 16004 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-09 22:55:36.905 [34mINFO [0;39m 16004 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-09 22:55:36.910 [34mINFO [0;39m 16004 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-09 22:55:36.911 [34mINFO [0;39m 16004 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-09 22:55:36.911 [34mINFO [0;39m 16004 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 22:55:37.001 [34mINFO [0;39m 16004 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-09 22:55:37.001 [34mINFO [0;39m 16004 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1613 ms
2025-07-09 22:55:37.226 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-09 22:55:37.238 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-09 22:55:37.245 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-09 22:55:37.252 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-09 22:55:37.264 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-09 22:55:37.269 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-09 22:55:37.275 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-09 22:55:37.287 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-09 22:55:37.293 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-09 22:55:37.300 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-09 22:55:37.313 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-07-09 22:55:37.321 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-09 22:55:37.325 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-09 22:55:37.332 [39mDEBUG[0;39m 16004 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-09 22:55:37.348 [34mINFO [0;39m 16004 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-09 22:55:37.684 [34mINFO [0;39m 16004 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-09 22:55:38.314 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-09 22:55:38.315 [39mDEBUG[0;39m 16004 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-09 22:55:38.713 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-09 22:55:38.716 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.178 [34mINFO [0;39m 16004 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-09 22:55:39.302 [34mINFO [0;39m 16004 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-09 22:55:39.310 [34mINFO [0;39m 16004 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-09 22:55:39.363 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-09 22:55:39.363 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.364 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-09 22:55:39.364 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.364 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-09 22:55:39.365 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.365 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-09 22:55:39.365 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.366 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-09 22:55:39.366 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.366 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-09 22:55:39.366 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.369 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-09 22:55:39.369 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.369 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-09 22:55:39.370 [39mDEBUG[0;39m 16004 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-09 22:55:39.470 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-09 22:55:39.472 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-09 22:55:39.477 [34mINFO [0;39m 16004 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2d6e09f0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@257da215, org.springframework.security.web.context.SecurityContextPersistenceFilter@94b5fe3, org.springframework.security.web.header.HeaderWriterFilter@1db9d253, org.springframework.security.web.authentication.logout.LogoutFilter@7894a250, com.example.pure.filter.JwtFilter@687f62a5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3811464b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5f0a5848, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5aa461, org.springframework.security.web.session.SessionManagementFilter@4e00723b, org.springframework.security.web.access.ExceptionTranslationFilter@f3c27e9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@48cbb4c5]
2025-07-09 22:55:39.480 [34mINFO [0;39m 16004 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-09 22:55:39.481 [34mINFO [0;39m 16004 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-09 22:55:39.483 [34mINFO [0;39m 16004 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-09 22:55:39.659 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-09 22:55:39.681 [34mINFO [0;39m 16004 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-09 22:55:39.757 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 81 mappings in 'requestMappingHandlerMapping'
2025-07-09 22:55:39.766 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-09 22:55:40.103 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-09 22:55:40.248 [34mINFO [0;39m 16004 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-09 22:55:40.271 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-09 22:55:40.272 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-09 22:55:40.273 [39mDEBUG[0;39m 16004 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-09 22:55:40.274 [34mINFO [0;39m 16004 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@c9234a0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46bbcb82, org.springframework.security.web.context.SecurityContextPersistenceFilter@19a544cd, org.springframework.security.web.header.HeaderWriterFilter@f776b4a, org.springframework.web.filter.CorsFilter@b91e024, org.springframework.security.web.authentication.logout.LogoutFilter@6e669b5c, com.example.pure.filter.JwtFilter@687f62a5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6c502018, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@58c93be3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@309ac89e, org.springframework.security.web.session.SessionManagementFilter@2fb555e8, org.springframework.security.web.access.ExceptionTranslationFilter@3338d706, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6b3a1151]
2025-07-09 22:55:40.311 [39mTRACE[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@308a6984, started on Wed Jul 09 22:55:35 CST 2025
2025-07-09 22:55:40.327 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AiChatController:

2025-07-09 22:55:40.327 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.AuthController:

2025-07-09 22:55:40.327 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.DownloadController:

2025-07-09 22:55:40.327 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.FileManagerController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.ImageController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.MessagesController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OAuth2Controller:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.OperatingLogController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureFileManagerController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureImageFileController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoInteractionController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.PureVideoUrlController:

2025-07-09 22:55:40.328 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRCodeController:

2025-07-09 22:55:40.331 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-07-09 22:55:40.332 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.R2Controller:

2025-07-09 22:55:40.332 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserController:

2025-07-09 22:55:40.332 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.UserProfileController:

2025-07-09 22:55:40.332 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VerificationController:

2025-07-09 22:55:40.332 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoCommentInteractionController:

2025-07-09 22:55:40.333 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.VideoController:

2025-07-09 22:55:40.333 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-09 22:55:40.334 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.b.a.w.s.e.BasicErrorController:

2025-07-09 22:55:40.334 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.a.OpenApiWebMvcResource:

2025-07-09 22:55:40.335 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-07-09 22:55:40.335 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler :
	o.s.w.u.SwaggerConfigResource:

2025-07-09 22:55:40.432 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-09 22:55:40.460 [39mDEBUG[0;39m 16004 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-09 22:55:40.693 [34mINFO [0;39m 16004 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-09 22:55:40.702 [34mINFO [0;39m 16004 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 22:55:40.703 [39mDEBUG[0;39m 16004 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-09 22:55:40.703 [39mDEBUG[0;39m 16004 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-09 22:55:40.704 [34mINFO [0;39m 16004 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-09 22:55:40.704 [39mDEBUG[0;39m 16004 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5c519da4]
2025-07-09 22:55:40.704 [39mDEBUG[0;39m 16004 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5c519da4]
2025-07-09 22:55:40.704 [34mINFO [0;39m 16004 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5c519da4]]
2025-07-09 22:55:40.704 [34mINFO [0;39m 16004 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-09 22:55:40.704 [39mDEBUG[0;39m 16004 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 22:55:40.704 [39mDEBUG[0;39m 16004 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-09 22:55:40.716 [34mINFO [0;39m 16004 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.885 seconds (JVM running for 6.769)
2025-07-09 22:55:50.441 [34mINFO [0;39m 16004 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 22:55:50.441 [34mINFO [0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-09 22:55:50.441 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-09 22:55:50.442 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-09 22:55:50.442 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-09 22:55:50.443 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@425a6528
2025-07-09 22:55:50.444 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@44614b1e
2025-07-09 22:55:50.444 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-09 22:55:50.444 [34mINFO [0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-07-09 22:55:50.457 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-09 22:55:50.460 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-09 22:55:50.468 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-09 22:55:50.471 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 22:55:50.471 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.s.SessionManagementFilter : Request requested invalid session id 21854CD7CFB03141F8DEF8449E40FCDA
2025-07-09 22:55:50.476 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai-chat/stream] with attributes [permitAll]
2025-07-09 22:55:50.476 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-09 22:55:50.479 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/ai-chat/stream", parameters={}
2025-07-09 22:55:50.481 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiChatController#chatStream(AiChatRequest, Authentication)
2025-07-09 22:55:50.540 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [AiChatRequest(message=你好, sessionId=null, messageType=chat)]
2025-07-09 22:55:50.606 [34mINFO [0;39m 16004 --- [http-nio-8080-exec-2] c.e.pure.controller.AiChatController : 收到AI聊天流式请求 - 用户: 匿名用户, 消息: 你好, 会话ID: null
2025-07-09 22:55:50.610 [34mINFO [0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 开始处理AI聊天请求 - 会话ID: session_1752072950607_fae0ce5d, 消息: 你好
2025-07-09 22:55:50.626 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送start控制事件 - 会话ID: session_1752072950607_fae0ce5d
2025-07-09 22:55:50.626 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 1
2025-07-09 22:55:50.698 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 2
2025-07-09 22:55:50.775 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 3
2025-07-09 22:55:50.844 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 4
2025-07-09 22:55:50.980 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 5
2025-07-09 22:55:51.033 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 6
2025-07-09 22:55:51.085 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 7
2025-07-09 22:55:51.171 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 8
2025-07-09 22:55:51.236 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 9
2025-07-09 22:55:51.317 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 10
2025-07-09 22:55:51.439 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 11
2025-07-09 22:55:51.573 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 12
2025-07-09 22:55:51.678 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 13
2025-07-09 22:55:51.807 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 14
2025-07-09 22:55:51.924 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 15
2025-07-09 22:55:51.985 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 16
2025-07-09 22:55:52.072 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 17
2025-07-09 22:55:52.184 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 18
2025-07-09 22:55:52.269 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 19
2025-07-09 22:55:52.331 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 20
2025-07-09 22:55:52.392 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 21
2025-07-09 22:55:52.464 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 22
2025-07-09 22:55:52.555 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送文本事件 - 会话ID: session_1752072950607_fae0ce5d, 内容长度: 23
2025-07-09 22:55:52.619 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : 发送end控制事件 - 会话ID: session_1752072950607_fae0ce5d
2025-07-09 22:55:52.619 [34mINFO [0;39m 16004 --- [http-nio-8080-exec-2] c.e.p.service.impl.AiChatServiceImpl : AI聊天请求处理完成 - 会话ID: session_1752072950607_fae0ce5d
2025-07-09 22:55:52.628 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.w.c.r.async.WebAsyncManager : Started async request
2025-07-09 22:55:52.638 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.w.c.r.async.WebAsyncManager : Async result set, dispatch to /api/ai-chat/stream
2025-07-09 22:55:52.639 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Exiting but response remains open for further handling
2025-07-09 22:55:52.640 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-09 22:55:52.642 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/ai-chat/stream
2025-07-09 22:55:52.642 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-09 22:55:52.642 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 22:55:52.643 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/ai-chat/stream
2025-07-09 22:55:52.643 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : "ASYNC" dispatch for POST "/api/ai-chat/stream", parameters={}
2025-07-09 22:55:52.643 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-07-09 22:55:52.646 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Exiting from "ASYNC" dispatch, status 200
2025-07-09 22:55:52.646 [39mDEBUG[0;39m 16004 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-09 22:56:40.296 [34mINFO [0;39m 16004 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-09 23:26:40.303 [34mINFO [0;39m 16004 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-09 23:56:40.314 [34mINFO [0;39m 16004 --- [MessageBroker-2] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
