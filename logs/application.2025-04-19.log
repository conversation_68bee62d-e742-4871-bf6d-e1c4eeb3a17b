2025-04-19 16:48:00.584 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 16:48:00.586 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 15072 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 16:48:00.589 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 16:48:00.589 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 16:48:01.459 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 16:48:01.460 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 16:48:01.488 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-04-19 16:48:01.564 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 16:48:01.564 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 16:48:01.564 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 16:48:01.565 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 16:48:01.566 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 16:48:01.566 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 16:48:01.566 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 16:48:01.566 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 16:48:01.566 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 16:48:02.030 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 16:48:02.121 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1485 ms
2025-04-19 16:48:02.311 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 16:48:02.322 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 16:48:02.333 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 16:48:02.341 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 16:48:02.450 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 16:48:02.798 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 16:48:02.929 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 16:48:02.932 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:02.938 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:02.939 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 16:48:02.940 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:03.017 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 16:48:03.018 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 16:48:03.022 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@267891bf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a109ff7, org.springframework.security.web.context.SecurityContextPersistenceFilter@2baac4a7, org.springframework.security.web.header.HeaderWriterFilter@22f8adc2, org.springframework.security.web.authentication.logout.LogoutFilter@2c60ce47, com.example.pure.filter.JwtFilter@6d69a0d3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6bce4140, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5882b202, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@111a7973, org.springframework.security.web.session.SessionManagementFilter@6ef4cbe1, org.springframework.security.web.access.ExceptionTranslationFilter@230232b0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4b4814d0]
2025-04-19 16:48:03.023 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 16:48:03.024 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 16:48:03.163 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 16:48:03.178 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 16:48:03.224 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-19 16:48:03.232 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 16:48:38.848 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 17908 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 16:48:38.848 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 16:48:38.850 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 16:48:38.850 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 16:48:39.581 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 16:48:39.583 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 16:48:39.609 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-04-19 16:48:39.682 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 16:48:39.682 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 16:48:39.682 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 16:48:39.682 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 16:48:39.683 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 16:48:39.683 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 16:48:39.684 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 16:48:39.684 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 16:48:39.684 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 16:48:40.065 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 16:48:40.069 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-19 16:48:40.070 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-19 16:48:40.070 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-19 16:48:40.151 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-19 16:48:40.151 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1269 ms
2025-04-19 16:48:40.324 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 16:48:40.334 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 16:48:40.345 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 16:48:40.353 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 16:48:40.440 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 16:48:40.745 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 16:48:40.865 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 16:48:40.867 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.872 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 16:48:40.873 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 16:48:40.874 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 16:48:40.937 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 16:48:40.938 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 16:48:40.941 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@70b33256, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@33d230ce, org.springframework.security.web.context.SecurityContextPersistenceFilter@52621501, org.springframework.security.web.header.HeaderWriterFilter@24197b13, org.springframework.security.web.authentication.logout.LogoutFilter@6f315b08, com.example.pure.filter.JwtFilter@5968800d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@164d01ba, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5ac044ef, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35e74e08, org.springframework.security.web.session.SessionManagementFilter@7a358613, org.springframework.security.web.access.ExceptionTranslationFilter@2b7facc7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1144a02b]
2025-04-19 16:48:40.942 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 16:48:40.943 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 16:48:41.062 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 16:48:41.076 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 16:48:41.116 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-19 16:48:41.122 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 16:48:41.515 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-19 16:48:41.596 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-19 16:48:41.615 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-19 16:48:41.616 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-19 16:48:41.617 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-19 16:48:41.617 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-19 16:48:41.617 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@191f4d65, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a2a7492, org.springframework.security.web.context.SecurityContextPersistenceFilter@6b25ef1c, org.springframework.security.web.header.HeaderWriterFilter@727986ad, org.springframework.web.filter.CorsFilter@6326c5ec, org.springframework.security.web.authentication.logout.LogoutFilter@3f0ce0d1, com.example.pure.filter.JwtFilter@5968800d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@56b1e527, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@68c34db2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@138a85d3, org.springframework.security.web.session.SessionManagementFilter@1600a8a2, org.springframework.security.web.access.ExceptionTranslationFilter@6c9a3661, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@21e484b]
2025-04-19 16:48:41.645 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3bcd05cb, started on Sat Apr 19 16:48:38 CST 2025
2025-04-19 16:48:41.656 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-19 16:48:41.656 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-19 16:48:41.656 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-19 16:48:41.656 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-19 16:48:41.656 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-19 16:48:41.656 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-19 16:48:41.658 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-19 16:48:41.659 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-19 16:48:41.659 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-19 16:48:41.659 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-19 16:48:41.660 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-19 16:48:41.660 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-19 16:48:41.660 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-19 16:48:41.660 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-19 16:48:41.695 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-19 16:48:41.718 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-19 16:48:41.902 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-19 16:48:41.914 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-19 16:48:41.916 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 16:48:41.916 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 16:48:41.916 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-19 16:48:41.916 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@527d48db]
2025-04-19 16:48:41.916 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@527d48db]
2025-04-19 16:48:41.916 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@527d48db]]
2025-04-19 16:48:41.916 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-19 16:48:41.916 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 16:48:41.917 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 16:48:42.036 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.53 seconds (JVM running for 4.088)
2025-04-19 16:49:41.643 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-19 16:50:03.031 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-19 16:50:03.031 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-19 16:50:03.031 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-19 16:50:03.031 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-19 16:50:03.031 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-19 16:50:03.032 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1dc67fac
2025-04-19 16:50:03.032 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@1bca2987
2025-04-19 16:50:03.032 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-19 16:50:03.032 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-19 16:50:03.042 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-19 16:50:03.044 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 16:50:03.051 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-19 16:50:03.053 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-19 16:50:03.057 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-19 16:50:03.058 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-19 16:50:03.059 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-19 16:50:03.060 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-19 16:50:03.108 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=23adfa126662, password=12345678, email=null)]
2025-04-19 16:50:05.549 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-04-19 16:50:05.551 [http-nio-8080-exec-2] ERROR c.e.d.e.GlobalExceptionHandler - 系统异常
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1093)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:421)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.example.pure.util.IpUtil.getClientIp(IpUtil.java:42)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:108)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d3ef747e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$54568f60.login(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 123 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-04-19 16:50:05.564 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 16:50:05.565 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [Result(code=500, message=系统异常，请联系管理员, data=null)]
2025-04-19 16:50:05.576 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379]
2025-04-19 16:50:05.576 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 500 INTERNAL_SERVER_ERROR
2025-04-19 16:50:05.577 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 16:51:07.456 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-19 16:51:07.456 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 16:51:07.457 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-19 16:51:07.457 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-19 16:51:07.457 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-19 16:51:07.457 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-19 16:51:07.457 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-19 16:51:07.457 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-19 16:51:07.458 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=23adfa126662, password=12345678, email=null)]
2025-04-19 16:51:09.498 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-04-19 16:51:09.499 [http-nio-8080-exec-4] ERROR c.e.d.e.GlobalExceptionHandler - 系统异常
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1093)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:421)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.example.pure.util.IpUtil.getClientIp(IpUtil.java:42)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:108)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d3ef747e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$54568f60.login(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 123 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-04-19 16:51:09.499 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 16:51:09.500 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [Result(code=500, message=系统异常，请联系管理员, data=null)]
2025-04-19 16:51:09.500 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379]
2025-04-19 16:51:09.500 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 500 INTERNAL_SERVER_ERROR
2025-04-19 16:51:09.500 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 16:53:00.692 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-04-19 16:53:00.692 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 16:53:00.693 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-19 16:53:00.693 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-19 16:53:00.693 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-04-19 16:53:00.693 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-04-19 16:53:00.693 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/login", parameters={}
2025-04-19 16:53:00.694 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-04-19 16:53:00.694 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginRequest(username=23adfa126662, password=12345678, email=null)]
2025-04-19 16:53:00.767 [http-nio-8080-exec-6] INFO  c.e.demo13.controller.AuthController - 用户登录: 23adfa126662
2025-04-19 16:53:00.767 [http-nio-8080-exec-6] DEBUG c.e.d.service.impl.AuthServiceImpl - 处理用户登录请求: 23adfa126662
2025-04-19 16:53:00.840 [http-nio-8080-exec-6] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 16:53:00.851 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 16:53:00.854 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9]
2025-04-19 16:53:00.860 [http-nio-8080-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@********] will be managed by Spring
2025-04-19 16:53:00.861 [http-nio-8080-exec-6] DEBUG c.e.d.m.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, /* Check if password is loaded */ email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-19 16:53:00.878 [http-nio-8080-exec-6] DEBUG c.e.d.m.UserMapper.findByUsername - ==> Parameters: 23adfa126662(String)
2025-04-19 16:53:00.897 [http-nio-8080-exec-6] DEBUG c.e.d.m.UserMapper.findByUsername - <==      Total: 1
2025-04-19 16:53:00.898 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9]
2025-04-19 16:53:00.899 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9] from current transaction
2025-04-19 16:53:00.899 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 16:53:00.899 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 16:53:00.904 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 16:53:00.904 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9]
2025-04-19 16:53:00.904 [http-nio-8080-exec-6] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 16:53:00.905 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9]
2025-04-19 16:53:00.905 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9]
2025-04-19 16:53:00.905 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@598d4ca9]
2025-04-19 16:53:01.165 [http-nio-8080-exec-6] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-04-19 16:53:01.229 [http-nio-8080-exec-6] INFO  c.e.d.service.impl.DeviceServiceImpl - 用户 23adfa126662 设备数量已达上限，移除最早登录的设备: 497c2dcd-f476-425a-aa70-ba9fb7f0b2d3
2025-04-19 16:53:01.234 [http-nio-8080-exec-6] INFO  c.e.d.service.impl.DeviceServiceImpl - 通过token移除用户 23adfa126662 设备成功
2025-04-19 16:53:01.238 [http-nio-8080-exec-6] INFO  c.e.d.service.impl.DeviceServiceImpl - 用户 23adfa126662 添加设备 de943dfa-0824-4cda-a7fa-e5582b8a76a1 成功，使用token作为标识
2025-04-19 16:53:01.238 [http-nio-8080-exec-6] INFO  c.e.d.service.impl.AuthServiceImpl - 用户 23adfa126662 登录设备超过限制，已移除最早登录的设备: eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************.qtGnDniK86K438Rn3OJ5q4Vc3DyUR6d2SCVZtXJh9Co
2025-04-19 16:53:01.246 [http-nio-8080-exec-6] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-19 16:53:01.251 [http-nio-8080-exec-6] DEBUG c.e.d.service.impl.UserServiceImpl - 更新用户基本信息: 23adfa126662
2025-04-19 16:53:01.251 [http-nio-8080-exec-6] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 1
2025-04-19 16:53:01.252 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 16:53:01.252 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b]
2025-04-19 16:53:01.252 [http-nio-8080-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@********] will be managed by Spring
2025-04-19 16:53:01.252 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.UserMapper.findById - ==>  Preparing: SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE id = ?
2025-04-19 16:53:01.253 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.UserMapper.findById - ==> Parameters: 1(Long)
2025-04-19 16:53:01.254 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.UserMapper.findById - <==      Total: 1
2025-04-19 16:53:01.254 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b]
2025-04-19 16:53:01.254 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b] from current transaction
2025-04-19 16:53:01.266 [http-nio-8080-exec-6] DEBUG c.e.demo13.mapper.UserMapper.update - ==>  Preparing: UPDATE user SET username = ?, password = ?, email = ?, nickname = ?, avatar = ?, gender = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, update_time = NOW() WHERE id = ?
2025-04-19 16:53:01.266 [http-nio-8080-exec-6] DEBUG c.e.demo13.mapper.UserMapper.update - ==> Parameters: 23adfa126662(String), $2a$12$zZJ.NzKo/56aVFdL5CCu7up/dX2v.BH0sr3KHGCIake0V/UYbH2oS(String), <EMAIL>(String), nihaohao(String), a(String), 1(Integer), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-04-19T16:53:01.251(LocalDateTime), eyJhbGciOiJIUzI1NiJ9.***************************************************************************.4XRt_2FvorG5Xc_IB1aL263VexyWnzcVcjCv6_RgNxY(String), 1(Long)
2025-04-19 16:53:01.270 [http-nio-8080-exec-6] DEBUG c.e.demo13.mapper.UserMapper.update - <==    Updates: 1
2025-04-19 16:53:01.270 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b]
2025-04-19 16:53:01.271 [http-nio-8080-exec-6] DEBUG c.e.d.service.impl.UserServiceImpl - 用户基本信息更新成功: 23adfa126662
2025-04-19 16:53:01.271 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b]
2025-04-19 16:53:01.271 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b]
2025-04-19 16:53:01.271 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a9d750b]
2025-04-19 16:53:01.276 [http-nio-8080-exec-6] INFO  c.e.d.service.impl.AuthServiceImpl - 用户登录成功: 23adfa126662
2025-04-19 16:53:01.277 [http-nio-8080-exec-6] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-19 16:53:01.281 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 16:53:01.281 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.281 [http-nio-8080-exec-6] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1751176660 wrapping com.mysql.cj.jdbc.ConnectionImpl@********] will be managed by Spring
2025-04-19 16:53:01.281 [http-nio-8080-exec-6] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 16:53:01.282 [http-nio-8080-exec-6] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), LOGIN(String), 2025-04-19(String)
2025-04-19 16:53:01.283 [http-nio-8080-exec-6] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-19 16:53:01.283 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.284 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0] from current transaction
2025-04-19 16:53:01.284 [http-nio-8080-exec-6] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 16:53:01.284 [http-nio-8080-exec-6] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), LOGIN(String), 2025-04-18(String)
2025-04-19 16:53:01.285 [http-nio-8080-exec-6] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-19 16:53:01.285 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.285 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0] from current transaction
2025-04-19 16:53:01.285 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, create_time, update_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-04-19 16:53:01.286 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==> Parameters: 1(Long), LOGIN(String), 1(Integer), 2025-04-19(LocalDate), 2025-04-19T16:53:01.285(LocalDateTime), 2025-04-19T16:53:01.285(LocalDateTime), 127.0.0.1(String)
2025-04-19 16:53:01.287 [http-nio-8080-exec-6] DEBUG c.e.d.mapper.AccessLogMapper.insert - <==    Updates: 1
2025-04-19 16:53:01.287 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.287 [http-nio-8080-exec-6] INFO  c.e.d.s.impl.AccessLogServiceImpl - Created new access log for user: 1, type: LOGIN, count: 1
2025-04-19 16:53:01.287 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.287 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.287 [http-nio-8080-exec-6] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a802fc0]
2025-04-19 16:53:01.294 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 16:53:01.294 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=登录成功, data=TokenResponse(accessToken=Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdXRob3 (truncated)...]
2025-04-19 16:53:01.295 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 16:53:01.295 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 16:53:48.813 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/6
2025-04-19 16:53:48.813 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 16:53:48.815 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 16:53:48.825 [http-nio-8080-exec-9] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 16:53:48.827 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 16:53:48.827 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a4c6d50]
2025-04-19 16:53:48.827 [http-nio-8080-exec-9] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1918611108 wrapping com.mysql.cj.jdbc.ConnectionImpl@********] will be managed by Spring
2025-04-19 16:53:48.827 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 16:53:48.827 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 16:53:48.828 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 16:53:48.828 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a4c6d50]
2025-04-19 16:53:48.828 [http-nio-8080-exec-9] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 16:53:48.828 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a4c6d50]
2025-04-19 16:53:48.828 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a4c6d50]
2025-04-19 16:53:48.828 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a4c6d50]
2025-04-19 16:53:48.829 [http-nio-8080-exec-9] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-19 16:53:48.829 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users/6] with attributes [authenticated]
2025-04-19 16:53:48.829 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users/6
2025-04-19 16:53:48.829 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users/6", parameters={}
2025-04-19 16:53:48.830 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 16:53:48.837 [http-nio-8080-exec-9] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 16:53:48.848 [http-nio-8080-exec-9] DEBUG c.e.demo13.controller.UserController - 获取用户信息: 6
2025-04-19 16:53:48.848 [http-nio-8080-exec-9] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 6
2025-04-19 16:53:48.849 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 16:53:48.849 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c1c59af]
2025-04-19 16:53:48.849 [http-nio-8080-exec-9] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@********] will be managed by Spring
2025-04-19 16:53:48.849 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.UserMapper.findById - ==>  Preparing: SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE id = ?
2025-04-19 16:53:48.850 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.UserMapper.findById - ==> Parameters: 6(Long)
2025-04-19 16:53:48.851 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.UserMapper.findById - <==      Total: 1
2025-04-19 16:53:48.851 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c1c59af]
2025-04-19 16:53:48.851 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c1c59af]
2025-04-19 16:53:48.851 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c1c59af]
2025-04-19 16:53:48.851 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c1c59af]
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@728571071 wrapping com.mysql.cj.jdbc.ConnectionImpl@********] will be managed by Spring
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 6(Long), GET_USER_INFO(String), 2025-04-19(String)
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-19 16:53:48.854 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.855 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5] from current transaction
2025-04-19 16:53:48.855 [http-nio-8080-exec-9] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 16:53:48.856 [http-nio-8080-exec-9] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 6(Long), GET_USER_INFO(String), 2025-04-18(String)
2025-04-19 16:53:48.856 [http-nio-8080-exec-9] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-19 16:53:48.856 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.857 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5] from current transaction
2025-04-19 16:53:48.857 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, create_time, update_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-04-19 16:53:48.857 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==> Parameters: 6(Long), GET_USER_INFO(String), 1(Integer), 2025-04-19(LocalDate), 2025-04-19T16:53:48.856(LocalDateTime), 2025-04-19T16:53:48.857(LocalDateTime), 127.0.0.1(String)
2025-04-19 16:53:48.857 [http-nio-8080-exec-9] DEBUG c.e.d.mapper.AccessLogMapper.insert - <==    Updates: 1
2025-04-19 16:53:48.858 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.858 [http-nio-8080-exec-9] INFO  c.e.d.s.impl.AccessLogServiceImpl - Created new access log for user: 6, type: GET_USER_INFO, count: 1
2025-04-19 16:53:48.858 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.858 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.858 [http-nio-8080-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4aa2d3c5]
2025-04-19 16:53:48.863 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 16:53:48.863 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=获取成功, data=User(id=6, username=23Adfa12666223663, email=6677222aaa79@gmail. (truncated)...]
2025-04-19 16:53:48.865 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 16:53:48.865 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 17:08:48.675 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@527d48db]]
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@527d48db]
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@527d48db]
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 17:08:48.676 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 17:08:48.973 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-19 17:08:48.979 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-19 17:08:53.538 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 17:08:53.540 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 20256 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 17:08:53.540 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 17:08:53.540 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 17:08:54.260 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 17:08:54.261 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 17:08:54.287 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-04-19 17:08:54.359 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 17:08:54.359 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 17:08:54.359 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 17:08:54.359 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 17:08:54.360 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 17:08:54.360 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 17:08:54.360 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 17:08:54.360 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 17:08:54.361 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 17:08:54.721 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 17:08:54.725 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-19 17:08:54.726 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-19 17:08:54.726 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-19 17:08:54.807 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-19 17:08:54.807 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1235 ms
2025-04-19 17:08:54.982 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 17:08:54.993 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 17:08:55.004 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 17:08:55.012 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 17:08:55.102 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 17:08:55.412 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 17:08:55.536 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 17:08:55.538 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.544 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 17:08:55.544 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.545 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 17:08:55.545 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.545 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 17:08:55.545 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.545 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 17:08:55.546 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.546 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 17:08:55.546 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.546 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 17:08:55.546 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 17:08:55.611 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 17:08:55.612 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 17:08:55.616 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@25699aa7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a47730c, org.springframework.security.web.context.SecurityContextPersistenceFilter@282c4da0, org.springframework.security.web.header.HeaderWriterFilter@26fadd98, org.springframework.security.web.authentication.logout.LogoutFilter@2776fd8f, com.example.pure.filter.JwtFilter@5b02a984, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18cf5c52, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5aea8994, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15369d73, org.springframework.security.web.session.SessionManagementFilter@120df990, org.springframework.security.web.access.ExceptionTranslationFilter@722531ab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@111a7973]
2025-04-19 17:08:55.617 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 17:08:55.618 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 17:08:55.739 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 17:08:55.753 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 17:08:55.794 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-19 17:08:55.800 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 17:08:56.211 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-19 17:08:56.294 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-19 17:08:56.313 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-19 17:08:56.313 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-19 17:08:56.313 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-19 17:08:56.313 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-19 17:08:56.314 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-19 17:08:56.315 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-19 17:08:56.315 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5817f1ca, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b395581, org.springframework.security.web.context.SecurityContextPersistenceFilter@7772d266, org.springframework.security.web.header.HeaderWriterFilter@733f1395, org.springframework.web.filter.CorsFilter@726a8729, org.springframework.security.web.authentication.logout.LogoutFilter@3e38c472, com.example.pure.filter.JwtFilter@5b02a984, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a21c74, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@b8a144e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1a2724d3, org.springframework.security.web.session.SessionManagementFilter@21dea711, org.springframework.security.web.access.ExceptionTranslationFilter@2edb2f8b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ba060af]
2025-04-19 17:08:56.343 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3bcd05cb, started on Sat Apr 19 17:08:53 CST 2025
2025-04-19 17:08:56.352 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-19 17:08:56.352 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-19 17:08:56.353 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-19 17:08:56.353 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-19 17:08:56.353 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-19 17:08:56.353 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-19 17:08:56.355 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-19 17:08:56.355 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-19 17:08:56.355 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-19 17:08:56.356 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-19 17:08:56.356 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-19 17:08:56.356 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-19 17:08:56.357 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-19 17:08:56.357 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-19 17:08:56.388 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-19 17:08:56.413 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-19 17:08:56.601 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-19 17:08:56.614 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-19 17:08:56.616 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 17:08:56.616 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 17:08:56.617 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-19 17:08:56.617 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 17:08:56.617 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 17:08:56.617 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]]
2025-04-19 17:08:56.617 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-19 17:08:56.617 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 17:08:56.617 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 17:08:56.733 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.538 seconds (JVM running for 4.146)
2025-04-19 17:09:00.378 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-19 17:09:00.378 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-19 17:09:00.379 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-19 17:09:00.379 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-19 17:09:00.379 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-19 17:09:00.380 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2bff4dc5
2025-04-19 17:09:00.380 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@58568e8a
2025-04-19 17:09:00.380 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-19 17:09:00.380 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-19 17:09:00.389 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/6
2025-04-19 17:09:00.391 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 17:09:00.397 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 17:09:00.858 [http-nio-8080-exec-2] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 17:09:00.910 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 17:09:00.913 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3dd87d]
2025-04-19 17:09:00.918 [http-nio-8080-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1406868322 wrapping com.mysql.cj.jdbc.ConnectionImpl@30c7235c] will be managed by Spring
2025-04-19 17:09:00.919 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 17:09:00.934 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 17:09:00.952 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 17:09:00.953 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3dd87d]
2025-04-19 17:09:00.953 [http-nio-8080-exec-2] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 17:09:00.954 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3dd87d]
2025-04-19 17:09:00.954 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3dd87d]
2025-04-19 17:09:00.954 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3dd87d]
2025-04-19 17:09:00.955 [http-nio-8080-exec-2] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-19 17:09:00.959 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users/6] with attributes [authenticated]
2025-04-19 17:09:00.960 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users/6
2025-04-19 17:09:00.961 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users/6", parameters={}
2025-04-19 17:09:00.962 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 17:09:00.977 [http-nio-8080-exec-2] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 17:09:01.017 [http-nio-8080-exec-2] DEBUG c.e.demo13.controller.UserController - 获取用户信息: 6
2025-04-19 17:09:01.030 [http-nio-8080-exec-2] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 6
2025-04-19 17:09:01.037 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 17:09:01.037 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57134bac]
2025-04-19 17:09:01.037 [http-nio-8080-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1695534988 wrapping com.mysql.cj.jdbc.ConnectionImpl@30c7235c] will be managed by Spring
2025-04-19 17:09:01.037 [http-nio-8080-exec-2] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 17:09:01.037 [http-nio-8080-exec-2] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 6(Long), GET_USER_INFO(String), 2025-04-19(String)
2025-04-19 17:09:01.040 [http-nio-8080-exec-2] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 1
2025-04-19 17:09:01.040 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57134bac]
2025-04-19 17:09:01.041 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57134bac]
2025-04-19 17:09:01.041 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57134bac]
2025-04-19 17:09:01.041 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@57134bac]
2025-04-19 17:09:01.068 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 17:09:01.069 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=获取成功, data=User(id=6, username=23Adfa12666223663, email=6677222aaa79@gmail. (truncated)...]
2025-04-19 17:09:01.080 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 17:09:01.081 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 17:09:56.339 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-19 17:39:56.350 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 17:45:13.534 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 17:45:13.856 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-19 17:45:13.872 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-19 18:50:28.328 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 18:50:28.340 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 1816 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 18:50:28.340 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 18:50:28.340 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 18:50:29.452 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 18:50:29.454 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 18:50:29.481 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-04-19 18:50:29.557 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 18:50:29.557 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 18:50:29.557 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 18:50:29.558 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 18:50:29.559 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 18:50:29.559 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 18:50:29.559 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 18:50:29.559 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 18:50:29.560 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 18:50:29.932 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 18:50:29.936 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-19 18:50:29.937 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-19 18:50:29.937 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-19 18:50:30.017 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-19 18:50:30.017 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1520 ms
2025-04-19 18:50:30.197 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 18:50:30.208 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 18:50:30.219 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 18:50:30.227 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 18:50:30.321 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 18:50:30.618 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 18:50:30.737 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 18:50:30.739 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.745 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 18:50:30.746 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 18:50:30.812 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 18:50:30.814 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 18:50:30.817 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@21d9cd04, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@799f354a, org.springframework.security.web.context.SecurityContextPersistenceFilter@5e99e2cb, org.springframework.security.web.header.HeaderWriterFilter@282c4da0, org.springframework.security.web.authentication.logout.LogoutFilter@164d01ba, com.example.pure.filter.JwtFilter@67acfde9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f559c74, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@720f56e2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33bdd01, org.springframework.security.web.session.SessionManagementFilter@409732fb, org.springframework.security.web.access.ExceptionTranslationFilter@120df990, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@693f2213]
2025-04-19 18:50:30.818 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 18:50:30.818 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 18:50:30.939 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 18:50:30.954 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 18:50:30.999 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-19 18:50:31.006 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 18:50:31.432 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-19 18:50:31.514 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-19 18:50:31.532 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-19 18:50:31.533 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-19 18:50:31.534 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@74eec640, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3973b6d4, org.springframework.security.web.context.SecurityContextPersistenceFilter@6b832551, org.springframework.security.web.header.HeaderWriterFilter@66020d69, org.springframework.web.filter.CorsFilter@18dd7767, org.springframework.security.web.authentication.logout.LogoutFilter@2b44605c, com.example.pure.filter.JwtFilter@67acfde9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@150d6eaf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63124022, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@9205c0a, org.springframework.security.web.session.SessionManagementFilter@3751acd7, org.springframework.security.web.access.ExceptionTranslationFilter@554566a8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5b4954b2]
2025-04-19 18:50:31.561 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@17503f6b, started on Sat Apr 19 18:50:28 CST 2025
2025-04-19 18:50:31.569 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-19 18:50:31.570 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-19 18:50:31.570 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-19 18:50:31.570 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-19 18:50:31.570 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-19 18:50:31.570 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-19 18:50:31.572 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-19 18:50:31.572 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-19 18:50:31.573 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-19 18:50:31.573 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-19 18:50:31.573 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-19 18:50:31.574 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-19 18:50:31.574 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-19 18:50:31.574 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-19 18:50:31.604 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-19 18:50:31.626 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-19 18:50:31.798 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-19 18:50:31.810 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-19 18:50:31.812 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 18:50:31.812 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 18:50:31.812 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-19 18:50:31.812 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35329a05]
2025-04-19 18:50:31.812 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35329a05]
2025-04-19 18:50:31.812 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35329a05]]
2025-04-19 18:50:31.812 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-19 18:50:31.812 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 18:50:31.812 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 18:50:31.914 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 4.161 seconds (JVM running for 4.839)
2025-04-19 18:51:00.829 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-19 18:51:00.830 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-19 18:51:00.830 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-19 18:51:00.830 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-19 18:51:00.830 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-19 18:51:00.831 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1e19d577
2025-04-19 18:51:00.831 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@363b6f04
2025-04-19 18:51:00.831 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-19 18:51:00.831 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-19 18:51:00.841 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/6
2025-04-19 18:51:00.843 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 18:51:00.851 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 18:51:01.319 [http-nio-8080-exec-1] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 18:51:01.332 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 18:51:01.334 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b]
2025-04-19 18:51:01.339 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@53c917fc] will be managed by Spring
2025-04-19 18:51:01.340 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-19 18:51:01.358 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.findByUsername - ==> Parameters: 23adfa126662(String)
2025-04-19 18:51:01.375 [http-nio-8080-exec-1] DEBUG c.e.d.m.UserMapper.findByUsername - <==      Total: 1
2025-04-19 18:51:01.376 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b]
2025-04-19 18:51:01.378 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b] from current transaction
2025-04-19 18:51:01.378 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 18:51:01.378 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 18:51:01.381 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 18:51:01.382 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b]
2025-04-19 18:51:01.382 [http-nio-8080-exec-1] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 18:51:01.382 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b]
2025-04-19 18:51:01.383 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b]
2025-04-19 18:51:01.383 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25459e1b]
2025-04-19 18:51:01.424 [http-nio-8080-exec-1] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-19 18:51:01.428 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users/6] with attributes [authenticated]
2025-04-19 18:51:01.428 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users/6
2025-04-19 18:51:01.430 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users/6", parameters={}
2025-04-19 18:51:01.431 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 18:51:01.446 [http-nio-8080-exec-1] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 18:51:01.491 [http-nio-8080-exec-1] DEBUG c.e.demo13.controller.UserController - 获取用户信息: 6
2025-04-19 18:51:01.504 [http-nio-8080-exec-1] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 6
2025-04-19 18:51:01.506 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 18:51:01.506 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@b2707ac]
2025-04-19 18:51:01.506 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@371314788 wrapping com.mysql.cj.jdbc.ConnectionImpl@53c917fc] will be managed by Spring
2025-04-19 18:51:01.506 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.findById - ==>  Preparing: SELECT id, username, email, nickname, avatar, gender, create_time, last_login_time, score FROM user WHERE id = ?
2025-04-19 18:51:01.506 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.findById - ==> Parameters: 6(Long)
2025-04-19 18:51:01.508 [http-nio-8080-exec-1] DEBUG c.e.d.mapper.UserMapper.findById - <==      Total: 1
2025-04-19 18:51:01.508 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@b2707ac]
2025-04-19 18:51:01.508 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@b2707ac]
2025-04-19 18:51:01.508 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@b2707ac]
2025-04-19 18:51:01.508 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@b2707ac]
2025-04-19 18:51:01.519 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 18:51:01.519 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b0210f]
2025-04-19 18:51:01.519 [http-nio-8080-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@758246427 wrapping com.mysql.cj.jdbc.ConnectionImpl@53c917fc] will be managed by Spring
2025-04-19 18:51:01.519 [http-nio-8080-exec-1] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 18:51:01.519 [http-nio-8080-exec-1] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 6(Long), GET_USER_INFO(String), 2025-04-19(String)
2025-04-19 18:51:01.521 [http-nio-8080-exec-1] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 1
2025-04-19 18:51:01.521 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b0210f]
2025-04-19 18:51:01.521 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b0210f]
2025-04-19 18:51:01.521 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b0210f]
2025-04-19 18:51:01.521 [http-nio-8080-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b0210f]
2025-04-19 18:51:01.530 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 18:51:01.531 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=获取成功, data=UserDTO(id=6, username=23Adfa12666223663, email=6677222aaa79@gma (truncated)...]
2025-04-19 18:51:01.540 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 18:51:01.541 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 18:51:31.554 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-19 18:52:10.956 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users
2025-04-19 18:52:10.957 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 18:52:10.957 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getAllUsers()
2025-04-19 18:52:10.966 [http-nio-8080-exec-3] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 18:52:10.987 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 18:52:10.987 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e1254a6]
2025-04-19 18:52:10.987 [http-nio-8080-exec-3] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@295587382 wrapping com.mysql.cj.jdbc.ConnectionImpl@53c917fc] will be managed by Spring
2025-04-19 18:52:10.987 [http-nio-8080-exec-3] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 18:52:10.987 [http-nio-8080-exec-3] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 18:52:10.988 [http-nio-8080-exec-3] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 18:52:10.988 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e1254a6]
2025-04-19 18:52:10.988 [http-nio-8080-exec-3] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 18:52:10.989 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e1254a6]
2025-04-19 18:52:10.989 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e1254a6]
2025-04-19 18:52:10.989 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4e1254a6]
2025-04-19 18:52:10.989 [http-nio-8080-exec-3] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-19 18:52:10.989 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users] with attributes [authenticated]
2025-04-19 18:52:10.989 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users
2025-04-19 18:52:10.990 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users", parameters={}
2025-04-19 18:52:10.990 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getAllUsers()
2025-04-19 18:52:10.991 [http-nio-8080-exec-3] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers(); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 18:52:10.999 [http-nio-8080-exec-3] DEBUG c.e.demo13.controller.UserController - 获取所有用户
2025-04-19 18:52:10.999 [http-nio-8080-exec-3] DEBUG c.e.d.service.impl.UserServiceImpl - 查找所有用户
2025-04-19 18:52:10.999 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 18:52:10.999 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5070cff4]
2025-04-19 18:52:11.000 [http-nio-8080-exec-3] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1396738235 wrapping com.mysql.cj.jdbc.ConnectionImpl@53c917fc] will be managed by Spring
2025-04-19 18:52:11.000 [http-nio-8080-exec-3] DEBUG c.e.demo13.mapper.UserMapper.findAll - ==>  Preparing: SELECT id, username, email, nickname, avatar, gender, create_time, last_login_time, score FROM user
2025-04-19 18:52:11.000 [http-nio-8080-exec-3] DEBUG c.e.demo13.mapper.UserMapper.findAll - ==> Parameters:
2025-04-19 18:52:11.002 [http-nio-8080-exec-3] DEBUG c.e.demo13.mapper.UserMapper.findAll - <==      Total: 11
2025-04-19 18:52:11.003 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5070cff4]
2025-04-19 18:52:11.003 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5070cff4]
2025-04-19 18:52:11.003 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5070cff4]
2025-04-19 18:52:11.003 [http-nio-8080-exec-3] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5070cff4]
2025-04-19 18:52:11.003 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 18:52:11.004 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=获取成功, data=[UserDTO(id=1, username=23adfa126662, email=<EMAIL>, nic (truncated)...]
2025-04-19 18:52:11.007 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 18:52:11.007 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 19:21:31.560 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-04-19 19:51:31.564 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-04-19 20:21:31.580 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-04-19 20:51:31.582 [MessageBroker-3] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-04-19 21:21:31.588 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35329a05]]
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35329a05]
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35329a05]
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-19 21:34:21.532 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:34:21.533 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:34:21.846 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-19 21:34:21.853 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-19 21:34:26.201 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 21:34:26.202 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 21204 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 21:34:26.203 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 21:34:26.203 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 21:34:26.993 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 21:34:26.995 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 21:34:27.020 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-04-19 21:34:27.096 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 21:34:27.096 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 21:34:27.097 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 21:34:27.097 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 21:34:27.098 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 21:34:27.099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 21:34:27.099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 21:34:27.099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 21:34:27.099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 21:34:27.505 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 21:34:27.509 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-19 21:34:27.510 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-19 21:34:27.510 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-19 21:34:27.594 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-19 21:34:27.594 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1343 ms
2025-04-19 21:34:27.778 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 21:34:27.790 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 21:34:27.803 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 21:34:27.812 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 21:34:27.906 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 21:34:28.208 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 21:34:28.327 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 21:34:28.329 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.336 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 21:34:28.337 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.337 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 21:34:28.337 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.337 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 21:34:28.338 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:28.411 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 21:34:28.412 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 21:34:28.416 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@25699aa7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a47730c, org.springframework.security.web.context.SecurityContextPersistenceFilter@282c4da0, org.springframework.security.web.header.HeaderWriterFilter@26fadd98, org.springframework.security.web.authentication.logout.LogoutFilter@2776fd8f, com.example.pure.filter.JwtFilter@5b02a984, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18cf5c52, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5aea8994, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15369d73, org.springframework.security.web.session.SessionManagementFilter@120df990, org.springframework.security.web.access.ExceptionTranslationFilter@722531ab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@111a7973]
2025-04-19 21:34:28.418 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 21:34:28.418 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 21:34:28.562 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 21:34:28.576 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 21:34:28.621 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 48 mappings in 'requestMappingHandlerMapping'
2025-04-19 21:34:28.629 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 21:34:29.070 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-19 21:34:29.152 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-19 21:34:29.170 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-19 21:34:29.171 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-19 21:34:29.172 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-19 21:34:29.172 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5817f1ca, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b395581, org.springframework.security.web.context.SecurityContextPersistenceFilter@7772d266, org.springframework.security.web.header.HeaderWriterFilter@733f1395, org.springframework.web.filter.CorsFilter@726a8729, org.springframework.security.web.authentication.logout.LogoutFilter@3e38c472, com.example.pure.filter.JwtFilter@5b02a984, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a21c74, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@b8a144e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1a2724d3, org.springframework.security.web.session.SessionManagementFilter@21dea711, org.springframework.security.web.access.ExceptionTranslationFilter@2edb2f8b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ba060af]
2025-04-19 21:34:29.199 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3bcd05cb, started on Sat Apr 19 21:34:26 CST 2025
2025-04-19 21:34:29.208 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-19 21:34:29.209 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-19 21:34:29.209 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-19 21:34:29.209 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-19 21:34:29.209 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-19 21:34:29.209 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-19 21:34:29.211 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-19 21:34:29.211 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-19 21:34:29.211 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-19 21:34:29.211 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-04-19 21:34:29.212 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-19 21:34:29.212 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-19 21:34:29.212 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-19 21:34:29.212 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-19 21:34:29.244 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-19 21:34:29.266 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-19 21:34:29.444 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-19 21:34:29.457 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-19 21:34:29.458 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 21:34:29.458 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 21:34:29.458 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-19 21:34:29.458 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 21:34:29.458 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 21:34:29.459 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]]
2025-04-19 21:34:29.459 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-19 21:34:29.459 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:34:29.459 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:34:29.468 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.639 seconds (JVM running for 4.321)
2025-04-19 21:34:31.716 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-19 21:34:31.716 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-19 21:34:31.716 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-19 21:34:31.716 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-19 21:34:31.716 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-19 21:34:31.717 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@6eb33c9
2025-04-19 21:34:31.717 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@40de3e6a
2025-04-19 21:34:31.717 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-19 21:34:31.717 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-19 21:34:31.725 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/17
2025-04-19 21:34:31.727 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 21:34:31.734 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 21:34:32.183 [http-nio-8080-exec-2] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 21:34:32.196 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 21:34:32.198 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306]
2025-04-19 21:34:32.203 [http-nio-8080-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@3fca12e3] will be managed by Spring
2025-04-19 21:34:32.204 [http-nio-8080-exec-2] DEBUG c.e.d.m.UserMapper.findByUsername - ==>  Preparing: /* DEBUG findByUsername */ SELECT id, username, password, email, nickname, avatar, gender, enabled, account_non_expired, account_non_locked, credentials_non_expired, create_time, update_time, last_login_time, refresh_token FROM user WHERE username = ?
2025-04-19 21:34:32.219 [http-nio-8080-exec-2] DEBUG c.e.d.m.UserMapper.findByUsername - ==> Parameters: 23adfa126662(String)
2025-04-19 21:34:32.236 [http-nio-8080-exec-2] DEBUG c.e.d.m.UserMapper.findByUsername - <==      Total: 1
2025-04-19 21:34:32.237 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306]
2025-04-19 21:34:32.238 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306] from current transaction
2025-04-19 21:34:32.239 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 21:34:32.239 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 21:34:32.241 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 21:34:32.241 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306]
2025-04-19 21:34:32.242 [http-nio-8080-exec-2] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 21:34:32.242 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306]
2025-04-19 21:34:32.242 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306]
2025-04-19 21:34:32.242 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1db5306]
2025-04-19 21:34:32.282 [http-nio-8080-exec-2] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-19 21:34:32.286 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users/17] with attributes [authenticated]
2025-04-19 21:34:32.287 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users/17
2025-04-19 21:34:32.288 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users/17", parameters={}
2025-04-19 21:34:32.289 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUser(Long, HttpServletRequest)
2025-04-19 21:34:32.304 [http-nio-8080-exec-2] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:34:32.347 [http-nio-8080-exec-2] DEBUG c.e.demo13.controller.UserController - 获取用户信息: 17
2025-04-19 21:34:32.360 [http-nio-8080-exec-2] DEBUG c.e.d.service.impl.UserServiceImpl - 根据ID查找用户: 17
2025-04-19 21:34:32.361 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 21:34:32.362 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29eba209]
2025-04-19 21:34:32.362 [http-nio-8080-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@219656518 wrapping com.mysql.cj.jdbc.ConnectionImpl@3fca12e3] will be managed by Spring
2025-04-19 21:34:32.362 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.UserMapper.findById - ==>  Preparing: SELECT id, username, email, nickname, avatar, gender, create_time, last_login_time, score FROM user WHERE id = ?
2025-04-19 21:34:32.362 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.UserMapper.findById - ==> Parameters: 17(Long)
2025-04-19 21:34:32.363 [http-nio-8080-exec-2] DEBUG c.e.d.mapper.UserMapper.findById - <==      Total: 0
2025-04-19 21:34:32.363 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29eba209]
2025-04-19 21:34:32.363 [http-nio-8080-exec-2] ERROR c.e.d.service.impl.UserServiceImpl - ID:17用户不存在
2025-04-19 21:34:32.363 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29eba209]
2025-04-19 21:34:32.363 [http-nio-8080-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29eba209]
2025-04-19 21:34:32.365 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-04-19 21:34:32.365 [http-nio-8080-exec-2] WARN  c.e.d.e.GlobalExceptionHandler - 业务异常: 用户不存在
2025-04-19 21:34:32.371 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 21:34:32.372 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=600, message=用户不存在, data=null)]
2025-04-19 21:34:32.380 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [com.example.pure.exception.BusinessException: 用户不存在]
2025-04-19 21:34:32.380 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 400 BAD_REQUEST
2025-04-19 21:34:32.381 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 21:35:29.202 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35e689a0]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:56:52.052 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:56:52.350 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-19 21:56:52.357 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-19 21:56:56.387 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 21:56:56.389 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 20544 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 21:56:56.390 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 21:56:56.390 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 21:56:57.139 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 21:56:57.141 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 21:56:57.168 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-04-19 21:56:57.239 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 21:56:57.240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 21:56:57.240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 21:56:57.240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 21:56:57.241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 21:56:57.241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 21:56:57.241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 21:56:57.241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 21:56:57.242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 21:56:57.619 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 21:56:57.623 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-19 21:56:57.624 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-19 21:56:57.624 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-19 21:56:57.707 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-19 21:56:57.707 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1284 ms
2025-04-19 21:56:57.887 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 21:56:57.899 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 21:56:57.910 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 21:56:57.919 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 21:56:58.010 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 21:56:58.330 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 21:56:58.459 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)
2025-04-19 21:56:58.462 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 21:56:58.468 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.469 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 21:56:58.469 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.469 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 21:56:58.469 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.469 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 21:56:58.469 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 21:56:58.536 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 21:56:58.538 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 21:56:58.541 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@55c1ced9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@49cc9b2a, org.springframework.security.web.context.SecurityContextPersistenceFilter@71b97eeb, org.springframework.security.web.header.HeaderWriterFilter@5e99e2cb, org.springframework.security.web.authentication.logout.LogoutFilter@53feeac9, com.example.pure.filter.JwtFilter@16cf8438, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c62c3fd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49b89425, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@11826398, org.springframework.security.web.session.SessionManagementFilter@1c74d19, org.springframework.security.web.access.ExceptionTranslationFilter@409732fb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3ace6346]
2025-04-19 21:56:58.543 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 21:56:58.544 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 21:56:58.694 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 21:56:58.709 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 21:56:58.757 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 49 mappings in 'requestMappingHandlerMapping'
2025-04-19 21:56:58.764 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 21:56:59.189 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-19 21:56:59.271 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-19 21:56:59.290 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-19 21:56:59.291 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-19 21:56:59.292 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-19 21:56:59.293 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@74d22ddd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@283d3628, org.springframework.security.web.context.SecurityContextPersistenceFilter@7b3cbe6e, org.springframework.security.web.header.HeaderWriterFilter@611c3eae, org.springframework.web.filter.CorsFilter@5b3518e1, org.springframework.security.web.authentication.logout.LogoutFilter@1ab53860, com.example.pure.filter.JwtFilter@16cf8438, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@177ede17, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@17176b18, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5fd18419, org.springframework.security.web.session.SessionManagementFilter@13e00016, org.springframework.security.web.access.ExceptionTranslationFilter@6d6039df, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7d1f3fe9]
2025-04-19 21:56:59.322 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3bcd05cb, started on Sat Apr 19 21:56:56 CST 2025
2025-04-19 21:56:59.331 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-19 21:56:59.332 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-19 21:56:59.332 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-19 21:56:59.332 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-19 21:56:59.332 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-19 21:56:59.332 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-19 21:56:59.334 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-19 21:56:59.334 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-19 21:56:59.335 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-19 21:56:59.335 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-04-19 21:56:59.335 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-19 21:56:59.336 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-19 21:56:59.336 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-19 21:56:59.336 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-19 21:56:59.368 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-19 21:56:59.392 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-19 21:56:59.576 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-19 21:56:59.589 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-19 21:56:59.591 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 21:56:59.591 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 21:56:59.591 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-19 21:56:59.591 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@503a7990]
2025-04-19 21:56:59.591 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@503a7990]
2025-04-19 21:56:59.592 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@503a7990]]
2025-04-19 21:56:59.592 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-19 21:56:59.592 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:56:59.592 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 21:56:59.705 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.663 seconds (JVM running for 4.305)
2025-04-19 21:57:34.263 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-19 21:57:34.263 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-19 21:57:34.263 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-19 21:57:34.263 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-19 21:57:34.263 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-19 21:57:34.264 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2e540ca4
2025-04-19 21:57:34.264 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@65ab6d3c
2025-04-19 21:57:34.264 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-19 21:57:34.265 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-19 21:57:34.273 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /v3/api-docs
2025-04-19 21:57:34.274 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 21:57:34.281 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-04-19 21:57:34.282 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-19 21:57:34.286 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /v3/api-docs] with attributes [permitAll]
2025-04-19 21:57:34.286 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /v3/api-docs
2025-04-19 21:57:34.287 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/v3/api-docs", parameters={}
2025-04-19 21:57:34.288 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-04-19 21:57:34.696 [http-nio-8080-exec-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 395 ms
2025-04-19 21:57:34.712 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json]
2025-04-19 21:57:34.713 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing ["{"openapi":"3.0.1","info":{"title":"Spring Boot REST API","description":"Spring Boot REST API with J (truncated)..."]
2025-04-19 21:57:34.720 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 21:57:34.721 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 21:57:59.313 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-19 22:07:22.908 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@503a7990]]
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@503a7990]
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@503a7990]
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 22:07:22.909 [SpringApplicationShutdownHook] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 22:07:23.084 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-19 22:07:23.088 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-19 22:07:27.095 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-04-19 22:07:27.097 [main] INFO  com.example.pure.Demo13Application - Starting Demo13Application using Java 1.8.0_432 on DESKTOP-DQ33ANO with PID 21460 (C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main started by Hao in C:\Users\<USER>\IdeaProjects\demo13)
2025-04-19 22:07:27.098 [main] DEBUG com.example.pure.Demo13Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-04-19 22:07:27.098 [main] INFO  com.example.pure.Demo13Application - The following 1 profile is active: "dev"
2025-04-19 22:07:27.843 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-19 22:07:27.845 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-19 22:07:27.873 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-04-19 22:07:27.954 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\AccessLogMapper.class]
2025-04-19 22:07:27.954 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\RoleMapper.class]
2025-04-19 22:07:27.954 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\demo13\build\classes\java\main\com\example\demo13\mapper\UserMapper.class]
2025-04-19 22:07:27.955 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-04-19 22:07:27.956 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-04-19 22:07:27.956 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-04-19 22:07:27.956 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-04-19 22:07:27.956 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-04-19 22:07:27.956 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-04-19 22:07:28.364 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-04-19 22:07:28.368 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-19 22:07:28.369 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-19 22:07:28.370 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-04-19 22:07:28.455 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-19 22:07:28.455 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1317 ms
2025-04-19 22:07:28.652 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\AccessLogMapper.xml]'
2025-04-19 22:07:28.667 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\RoleMapper.xml]'
2025-04-19 22:07:28.684 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\demo13\build\resources\main\mapper\UserMapper.xml]'
2025-04-19 22:07:28.698 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-19 22:07:28.817 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-19 22:07:29.180 [main] DEBUG com.example.pure.filter.JwtFilter - Filter 'jwtFilter' configured for use
2025-04-19 22:07:29.309 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-04-19 22:07:29.311 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.318 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updateUser(java.lang.Long,com.example.pure.model.dto.request.user.UserUpdateDTO)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=isAuthenticated()) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=@securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)
2025-04-19 22:07:29.319 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.updatePassword(java.lang.Long,com.example.pure.model.dto.request.user.PasswordUpdateDTO)]] with attributes [[authorize: '@securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.320 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN') or @securityService.isCurrentUser(#id)) found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)
2025-04-19 22:07:29.320 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 22:07:29.389 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-04-19 22:07:29.391 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-04-19 22:07:29.397 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@1cde374, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6818fd48, org.springframework.security.web.context.SecurityContextPersistenceFilter@10618775, org.springframework.security.web.header.HeaderWriterFilter@1e92c3b6, org.springframework.security.web.authentication.logout.LogoutFilter@49b89425, com.example.pure.filter.JwtFilter@6d69a0d3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20a3e10c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e2a6991, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@9263c54, org.springframework.security.web.session.SessionManagementFilter@65f3e805, org.springframework.security.web.access.ExceptionTranslationFilter@66451058, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@46612bfc]
2025-04-19 22:07:29.398 [main] INFO  c.example.demo13.config.AsyncConfig - 创建文件操作异步任务线程池
2025-04-19 22:07:29.399 [main] INFO  c.example.demo13.config.AsyncConfig - 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-04-19 22:07:29.534 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-04-19 22:07:29.550 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-19 22:07:29.598 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 49 mappings in 'requestMappingHandlerMapping'
2025-04-19 22:07:29.605 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-04-19 22:07:30.023 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-04-19 22:07:30.110 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path '/actuator'
2025-04-19 22:07:30.131 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-04-19 22:07:30.131 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/auth/register']
2025-04-19 22:07:30.131 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-04-19 22:07:30.131 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-04-19 22:07:30.131 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/create']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/status/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/oauth/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-04-19 22:07:30.132 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/user/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN')] for Ant [pattern='/api/profile/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-04-19 22:07:30.133 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-04-19 22:07:30.134 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1a2724d3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7dd91ffc, org.springframework.security.web.context.SecurityContextPersistenceFilter@5fb8dc01, org.springframework.security.web.header.HeaderWriterFilter@a21c74, org.springframework.web.filter.CorsFilter@1c2dd89b, org.springframework.security.web.authentication.logout.LogoutFilter@6cdbe5ec, com.example.pure.filter.JwtFilter@6d69a0d3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3e38c472, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2ee92e7d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3ad847f8, org.springframework.security.web.session.SessionManagementFilter@b8a144e, org.springframework.security.web.access.ExceptionTranslationFilter@7772d266, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@726a8729]
2025-04-19 22:07:30.162 [main] TRACE o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3bcd05cb, started on Sat Apr 19 22:07:27 CST 2025
2025-04-19 22:07:30.171 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.Auth2Controller:

2025-04-19 22:07:30.171 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.AuthController:

2025-04-19 22:07:30.171 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.DownloadController:

2025-04-19 22:07:30.171 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.FileManagerController:

2025-04-19 22:07:30.172 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.ImageController:

2025-04-19 22:07:30.172 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRCodeController:

2025-04-19 22:07:30.174 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-04-19 22:07:30.174 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.UserController:

2025-04-19 22:07:30.174 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.VideoController:

2025-04-19 22:07:30.174 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	c.e.d.c.WebSocketTestController:
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-04-19 22:07:30.175 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.b.a.w.s.e.BasicErrorController:

2025-04-19 22:07:30.175 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.a.OpenApiWebMvcResource:

2025-04-19 22:07:30.175 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerWelcomeWebMvc:

2025-04-19 22:07:30.175 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler -
	o.s.w.u.SwaggerConfigResource:

2025-04-19 22:07:30.210 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-04-19 22:07:30.233 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-04-19 22:07:30.433 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-04-19 22:07:30.447 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-04-19 22:07:30.450 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-04-19 22:07:30.450 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-04-19 22:07:30.450 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-04-19 22:07:30.451 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de08775]
2025-04-19 22:07:30.451 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de08775]
2025-04-19 22:07:30.451 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1de08775]]
2025-04-19 22:07:30.451 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-04-19 22:07:30.451 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 22:07:30.451 [main] DEBUG o.s.m.s.ExecutorSubscribableChannel - brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-04-19 22:07:30.554 [main] INFO  com.example.pure.Demo13Application - Started Demo13Application in 3.814 seconds (JVM running for 4.454)
2025-04-19 22:07:42.968 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-19 22:07:42.968 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-19 22:07:42.968 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-04-19 22:07:42.968 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-04-19 22:07:42.968 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-04-19 22:07:42.969 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@786035ae
2025-04-19 22:07:42.969 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@672d4c3b
2025-04-19 22:07:42.970 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-04-19 22:07:42.970 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-19 22:07:42.978 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /v3/api-docs
2025-04-19 22:07:42.980 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 22:07:42.986 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-04-19 22:07:42.988 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-04-19 22:07:42.991 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /v3/api-docs] with attributes [permitAll]
2025-04-19 22:07:42.992 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /v3/api-docs
2025-04-19 22:07:42.993 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/v3/api-docs", parameters={}
2025-04-19 22:07:42.994 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to org.springdoc.webmvc.api.OpenApiWebMvcResource#openapiJson(HttpServletRequest, String, Locale)
2025-04-19 22:07:43.407 [http-nio-8080-exec-1] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 400 ms
2025-04-19 22:07:43.424 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json]
2025-04-19 22:07:43.425 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing ["{"openapi":"3.0.1","info":{"title":"Spring Boot REST API","description":"Spring Boot REST API with J (truncated)..."]
2025-04-19 22:07:43.432 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 22:07:43.433 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 22:08:30.152 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-04-19 22:09:47.865 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/users/username?username=23adfa126662&password=&enabled=&authorities=&credentialsNonExpired=&accountNonExpired=&accountNonLocked=
2025-04-19 22:09:47.865 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-04-19 22:09:47.866 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUserByUsername(HttpServletRequest, String, UserDetails)
2025-04-19 22:09:48.310 [http-nio-8080-exec-4] DEBUG c.e.d.s.CustomUserDetailsService - 正在加载用户信息: 23adfa126662
2025-04-19 22:09:48.327 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 22:09:48.330 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1824c7bb]
2025-04-19 22:09:48.334 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@536213f3] will be managed by Spring
2025-04-19 22:09:48.336 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==>  Preparing: SELECT r.* FROM role r JOIN user_role ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-04-19 22:09:48.350 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - ==> Parameters: 1(Long)
2025-04-19 22:09:48.366 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.RoleMapper.findByUserId - <==      Total: 1
2025-04-19 22:09:48.366 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1824c7bb]
2025-04-19 22:09:48.367 [http-nio-8080-exec-4] DEBUG c.e.d.s.CustomUserDetailsService - 用户 23adfa126662 的角色信息已加载, 角色数量: 1
2025-04-19 22:09:48.367 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1824c7bb]
2025-04-19 22:09:48.367 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1824c7bb]
2025-04-19 22:09:48.367 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1824c7bb]
2025-04-19 22:09:48.368 [http-nio-8080-exec-4] DEBUG com.example.pure.filter.JwtFilter - 用户 '23adfa126662' 认证成功
2025-04-19 22:09:48.368 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.i.FilterSecurityInterceptor - Authorized filter invocation [GET /api/users/username?username=23adfa126662&password=&enabled=&authorities=&credentialsNonExpired=&accountNonExpired=&accountNonLocked=] with attributes [authenticated]
2025-04-19 22:09:48.369 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/users/username?username=23adfa126662&password=&enabled=&authorities=&credentialsNonExpired=&accountNonExpired=&accountNonLocked=
2025-04-19 22:09:48.369 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/users/username?username=23adfa126662&password=&enabled=&authorities=&credentialsNonExpired=&accountNonExpired=&accountNonLocked=", parameters={masked}
2025-04-19 22:09:48.369 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.example.pure.controller.user.UserController#getUserByUsername(HttpServletRequest, String, UserDetails)
2025-04-19 22:09:48.375 [http-nio-8080-exec-4] DEBUG o.s.s.a.i.a.MethodSecurityInterceptor - Authorized ReflectiveMethodInvocation: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserByUsername(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.security.core.userdetails.UserDetails); target is of class [com.example.pure.controller.user.UserController] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-04-19 22:09:48.428 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 获取用户DTO, username: 23adfa126662
2025-04-19 22:09:48.428 [http-nio-8080-exec-4] DEBUG c.e.d.service.impl.UserServiceImpl - 根据用户名查找用户: 23adfa126662
2025-04-19 22:09:48.431 [http-nio-8080-exec-4] DEBUG c.e.demo13.controller.UserController - 获取用户信息：23adfa126662
2025-04-19 22:09:48.434 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-04-19 22:09:48.434 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.434 [http-nio-8080-exec-4] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2041993659 wrapping com.mysql.cj.jdbc.ConnectionImpl@536213f3] will be managed by Spring
2025-04-19 22:09:48.434 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 22:09:48.435 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), GET_USER_INFO(String), 2025-04-19(String)
2025-04-19 22:09:48.436 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-19 22:09:48.437 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.438 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6] from current transaction
2025-04-19 22:09:48.438 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-04-19 22:09:48.438 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - ==> Parameters: 1(Long), GET_USER_INFO(String), 2025-04-18(String)
2025-04-19 22:09:48.439 [http-nio-8080-exec-4] DEBUG c.e.d.m.A.findByUserIdAndTypeAndDate - <==      Total: 0
2025-04-19 22:09:48.439 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.440 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6] from current transaction
2025-04-19 22:09:48.440 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, create_time, update_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-04-19 22:09:48.441 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.AccessLogMapper.insert - ==> Parameters: 1(Long), GET_USER_INFO(String), 1(Integer), 2025-04-19(LocalDate), 2025-04-19T22:09:48.439(LocalDateTime), 2025-04-19T22:09:48.439(LocalDateTime), 127.0.0.1(String)
2025-04-19 22:09:48.443 [http-nio-8080-exec-4] DEBUG c.e.d.mapper.AccessLogMapper.insert - <==    Updates: 1
2025-04-19 22:09:48.443 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.443 [http-nio-8080-exec-4] INFO  c.e.d.s.impl.AccessLogServiceImpl - Created new access log for user: 1, type: GET_USER_INFO, count: 1
2025-04-19 22:09:48.443 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.443 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.443 [http-nio-8080-exec-4] DEBUG org.mybatis.spring.SqlSessionUtils - Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7dad9df6]
2025-04-19 22:09:48.447 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-04-19 22:09:48.447 [http-nio-8080-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(code=200, message=查询用户名成功, data=UserDTO(id=1, username=23adfa126662, email=<EMAIL>, n (truncated)...]
2025-04-19 22:09:48.450 [http-nio-8080-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-04-19 22:09:48.450 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-04-19 22:38:30.164 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-04-19 23:08:30.172 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-04-19 23:38:30.185 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
