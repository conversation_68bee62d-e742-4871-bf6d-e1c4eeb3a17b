# AI大模型API转发系统 - API参考文档

## 📋 概述

本文档详细描述了AI大模型API转发系统的所有REST API接口，包括**OpenAI兼容API**和**配置管理API**。系统完全兼容OpenAI API格式，支持所有使用OpenAI兼容API的AI工具和软件。

## 🔐 认证方式

### OpenAI兼容API认证
使用OpenAI格式的API Key进行认证：

```http
Authorization: Bearer sk-{userId}_{keyId}_{hash}
```

### 配置管理API认证
使用JWT令牌进行认证：

```http
Authorization: Bearer {jwt_token}
```

## 📊 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "详细错误信息",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔌 OpenAI兼容API

### 概述
这些API完全兼容OpenAI格式，可以直接被Cursor、VS Code、LangChain、 Lobe Chat, NextChat、OpenCat等工具使用。

### 1. 聊天完成接口

#### 流式聊天完成
```http
POST /v1/chat/completions
Authorization: Bearer sk-{userId}_{keyId}_{hash}
Content-Type: application/json
```

**请求参数 (标准OpenAI格式):**
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": true,
  "temperature": 0.7,
  "max_tokens": 2048,
  "top_p": 1.0,
  "frequency_penalty": 0,
  "presence_penalty": 0,
  "stop": null
}
```

**流式响应 (SSE格式):**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"usage":{"prompt_tokens":9,"completion_tokens":12,"total_tokens":21}}

data: [DONE]
```

#### 非流式聊天完成
```http
POST /v1/chat/completions
Authorization: Bearer sk-{userId}_{keyId}_{hash}
Content-Type: application/json
```

**请求参数:**
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 2048
}
```

**响应示例:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 20,
    "total_tokens": 29
  }
}
```

### 2. 模型列表接口

```http
GET /v1/models
Authorization: Bearer sk-{userId}_{keyId}_{hash}
```

**响应示例:**
```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-3.5-turbo",
      "object": "model",
      "created": 1677610602,
      "owned_by": "openai",
      "permission": [],
      "root": "gpt-3.5-turbo",
      "parent": null
    },
    {
      "id": "gpt-4",
      "object": "model",
      "created": 1687882411,
      "owned_by": "openai",
      "permission": [],
      "root": "gpt-4",
      "parent": null
    },
    {
      "id": "claude-3-sonnet",
      "object": "model",
      "created": 1677610602,
      "owned_by": "anthropic",
      "permission": [],
      "root": "claude-3-sonnet",
      "parent": null
    }
  ]
}
```

### 3. 文本嵌入接口 (计划支持)

```http
POST /v1/embeddings
Authorization: Bearer sk-{userId}_{keyId}_{hash}
Content-Type: application/json
```

**请求参数:**
```json
{
  "model": "text-embedding-ada-002",
  "input": "The food was delicious and the waiter...",
  "encoding_format": "float"
}
```

## 🔧 配置管理API

### 概述
这些API用于系统配置管理，需要JWT认证。

### 1. 用户配置管理

#### 获取用户配置
```http
GET /api/ai/config/user
```

**响应参数:**
| 字段 | 类型 | 说明 |
|------|------|------|
| preferredModel | String | 偏好模型 |
| defaultTemperature | Decimal | 默认温度值 (0.0-2.0) |
| defaultMaxTokens | Integer | 默认最大token数 |
| defaultTopP | Decimal | 默认top_p值 (0.0-1.0) |
| streamEnabled | Boolean | 是否启用流式输出 |
| timeoutSeconds | Integer | 超时时间(秒) |
| systemPrompt | String | 系统提示词 |

#### 更新用户配置
```http
PUT /api/ai/config/user
Content-Type: application/json
```

**请求参数:**
```json
{
  "preferredModel": "gpt-4",
  "defaultTemperature": 0.8,
  "defaultMaxTokens": 2048,
  "defaultTopP": 0.9,
  "streamEnabled": true,
  "timeoutSeconds": 45,
  "systemPrompt": "你是一个专业的编程助手"
}
```

### 2. API密钥管理

#### 获取API密钥列表
```http
GET /api/ai/config/api-keys
```

**查询参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| provider | String | 否 | 提供商筛选 (OPENAI/ANTHROPIC/GOOGLE) |
| active | Boolean | 否 | 是否只显示活跃密钥 |

**响应示例:**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "provider": "OPENAI",
      "keyName": "主要密钥",
      "isActive": true,
      "priority": 1,
      "usageCount": 1250,
      "lastUsedAt": "2024-01-15T10:30:00Z",
      "healthStatus": "HEALTHY",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 添加API密钥
```http
POST /api/ai/config/api-keys
Content-Type: application/json
```

**请求参数:**
```json
{
  "provider": "OPENAI",
  "keyName": "备用密钥",
  "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "priority": 2,
  "isActive": true
}
```

**验证规则:**
- `provider`: 必填，枚举值 (OPENAI/ANTHROPIC/GOOGLE)
- `keyName`: 必填，长度1-100字符
- `apiKey`: 必填，符合对应提供商的格式要求
- `priority`: 可选，默认为1，数值越小优先级越高

#### 更新API密钥
```http
PUT /api/ai/config/api-keys/{keyId}
Content-Type: application/json
```

**请求参数:**
```json
{
  "keyName": "更新后的密钥名称",
  "priority": 3,
  "isActive": false
}
```

#### 删除API密钥
```http
DELETE /api/ai/config/api-keys/{keyId}
```

#### 测试API密钥
```http
POST /api/ai/config/api-keys/{keyId}/test
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "isValid": true,
    "responseTime": 1250,
    "model": "gpt-3.5-turbo",
    "testMessage": "API密钥有效，连接正常",
    "quota": {
      "remaining": 95.50,
      "total": 100.00,
      "unit": "USD"
    }
  }
}
```

### 3. 负载均衡配置

#### 获取负载均衡状态
```http
GET /api/ai/config/load-balance
```

**查询参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| provider | String | 否 | 提供商筛选 |

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "strategy": "LEAST_CONNECTIONS",
    "providers": {
      "OPENAI": {
        "totalKeys": 3,
        "healthyKeys": 2,
        "currentRequests": 15,
        "totalRequests": 12450,
        "errorRate": 0.02
      }
    },
    "keyStatus": [
      {
        "keyId": 1,
        "provider": "OPENAI",
        "isHealthy": true,
        "currentRequests": 5,
        "errorCount": 2,
        "lastUsed": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### 更新负载均衡策略
```http
PUT /api/ai/config/load-balance
Content-Type: application/json
```

**请求参数:**
```json
{
  "strategy": "WEIGHTED_ROUND_ROBIN",
  "healthCheckInterval": 60,
  "maxErrorThreshold": 5,
  "recoveryCheckInterval": 300
}
```

**策略说明:**
- `ROUND_ROBIN`: 轮询策略
- `LEAST_CONNECTIONS`: 最少连接策略
- `WEIGHTED_ROUND_ROBIN`: 加权轮询策略
- `RANDOM`: 随机策略

## 💬 聊天API

### 1. 流式聊天

#### 发起流式聊天
```http
POST /api/ai/chat/stream
Content-Type: application/json
Accept: text/event-stream
```

**请求参数:**
```json
{
  "sessionId": "session_123456789",
  "message": "你好，请介绍一下人工智能",
  "model": "gpt-3.5-turbo",
  "temperature": 0.7,
  "maxTokens": 2048,
  "topP": 1.0,
  "streamEnabled": true,
  "systemPrompt": "你是一个有用的AI助手"
}
```

**参数说明:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| sessionId | String | 是 | 会话ID |
| message | String | 是 | 用户消息内容 |
| model | String | 否 | 指定模型，不填使用用户默认配置 |
| temperature | Decimal | 否 | 温度值 (0.0-2.0) |
| maxTokens | Integer | 否 | 最大生成token数 |
| topP | Decimal | 否 | top_p值 (0.0-1.0) |
| streamEnabled | Boolean | 否 | 是否启用流式输出 |
| systemPrompt | String | 否 | 临时系统提示词 |

**SSE响应流:**
```
data: {"type":"start","sessionId":"session_123456789","model":"gpt-3.5-turbo"}

data: {"type":"content","content":"你好！"}

data: {"type":"content","content":"人工智能"}

data: {"type":"content","content":"（Artificial Intelligence，AI）"}

data: {"type":"end","usage":{"promptTokens":15,"completionTokens":200,"totalTokens":215},"finishReason":"stop"}
```

**事件类型说明:**
- `start`: 开始响应，包含会话和模型信息
- `content`: 内容片段
- `error`: 错误信息
- `end`: 响应结束，包含使用统计

### 2. 非流式聊天

#### 发起非流式聊天
```http
POST /api/ai/chat/completion
Content-Type: application/json
```

**请求参数:** (同流式聊天，但streamEnabled固定为false)

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "sessionId": "session_123456789",
    "model": "gpt-3.5-turbo",
    "content": "你好！人工智能（Artificial Intelligence，AI）是计算机科学的一个分支...",
    "usage": {
      "promptTokens": 15,
      "completionTokens": 200,
      "totalTokens": 215
    },
    "finishReason": "stop",
    "responseTime": 2150
  }
}
```

### 3. 会话管理

#### 创建新会话
```http
GET /api/ai/chat/session/new
```

**响应示例:**
```json
{
  "code": 200,
  "message": "会话创建成功",
  "data": "session_1705123456789"
}
```

#### 获取会话信息
```http
GET /api/ai/chat/session/{sessionId}
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "sessionId": "session_123456789",
    "userId": 1001,
    "model": "gpt-3.5-turbo",
    "provider": "OPENAI",
    "messageCount": 5,
    "createdAt": "2024-01-15T10:00:00Z",
    "expiresAt": "2024-01-15T22:00:00Z",
    "isActive": true
  }
}
```

#### 删除会话
```http
DELETE /api/ai/chat/session/{sessionId}
```

#### 获取用户会话列表
```http
GET /api/ai/chat/sessions
```

**查询参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认20 |
| active | Boolean | 否 | 是否只显示活跃会话 |

### 4. 模型管理

#### 获取可用模型列表
```http
GET /api/ai/chat/models
```

**查询参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| provider | String | 否 | 提供商筛选 |
| available | Boolean | 否 | 是否只显示可用模型 |

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "OPENAI": [
      {
        "id": "gpt-3.5-turbo",
        "name": "GPT-3.5 Turbo",
        "maxTokens": 4096,
        "inputCost": 0.0015,
        "outputCost": 0.002,
        "description": "快速、经济的对话模型",
        "capabilities": ["chat", "completion"],
        "isAvailable": true
      }
    ],
    "ANTHROPIC": [
      {
        "id": "claude-3-sonnet",
        "name": "Claude 3 Sonnet",
        "maxTokens": 200000,
        "inputCost": 0.003,
        "outputCost": 0.015,
        "description": "平衡性能和速度",
        "capabilities": ["chat", "analysis"],
        "isAvailable": true
      }
    ]
  }
}
```

### 5. 系统接口

#### 健康检查
```http
GET /api/ai/chat/health
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "status": "UP",
    "providers": {
      "OPENAI": "UP",
      "ANTHROPIC": "UP",
      "GOOGLE": "DOWN"
    },
    "activeConnections": 25,
    "totalRequests": 12450,
    "errorRate": 0.02,
    "averageResponseTime": 1850
  }
}
```

#### 获取使用统计
```http
GET /api/ai/chat/statistics
```

**查询参数:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| startDate | String | 否 | 开始日期 (YYYY-MM-DD) |
| endDate | String | 否 | 结束日期 (YYYY-MM-DD) |
| provider | String | 否 | 提供商筛选 |

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "totalRequests": 12450,
    "successfulRequests": 12200,
    "failedRequests": 250,
    "totalTokens": 2450000,
    "totalCost": 125.50,
    "averageResponseTime": 1850,
    "dailyStats": [
      {
        "date": "2024-01-15",
        "requests": 450,
        "tokens": 89000,
        "cost": 4.25
      }
    ]
  }
}
```

## ❌ 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权访问 | 检查JWT token是否有效 |
| 403 | 权限不足 | 确认用户权限或联系管理员 |
| 404 | 资源不存在 | 检查请求的资源ID是否正确 |
| 429 | 请求频率超限 | 降低请求频率或联系管理员提升限额 |
| 500 | 服务器内部错误 | 联系技术支持 |
| 502 | AI服务不可用 | 检查API Key状态或稍后重试 |
| 503 | 服务暂时不可用 | 系统维护中，请稍后重试 |

## 📝 使用示例

### JavaScript/TypeScript示例

```typescript
// 配置管理示例
class AiConfigClient {
  private baseUrl = 'https://api.example.com';
  private token = 'your-jwt-token';

  async getUserConfig(): Promise<UserConfig> {
    const response = await fetch(`${this.baseUrl}/api/ai/config/user`, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }

  async addApiKey(provider: string, keyName: string, apiKey: string): Promise<void> {
    await fetch(`${this.baseUrl}/api/ai/config/api-keys`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ provider, keyName, apiKey })
    });
  }
}

// 流式聊天示例
class AiChatClient {
  async streamChat(sessionId: string, message: string): Promise<EventSource> {
    const response = await fetch('/api/ai/chat/stream', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ sessionId, message })
    });

    const eventSource = new EventSource(response.url);
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      switch (data.type) {
        case 'start':
          console.log('开始响应:', data);
          break;
        case 'content':
          console.log('内容片段:', data.content);
          break;
        case 'end':
          console.log('响应结束:', data.usage);
          eventSource.close();
          break;
        case 'error':
          console.error('错误:', data.error);
          eventSource.close();
          break;
      }
    };

    return eventSource;
  }
}
```

### Python示例

```python
import requests
import json
from typing import Dict, Any

class AiApiClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def get_user_config(self) -> Dict[str, Any]:
        response = requests.get(
            f'{self.base_url}/api/ai/config/user',
            headers=self.headers
        )
        return response.json()
    
    def add_api_key(self, provider: str, key_name: str, api_key: str) -> None:
        data = {
            'provider': provider,
            'keyName': key_name,
            'apiKey': api_key
        }
        response = requests.post(
            f'{self.base_url}/api/ai/config/api-keys',
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
    
    def chat_completion(self, session_id: str, message: str, **kwargs) -> Dict[str, Any]:
        data = {
            'sessionId': session_id,
            'message': message,
            **kwargs
        }
        response = requests.post(
            f'{self.base_url}/api/ai/chat/completion',
            headers=self.headers,
            json=data
        )
        return response.json()

# 使用示例
client = AiApiClient('https://api.example.com', 'your-jwt-token')

# 获取用户配置
config = client.get_user_config()
print(f"用户偏好模型: {config['data']['preferredModel']}")

# 添加API密钥
client.add_api_key('OPENAI', '主要密钥', 'sk-xxxxxxxx')

# 发起聊天
response = client.chat_completion(
    session_id='session_123',
    message='你好',
    temperature=0.7
)
print(f"AI回复: {response['data']['content']}")
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024-01-15  
**技术支持**: <EMAIL>
