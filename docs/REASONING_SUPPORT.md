# OpenAI 兼容思考过程支持

本文档介绍如何使用我们的 OpenAI 兼容 API 来获取 AI 大模型的思考过程。

## 功能概述

我们的 API 支持从不同 AI 提供商（OpenAI、<PERSON>、Gemini）提取思考过程，并统一转换为 OpenAI o1 兼容格式，让客户端能够以标准方式获取 AI 的推理过程。

## 支持的模型

### OpenAI
- o1-preview
- o1-mini
- o1 (完整版)
- 其他包含 "o1" 或 "reasoning" 的模型
- **推理参数**: `reasoning_effort: "low" | "medium" | "high"`

### Anthropic Claude
- Claude 4 系列 (Opus 4, Sonnet 4)
- Claude 3.7 Sonnet
- 其他包含 "claude-4" 或 "claude-3.7" 的模型
- **推理参数**: `thinking: {type: "enabled", budget_tokens: 10000}`

### Google Gemini
- Gemini 2.5 系列 (Pro, Flash, Flash-Lite)
- 其他包含 "gemini-2.5" 或 "2.5" 的模型
- **推理参数**: `reasoning_effort: "medium"`

## API 使用方法

### 1. 非流式请求

```bash
curl -X POST "https://your-api.com/v1/chat/completions" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "o1-preview",
    "messages": [
      {
        "role": "user",
        "content": "解这个数学题：如果 x + 2y = 10 且 x - y = 1，求 x 和 y 的值"
      }
    ],
    "include_reasoning": true
  }'
```

**响应格式：**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "o1-preview",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "根据给定的方程组，我可以求解出 x = 4, y = 3。",
        "reasoning_content": "让我逐步解这个方程组：\n\n给定：\n- 方程1：x + 2y = 10\n- 方程2：x - y = 1\n\n从方程2可得：x = y + 1\n\n将此代入方程1：\n(y + 1) + 2y = 10\ny + 1 + 2y = 10\n3y + 1 = 10\n3y = 9\ny = 3\n\n将 y = 3 代入 x = y + 1：\nx = 3 + 1 = 4\n\n验证：\n- 方程1：4 + 2(3) = 4 + 6 = 10 ✓\n- 方程2：4 - 3 = 1 ✓"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 25,
    "completion_tokens": 50,
    "total_tokens": 75
  }
}
```

### 2. 流式请求

```bash
curl -X POST "https://your-api.com/v1/chat/completions" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4",
    "messages": [
      {
        "role": "user",
        "content": "写一个 Python 函数来计算斐波那契数列"
      }
    ],
    "include_reasoning": true,
    "stream": true
  }'
```

**流式响应格式：**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-4","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-4","choices":[{"index":0,"delta":{"reasoning_content":"我需要创建一个计算斐波那契数列的函数。让我考虑几种实现方式：\n\n1. 递归方法 - 简单但效率低\n2. 迭代方法 - 更高效\n3. 记忆化递归 - 平衡简洁性和效率\n\n我会选择迭代方法，因为它最高效且易于理解。"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-4","choices":[{"index":0,"delta":{"content":"我来为你写一个高效的斐波那契数列计算函数：\n\n```python\ndef fibonacci(n):\n    \"\"\"计算斐波那契数列的第n项\"\"\"\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    \n    a, b = 0, 1\n    for _ in range(2, n + 1):\n        a, b = b, a + b\n    \n    return b\n```"},"finish_reason":"stop"}]}

data: [DONE]
```

## 客户端兼容性

### Cursor
Cursor 可以直接使用我们的 API，只需在设置中：
1. 设置 API 端点为：`https://your-api.com/v1`
2. 使用支持推理的模型名称
3. 在请求中添加 `"include_reasoning": true`

### NextChat
NextChat 支持自定义 OpenAI 端点：
1. 在设置中修改 API 地址为：`https://your-api.com`
2. 选择支持推理的模型
3. 思考过程会自动显示在对话中

### 其他 OpenAI 兼容客户端
任何支持 OpenAI API 的客户端都可以使用，只需：
1. 修改 API 端点
2. 在请求中添加 `include_reasoning` 参数

## 配置说明

### 环境变量
```bash
# 启用推理过程功能
REASONING_ENABLED=true

# 推理过程最大长度（字符数）
REASONING_MAX_LENGTH=10000

# 推理过程缓存时间（秒）
REASONING_CACHE_TTL=300
```

### 应用配置
```yaml
reasoning:
  enabled: true
  max-length: 10000
  cache-ttl: 300
  providers:
    openai:
      models: ["o1-preview", "o1-mini"]
    anthropic:
      models: ["claude-4", "claude-3.7-sonnet"]
    google:
      models: ["gemini-2.5-pro", "gemini-2.5-flash"]
```

## 注意事项

1. **性能影响**：启用推理过程可能会增加响应时间和 token 消耗
2. **模型支持**：只有支持推理的模型才会返回思考过程
3. **格式统一**：不同提供商的思考过程会被转换为统一的 OpenAI 格式
4. **向后兼容**：不设置 `include_reasoning` 参数时，行为与标准 OpenAI API 完全一致

## 故障排除

### 常见问题

**Q: 为什么没有返回推理过程？**
A: 检查以下几点：
- 确保设置了 `"include_reasoning": true`
- 确认使用的是支持推理的模型
- 检查模型提供商是否支持思考过程

**Q: 推理过程格式不正确？**
A: 我们的 API 会自动转换不同提供商的格式，如果仍有问题，请检查客户端是否正确解析 `reasoning_content` 字段。

**Q: 流式响应中推理过程显示异常？**
A: 确保客户端正确处理 SSE 事件，推理过程会在常规内容之前发送。

## 技术实现说明

### 参数转换逻辑

我们的 API 会自动将统一的 `include_reasoning: true` 参数转换为各提供商的原生格式：

**OpenAI 转换**:
```json
// 输入
{"include_reasoning": true}

// 转换为
{"reasoning_effort": "medium"}
```

**Claude 转换**:
```json
// 输入
{"include_reasoning": true}

// 转换为
{
  "thinking": {
    "type": "enabled",
    "budget_tokens": 10000
  }
}
```

**Google 转换**:
```json
// 输入
{"include_reasoning": true}

// 转换为
{"reasoning_effort": "medium"}
```

### 响应格式统一

所有提供商的推理内容都会被转换为统一的 OpenAI 兼容格式：

```json
{
  "choices": [{
    "message": {
      "content": "最终回答",
      "reasoning_content": "AI的思考过程..."
    }
  }]
}
```

## 更新日志

- **v1.0.0**: 初始版本，支持 OpenAI、Claude、Gemini 的推理过程提取
- **v1.1.0**: 添加流式推理过程支持
- **v1.2.0**: 优化推理过程格式转换，提高兼容性
- **v1.3.0**: 修正所有提供商的推理参数格式，符合官方文档标准
