# AI大模型API转发系统 - 部署指南

## 📋 概述

本文档提供AI大模型API转发系统的完整部署指南，包括环境准备、配置说明、部署步骤、监控配置等。

## 🔧 环境要求

### 硬件要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM以上
- **存储**: 50GB SSD以上
- **网络**: 1Gbps

#### 生产环境配置
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **存储**: 100GB SSD以上
- **网络**: 1Gbps以上

### 软件要求

#### 基础环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+ / RHEL 8+)
- **Java**: OpenJDK 11+ 或 Oracle JDK 11+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **容器**: Docker 20.10+ (可选)

#### 依赖服务
- **负载均衡器**: Nginx 1.18+ (推荐)
- **监控**: Prometheus + Grafana (可选)
- **日志**: ELK Stack (可选)

## 🚀 快速部署

### 使用Docker Compose (推荐)

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/ai-proxy-system.git
cd ai-proxy-system
```

#### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**.env 文件示例:**
```bash
# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080

# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=ai_proxy_db
DB_USERNAME=ai_app
DB_PASSWORD=your_secure_password

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DATABASE=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_at_least_256_bits_long
JWT_EXPIRATION=604800
JWT_REFRESH_EXPIRATION=1209600

# AI加密配置
AI_ENCRYPTION_SECRET=your_ai_encryption_secret_key

# 邮件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# 监控配置
ACTUATOR_ENDPOINTS_ENABLED=true
MANAGEMENT_PORT=8081
```

#### 3. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

#### 4. 初始化数据库
```bash
# 等待MySQL启动完成
docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "SELECT 1"

# 执行数据库初始化脚本
docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} ai_proxy_db < /docker-entrypoint-initdb.d/schema.sql
docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} ai_proxy_db < /docker-entrypoint-initdb.d/init_data.sql
```

#### 5. 验证部署
```bash
# 健康检查
curl http://localhost:8080/actuator/health

# API测试
curl -X GET http://localhost:8080/api/ai/chat/health
```

### Docker Compose 配置文件

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${SERVER_PORT:-8080}:8080"
      - "${MANAGEMENT_PORT:-8081}:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-prod}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - AI_ENCRYPTION_SECRET=${AI_ENCRYPTION_SECRET}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - ai-proxy-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped
    networks:
      - ai-proxy-network

  redis:
    image: redis:6.0-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - ai-proxy-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - ai-proxy-network

volumes:
  mysql_data:
  redis_data:

networks:
  ai-proxy-network:
    driver: bridge
```

## 🔧 手动部署

### 1. 环境准备

#### 安装Java
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-11-jdk

# CentOS/RHEL
sudo yum install java-11-openjdk-devel

# 验证安装
java -version
```

#### 安装MySQL
```bash
# Ubuntu/Debian
sudo apt install mysql-server-8.0

# CentOS/RHEL
sudo yum install mysql-server

# 启动MySQL
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 安装Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis
```

### 2. 数据库配置

#### 创建数据库和用户
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE ai_proxy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER 'ai_app'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON ai_proxy_db.* TO 'ai_app'@'%';

-- 创建只读监控用户
CREATE USER 'ai_monitor'@'%' IDENTIFIED BY 'monitor_password';
GRANT SELECT ON ai_proxy_db.* TO 'ai_monitor'@'%';

FLUSH PRIVILEGES;
```

#### 执行数据库脚本
```bash
# 执行建表脚本
mysql -u ai_app -p ai_proxy_db < sql/schema.sql

# 执行初始化数据脚本
mysql -u ai_app -p ai_proxy_db < sql/init_data.sql
```

### 3. 应用配置

#### 创建应用目录
```bash
sudo mkdir -p /opt/ai-proxy
sudo chown $USER:$USER /opt/ai-proxy
cd /opt/ai-proxy
```

#### 配置文件
**application-prod.yml:**
```yaml
server:
  port: 8080
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain
    min-response-size: 1024

spring:
  profiles:
    active: prod
  
  datasource:
    url: **************************************************************************************************
    username: ${DB_USERNAME:ai_app}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    database: ${REDIS_DATABASE:0}
    timeout: 10000
    lettuce:
      pool:
        max-active: 16
        max-wait: -1
        max-idle: 16
        min-idle: 4

  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: true

jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:604800}
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:1209600}

ai:
  encryption:
    secret: ${AI_ENCRYPTION_SECRET}
  security:
    max-timeout: 300000
    max-tokens-limit: 32768
    rate-limit:
      requests-per-minute: 60
  providers:
    openai:
      base-url: "https://api.openai.com/v1"
    anthropic:
      base-url: "https://api.anthropic.com/v1"
    google:
      base-url: "https://generativelanguage.googleapis.com/v1"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  server:
    port: 8081

logging:
  level:
    com.example.pure: INFO
    org.springframework.security: WARN
  file:
    name: /opt/ai-proxy/logs/application.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
```

### 4. 启动应用

#### 创建启动脚本
**start.sh:**
```bash
#!/bin/bash

APP_HOME="/opt/ai-proxy"
APP_JAR="ai-proxy-1.0.0.jar"
PID_FILE="$APP_HOME/app.pid"
LOG_FILE="$APP_HOME/logs/startup.log"

# 创建日志目录
mkdir -p "$APP_HOME/logs"

# JVM参数
JVM_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$APP_HOME/logs/"
JVM_OPTS="$JVM_OPTS -Dspring.profiles.active=prod"

# 启动应用
cd "$APP_HOME"
nohup java $JVM_OPTS -jar "$APP_JAR" > "$LOG_FILE" 2>&1 &
echo $! > "$PID_FILE"

echo "应用启动中，PID: $(cat $PID_FILE)"
echo "日志文件: $LOG_FILE"
```

#### 创建停止脚本
**stop.sh:**
```bash
#!/bin/bash

APP_HOME="/opt/ai-proxy"
PID_FILE="$APP_HOME/app.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "停止应用，PID: $PID"
        kill $PID
        
        # 等待进程结束
        for i in {1..30}; do
            if ! ps -p $PID > /dev/null; then
                echo "应用已停止"
                rm -f "$PID_FILE"
                exit 0
            fi
            sleep 1
        done
        
        # 强制杀死进程
        echo "强制停止应用"
        kill -9 $PID
        rm -f "$PID_FILE"
    else
        echo "应用未运行"
        rm -f "$PID_FILE"
    fi
else
    echo "PID文件不存在"
fi
```

#### 设置权限并启动
```bash
chmod +x start.sh stop.sh
./start.sh
```

### 5. 系统服务配置

#### 创建systemd服务
**/etc/systemd/system/ai-proxy.service:**
```ini
[Unit]
Description=AI Proxy Service
After=network.target mysql.service redis.service

[Service]
Type=forking
User=ai-proxy
Group=ai-proxy
WorkingDirectory=/opt/ai-proxy
ExecStart=/opt/ai-proxy/start.sh
ExecStop=/opt/ai-proxy/stop.sh
PIDFile=/opt/ai-proxy/app.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 启用服务
```bash
# 创建专用用户
sudo useradd -r -s /bin/false ai-proxy
sudo chown -R ai-proxy:ai-proxy /opt/ai-proxy

# 重载systemd配置
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable ai-proxy
sudo systemctl start ai-proxy

# 查看服务状态
sudo systemctl status ai-proxy
```

## 🔍 监控配置

### Nginx反向代理

**nginx.conf:**
```nginx
upstream ai_proxy_backend {
    server 127.0.0.1:8080;
    # 如果有多个实例，可以添加更多server
    # server 127.0.0.1:8081;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 代理配置
    location / {
        proxy_pass http://ai_proxy_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # SSE流式响应特殊配置
    location /api/ai/chat/stream {
        proxy_pass http://ai_proxy_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE特殊配置
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://ai_proxy_backend/actuator/health;
        access_log off;
    }
}
```

### 日志配置

#### Logrotate配置
**/etc/logrotate.d/ai-proxy:**
```
/opt/ai-proxy/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ai-proxy ai-proxy
    postrotate
        systemctl reload ai-proxy
    endscript
}
```

### 监控脚本

**monitor.sh:**
```bash
#!/bin/bash

# 监控脚本
APP_URL="http://localhost:8080"
HEALTH_ENDPOINT="$APP_URL/actuator/health"
LOG_FILE="/var/log/ai-proxy-monitor.log"

# 检查应用健康状态
check_health() {
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_ENDPOINT")
    if [ "$response" = "200" ]; then
        echo "$(date): 应用健康检查通过" >> "$LOG_FILE"
        return 0
    else
        echo "$(date): 应用健康检查失败，状态码: $response" >> "$LOG_FILE"
        return 1
    fi
}

# 检查进程
check_process() {
    local pid_file="/opt/ai-proxy/app.pid"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null; then
            return 0
        fi
    fi
    return 1
}

# 重启应用
restart_app() {
    echo "$(date): 重启应用" >> "$LOG_FILE"
    systemctl restart ai-proxy
    sleep 30
}

# 主监控逻辑
main() {
    if ! check_process; then
        echo "$(date): 应用进程不存在，尝试重启" >> "$LOG_FILE"
        restart_app
    elif ! check_health; then
        echo "$(date): 应用健康检查失败，尝试重启" >> "$LOG_FILE"
        restart_app
    fi
}

main
```

#### 设置定时任务
```bash
# 添加到crontab
crontab -e

# 每5分钟检查一次
*/5 * * * * /opt/ai-proxy/monitor.sh
```

## 🔒 安全配置

### 防火墙配置
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🚨 故障排查

### 常见问题

#### 1. 应用启动失败
```bash
# 查看启动日志
tail -f /opt/ai-proxy/logs/startup.log

# 查看应用日志
tail -f /opt/ai-proxy/logs/application.log

# 检查端口占用
netstat -tlnp | grep 8080
```

#### 2. 数据库连接失败
```bash
# 测试数据库连接
mysql -h localhost -u ai_app -p ai_proxy_db

# 检查MySQL状态
sudo systemctl status mysql

# 查看MySQL错误日志
sudo tail -f /var/log/mysql/error.log
```

#### 3. Redis连接失败
```bash
# 测试Redis连接
redis-cli ping

# 检查Redis状态
sudo systemctl status redis

# 查看Redis日志
sudo tail -f /var/log/redis/redis-server.log
```

### 性能调优

#### JVM调优
```bash
# 生产环境JVM参数建议
JVM_OPTS="-Xms4g -Xmx8g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=/opt/ai-proxy/logs/"
```

#### 数据库调优
```sql
-- MySQL配置优化 (my.cnf)
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
max_connections = 200
query_cache_size = 0
query_cache_type = 0
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024-01-15  
**技术支持**: <EMAIL>
