# AI大模型API转发系统 - 数据库设计文档

## 📋 概述

本文档详细描述了AI大模型API转发系统的数据库设计，包括表结构、索引、约束、初始化数据等。

## 🗄️ 数据库配置

### 基本信息
- **数据库类型**: MySQL 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **时区**: UTC

### 创建数据库
```sql
CREATE DATABASE ai_proxy_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE ai_proxy_db;
```

## 📊 表结构设计

### 1. 用户AI配置表 (user_ai_config)

存储用户的AI模型使用偏好和默认配置。

```sql
CREATE TABLE user_ai_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID，关联用户表',
    preferred_model VARCHAR(50) DEFAULT 'gpt-3.5-turbo' COMMENT '偏好模型',
    default_temperature DECIMAL(3,2) DEFAULT 0.7 COMMENT '默认温度值 (0.0-2.0)',
    default_max_tokens INT DEFAULT 4096 COMMENT '默认最大token数',
    default_top_p DECIMAL(3,2) DEFAULT 1.0 COMMENT '默认top_p值 (0.0-1.0)',
    stream_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用流式输出',
    timeout_seconds INT DEFAULT 30 COMMENT '超时时间(秒)',
    system_prompt TEXT COMMENT '系统提示词',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_preferred_model (preferred_model),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB COMMENT='用户AI配置表';
```

### 2. 用户API密钥表 (user_api_keys)

存储用户的多个API密钥，支持负载均衡。

```sql
CREATE TABLE user_api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') NOT NULL COMMENT 'AI提供商',
    key_name VARCHAR(100) NOT NULL COMMENT '用户自定义密钥名称',
    api_key_encrypted TEXT NOT NULL COMMENT '加密的API Key',
    salt VARCHAR(32) NOT NULL COMMENT '加密盐值',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority INT DEFAULT 1 COMMENT '优先级（1最高）',
    usage_count BIGINT DEFAULT 0 COMMENT '使用次数统计',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_provider (user_id, provider),
    INDEX idx_active_priority (is_active, priority),
    INDEX idx_usage_count (usage_count),
    INDEX idx_last_used (last_used_at),
    
    CONSTRAINT uk_user_key_name UNIQUE (user_id, key_name)
) ENGINE=InnoDB COMMENT='用户API密钥表';
```

### 3. 系统配置表 (system_ai_config)

存储系统级别的AI配置，管理员可通过前端修改。

```sql
CREATE TABLE system_ai_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') NOT NULL COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置说明',
    is_user_modifiable BOOLEAN DEFAULT TRUE COMMENT '用户是否可以修改',
    category VARCHAR(50) DEFAULT 'GENERAL' COMMENT '配置分类',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_user_modifiable (is_user_modifiable)
) ENGINE=InnoDB COMMENT='系统AI配置表';
```

### 4. 负载均衡状态表 (api_key_load_balance)

记录API密钥的负载均衡状态和健康信息。

```sql
CREATE TABLE api_key_load_balance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') NOT NULL COMMENT 'AI提供商',
    api_key_id BIGINT NOT NULL COMMENT 'API密钥ID',
    current_requests INT DEFAULT 0 COMMENT '当前并发请求数',
    total_requests BIGINT DEFAULT 0 COMMENT '总请求数',
    success_requests BIGINT DEFAULT 0 COMMENT '成功请求数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    last_error_at TIMESTAMP NULL COMMENT '最后错误时间',
    last_error_message TEXT COMMENT '最后错误信息',
    is_healthy BOOLEAN DEFAULT TRUE COMMENT '健康状态',
    response_time_avg INT DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    last_health_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后健康检查时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_provider (user_id, provider),
    INDEX idx_api_key_id (api_key_id),
    INDEX idx_healthy (is_healthy),
    INDEX idx_last_health_check (last_health_check),
    
    FOREIGN KEY (api_key_id) REFERENCES user_api_keys(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='API密钥负载均衡状态表';
```

### 5. 聊天会话表 (chat_sessions)

存储聊天会话信息和临时配置覆盖。

```sql
CREATE TABLE chat_sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    model VARCHAR(50) COMMENT '当前会话使用的模型',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') COMMENT 'AI提供商',
    api_key_id BIGINT COMMENT '使用的API Key ID',
    temperature DECIMAL(3,2) COMMENT '会话级别的温度覆盖',
    max_tokens INT COMMENT '会话级别的最大token覆盖',
    top_p DECIMAL(3,2) COMMENT '会话级别的top_p覆盖',
    custom_prompt TEXT COMMENT '会话级别的自定义提示词',
    message_count INT DEFAULT 0 COMMENT '消息数量',
    total_tokens BIGINT DEFAULT 0 COMMENT '总token消耗',
    total_cost DECIMAL(10,4) DEFAULT 0.0000 COMMENT '总费用',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    expires_at TIMESTAMP COMMENT '会话过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at),
    INDEX idx_api_key_id (api_key_id),
    
    FOREIGN KEY (api_key_id) REFERENCES user_api_keys(id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='聊天会话表';
```

### 6. 聊天消息表 (chat_messages)

存储聊天消息历史记录。

```sql
CREATE TABLE chat_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    message_type ENUM('USER', 'ASSISTANT', 'SYSTEM') NOT NULL COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    model VARCHAR(50) COMMENT '使用的模型',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') COMMENT 'AI提供商',
    prompt_tokens INT DEFAULT 0 COMMENT '输入token数',
    completion_tokens INT DEFAULT 0 COMMENT '输出token数',
    total_tokens INT DEFAULT 0 COMMENT '总token数',
    cost DECIMAL(8,4) DEFAULT 0.0000 COMMENT '费用',
    response_time INT COMMENT '响应时间(毫秒)',
    finish_reason VARCHAR(20) COMMENT '完成原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_message_type (message_type),
    INDEX idx_created_at (created_at),
    INDEX idx_model (model),
    
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='聊天消息表';
```

### 7. API调用日志表 (api_call_logs)

记录所有API调用的详细日志，用于监控和分析。

```sql
CREATE TABLE api_call_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(64) COMMENT '会话ID',
    api_key_id BIGINT COMMENT 'API密钥ID',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') NOT NULL COMMENT 'AI提供商',
    model VARCHAR(50) NOT NULL COMMENT '使用的模型',
    endpoint VARCHAR(100) NOT NULL COMMENT 'API端点',
    request_method VARCHAR(10) DEFAULT 'POST' COMMENT '请求方法',
    request_size INT COMMENT '请求大小(字节)',
    response_size INT COMMENT '响应大小(字节)',
    status_code INT COMMENT 'HTTP状态码',
    response_time INT COMMENT '响应时间(毫秒)',
    prompt_tokens INT DEFAULT 0 COMMENT '输入token数',
    completion_tokens INT DEFAULT 0 COMMENT '输出token数',
    total_tokens INT DEFAULT 0 COMMENT '总token数',
    cost DECIMAL(8,4) DEFAULT 0.0000 COMMENT '费用',
    error_message TEXT COMMENT '错误信息',
    user_agent VARCHAR(255) COMMENT '用户代理',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_api_key_id (api_key_id),
    INDEX idx_provider_model (provider, model),
    INDEX idx_status_code (status_code),
    INDEX idx_created_at (created_at),
    INDEX idx_response_time (response_time),
    
    FOREIGN KEY (api_key_id) REFERENCES user_api_keys(id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='API调用日志表';
```

### 8. 系统监控表 (system_metrics)

存储系统性能指标和监控数据。

```sql
CREATE TABLE system_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(20) COMMENT '指标单位',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE', 'SYSTEM') COMMENT '提供商或系统',
    tags JSON COMMENT '标签信息',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_provider (provider),
    INDEX idx_recorded_at (recorded_at),
    INDEX idx_metric_name_provider (metric_name, provider)
) ENGINE=InnoDB COMMENT='系统监控指标表';
```

## 🔧 初始化数据

### 系统配置初始化
```sql
INSERT INTO system_ai_config (config_key, config_value, config_type, description, category) VALUES
('default_model', 'gpt-3.5-turbo', 'STRING', '系统默认模型', 'MODEL'),
('default_temperature', '0.7', 'NUMBER', '系统默认温度值', 'MODEL'),
('default_max_tokens', '4096', 'NUMBER', '系统默认最大token数', 'MODEL'),
('default_top_p', '1.0', 'NUMBER', '系统默认top_p值', 'MODEL'),
('default_timeout', '30', 'NUMBER', '系统默认超时时间(秒)', 'SYSTEM'),
('max_timeout_limit', '300', 'NUMBER', '最大超时限制(秒)', 'SYSTEM'),
('max_tokens_limit', '32768', 'NUMBER', '最大token硬限制', 'SYSTEM'),
('rate_limit_per_minute', '60', 'NUMBER', '每分钟最大请求数', 'SYSTEM'),
('session_expire_hours', '12', 'NUMBER', '会话过期时间(小时)', 'SYSTEM'),
('health_check_interval', '60', 'NUMBER', '健康检查间隔(秒)', 'SYSTEM'),
('load_balance_strategy', 'LEAST_CONNECTIONS', 'STRING', '负载均衡策略', 'SYSTEM'),
('max_error_threshold', '5', 'NUMBER', '最大错误阈值', 'SYSTEM'),
('recovery_check_interval', '300', 'NUMBER', '恢复检查间隔(秒)', 'SYSTEM');
```

### 支持的模型配置
```sql
-- 可以创建一个模型配置表来管理支持的模型
CREATE TABLE supported_models (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') NOT NULL COMMENT 'AI提供商',
    model_id VARCHAR(50) NOT NULL COMMENT '模型ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型显示名称',
    max_tokens INT NOT NULL COMMENT '最大token数',
    input_cost DECIMAL(8,6) NOT NULL COMMENT '输入费用(每1K token)',
    output_cost DECIMAL(8,6) NOT NULL COMMENT '输出费用(每1K token)',
    capabilities JSON COMMENT '模型能力',
    description TEXT COMMENT '模型描述',
    is_available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_provider_model (provider, model_id),
    INDEX idx_provider (provider),
    INDEX idx_available (is_available)
) ENGINE=InnoDB COMMENT='支持的模型配置表';

-- 插入支持的模型
INSERT INTO supported_models (provider, model_id, model_name, max_tokens, input_cost, output_cost, capabilities, description) VALUES
('OPENAI', 'gpt-3.5-turbo', 'GPT-3.5 Turbo', 4096, 0.0015, 0.002, '["chat", "completion"]', '快速、经济的对话模型'),
('OPENAI', 'gpt-4', 'GPT-4', 8192, 0.03, 0.06, '["chat", "completion", "analysis"]', '更强大的推理能力'),
('OPENAI', 'gpt-4-turbo', 'GPT-4 Turbo', 128000, 0.01, 0.03, '["chat", "completion", "analysis", "vision"]', '更快的GPT-4版本'),
('ANTHROPIC', 'claude-3-sonnet', 'Claude 3 Sonnet', 200000, 0.003, 0.015, '["chat", "analysis", "coding"]', '平衡性能和速度'),
('ANTHROPIC', 'claude-3-opus', 'Claude 3 Opus', 200000, 0.015, 0.075, '["chat", "analysis", "coding", "reasoning"]', '最强大的Claude模型'),
('GOOGLE', 'gemini-pro', 'Gemini Pro', 32768, 0.00025, 0.0005, '["chat", "completion"]', 'Google的多模态模型'),
('GOOGLE', 'gemini-pro-vision', 'Gemini Pro Vision', 16384, 0.00025, 0.0005, '["chat", "vision", "multimodal"]', '支持图像理解的Gemini模型');
```

## 📈 性能优化

### 分区策略
对于大数据量的日志表，可以考虑按时间分区：

```sql
-- 对API调用日志表按月分区
ALTER TABLE api_call_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- 继续添加更多分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 索引优化建议
```sql
-- 复合索引优化
CREATE INDEX idx_user_provider_active ON user_api_keys (user_id, provider, is_active);
CREATE INDEX idx_session_user_active ON chat_sessions (user_id, is_active, created_at);
CREATE INDEX idx_log_user_time ON api_call_logs (user_id, created_at);

-- 覆盖索引优化
CREATE INDEX idx_balance_status ON api_key_load_balance (user_id, provider, is_healthy, current_requests);
```

### 数据清理策略
```sql
-- 创建数据清理存储过程
DELIMITER //
CREATE PROCEDURE CleanupOldData()
BEGIN
    -- 清理30天前的API调用日志
    DELETE FROM api_call_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理过期的会话
    DELETE FROM chat_sessions WHERE expires_at < NOW();
    
    -- 清理90天前的系统监控数据
    DELETE FROM system_metrics WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
END //
DELIMITER ;

-- 创建定时任务（需要在系统层面配置）
-- 0 2 * * * mysql -u username -p database_name -e "CALL CleanupOldData();"
```

## 🔒 安全考虑

### 敏感数据加密
```sql
-- API密钥加密存储示例（应用层实现）
-- 使用AES-256-GCM加密算法
-- 每个用户使用不同的盐值
-- 加密密钥从环境变量获取，不存储在数据库中
```

### 访问控制
```sql
-- 创建只读用户用于监控
CREATE USER 'ai_monitor'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT ON ai_proxy_db.system_metrics TO 'ai_monitor'@'%';
GRANT SELECT ON ai_proxy_db.api_call_logs TO 'ai_monitor'@'%';

-- 创建应用用户
CREATE USER 'ai_app'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON ai_proxy_db.* TO 'ai_app'@'%';
```

### 审计日志
```sql
-- 启用MySQL审计日志（需要安装audit插件）
-- 记录所有对敏感表的操作
SET GLOBAL audit_log_policy = 'ALL';
SET GLOBAL audit_log_format = 'JSON';
```

---

**文档版本**: v1.0.0  
**最后更新**: 2024-01-15  
**数据库版本**: MySQL 8.0+
