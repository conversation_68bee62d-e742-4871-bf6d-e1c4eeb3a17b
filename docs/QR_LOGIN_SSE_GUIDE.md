# 二维码登录 SSE 功能使用指南

## 概述

本项目已完善二维码登录功能，支持两种通信方式：
- **WebSocket**：实时双向通信（原有功能）
- **SSE (Server-Sent Events)**：服务端推送流式通信（新增功能）

## 功能特性

### 1. 双通信模式支持
- 客户端可选择 WebSocket 或 SSE 通信方式
- 保持向后兼容性，默认使用 WebSocket
- 统一的 API 接口，根据参数自动选择通信方式

### 2. 服务层架构优化
- **统一通知服务**：`QRLoginNotificationService` 统一管理两种通信方式
- **WebSocket 服务层**：`QRLoginWebSocketService` 专门处理 WebSocket 通信
- **SSE 服务层**：`QRLoginSSEService` 专门处理 SSE 通信
- **控制器简化**：业务逻辑移至服务层，控制器只负责请求响应

### 3. SSE 专用功能
- 实时状态推送：扫描、确认、取消、错误
- 自动连接管理：超时、错误处理、资源清理
- 专用线程池：优化 SSE 长连接性能
- 结构化事件：标准化的事件格式

### 4. 系统监控增强
- 活跃连接数统计
- 内存使用情况监控
- 支持的通信类型信息
- 实时系统状态

## API 接口

### 1. 创建二维码
```http
GET /api/qrlogin/create?communicationType=SSE
```
**参数说明**：
- `communicationType`（可选）：通信类型，支持 `WEBSOCKET`（默认）或 `SSE`

**返回的二维码内容格式**：
- WebSocket: `qrlogin://qr-001?type=websocket`
- SSE: `qrlogin://qr-001?type=sse`

### 2. 查询二维码状态（HTTP 轮询 - 后备方案）
```http
GET /api/qrlogin/status/{qrId}
```
**使用场景**：
- WebSocket/SSE 不可用时的降级方案
- 简单的一次性状态查询
- 移动端兼容性考虑

### 3. SSE 订阅二维码状态（推荐）
```http
GET /api/qrlogin/sse/subscribe/{qrId}
Content-Type: text/event-stream
```

### 4. 扫描二维码
```http
POST /api/qrlogin/scan
Content-Type: application/json

{
  "qrId": "二维码ID",
  "token": "用户令牌"
  // 注意：通信类型从 Redis 中自动获取，移动端无需指定
}
```
**说明**：
- 移动端只需要提供 qrId 和 token
- 系统会自动从 Redis 中获取创建二维码时设置的通信类型
- 完全不需要移动端关心通信方式

### 5. 确认登录
```http
POST /api/qrlogin/confirm
Content-Type: application/json

{
  "qrId": "二维码ID",
  "confirmed": true
  // 注意：通信类型从 Redis 中自动获取，移动端无需指定
}
```

### 6. 获取系统统计信息
```http
GET /api/qrlogin/stats
```
**返回信息**：
- 活跃 SSE 连接数
- 系统时间戳

## SSE 事件格式

### 事件类型
- `start`: 开始监听
- `status`: 状态变化
- `login`: 登录成功
- `cancel`: 登录取消
- `error`: 错误事件
- `end`: 结束监听

### 事件数据结构
```json
{
  "qrId": "二维码ID",
  "eventType": "事件类型",
  "status": "二维码状态",
  "expired": false,
  "userInfo": {}, // 仅登录成功时
  "accessToken": "访问令牌", // 仅登录成功时
  "refreshToken": "刷新令牌", // 仅登录成功时
  "tokenExpiration": "2024-01-01T12:00:00Z", // 令牌过期时间（ISO 8601格式）
  "message": "事件描述",
  "error": "错误信息", // 仅错误时
  "timestamp": "2024-01-01T12:00:00Z",
  "isEnd": false
}
```

## 完整的登录流程

### Web 端（PC端）流程
1. **选择通信方式**：创建二维码时指定 `communicationType`
2. **建立连接**：根据选择的方式建立 SSE 或 WebSocket 连接
3. **显示二维码**：展示包含通信类型信息的二维码
4. **等待扫描**：监听状态变化事件
5. **处理结果**：接收登录成功或取消事件

### 移动端（扫码端）流程
1. **扫描二维码**：获取二维码内容（包含 qrId 和通信类型）
2. **解析内容**：系统自动解析通信类型
3. **调用扫描API**：发送扫描请求
4. **用户确认**：显示确认对话框
5. **发送结果**：调用确认API

### 关键设计原则
- **Web 端决定通信方式**：在创建二维码时选择
- **移动端无需关心**：通信类型信息嵌入二维码中
- **自动解析路由**：后端自动选择对应的通信服务
- **统一接口**：移动端 API 保持简洁

## 客户端使用示例

### JavaScript SSE 客户端
```javascript
// 1. 创建二维码（指定通信类型）
const createQRCode = async (communicationType = 'SSE') => {
  const response = await fetch(`/api/qrlogin/create?communicationType=${communicationType}`);
  const result = await response.json();
  return {
    qrId: result.data.qrId,
    qrContent: result.data.qrContent, // Base64 图片
    communicationType: communicationType
  };
};

// 2. 订阅 SSE 状态更新
const subscribeQRStatus = (qrId) => {
  const eventSource = new EventSource(`/api/qrlogin/sse/subscribe/${qrId}`);
  
  // 监听开始事件
  eventSource.addEventListener('start', (event) => {
    const data = JSON.parse(event.data);
    console.log('开始监听:', data);
  });
  
  // 监听状态变化
  eventSource.addEventListener('status', (event) => {
    const data = JSON.parse(event.data);
    console.log('状态变化:', data.status);
    
    if (data.status === 'SCANNED') {
      showMessage('二维码已扫描，等待确认...');
    }
  });
  
  // 监听登录成功
  eventSource.addEventListener('login', (event) => {
    const data = JSON.parse(event.data);
    console.log('登录成功:', data);
    
    // 保存令牌
    localStorage.setItem('accessToken', data.accessToken);
    localStorage.setItem('refreshToken', data.refreshToken);
    
    // 跳转到主页
    window.location.href = '/dashboard';
    
    eventSource.close();
  });
  
  // 监听取消事件
  eventSource.addEventListener('cancel', (event) => {
    const data = JSON.parse(event.data);
    showMessage('用户取消了登录');
    eventSource.close();
  });
  
  // 监听错误事件
  eventSource.addEventListener('error', (event) => {
    const data = JSON.parse(event.data);
    showError('登录失败: ' + data.error);
    eventSource.close();
  });
  
  // 连接错误处理
  eventSource.onerror = (error) => {
    console.error('SSE 连接错误:', error);
    eventSource.close();
  };
  
  return eventSource;
};

// 3. 完整的二维码登录流程
const startQRLogin = async (communicationType = 'SSE') => {
  try {
    // 创建二维码
    const qrData = await createQRCode(communicationType);

    // 显示二维码
    displayQRCode(qrData.qrContent);

    // 订阅状态更新
    const eventSource = subscribeQRStatus(qrData.qrId);
    
    // 设置超时
    setTimeout(() => {
      eventSource.close();
      showError('二维码已过期，请重新获取');
    }, 300000); // 5分钟超时
    
  } catch (error) {
    console.error('二维码登录失败:', error);
  }
};
```

### 移动端扫描示例
```javascript
// 移动端扫描二维码后调用
const scanQRCode = async (qrContent, userToken) => {
  // 解析二维码内容，提取 qrId
  const qrId = parseQRContent(qrContent); // 例如：从 "qrlogin://qr-001?type=sse" 中提取 "qr-001"

  const response = await fetch('/api/qrlogin/scan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      qrId: qrId,
      token: userToken
      // 注意：不需要指定 communicationType，后端会自动处理
    })
  });

  const result = await response.json();
  if (result.success) {
    showConfirmDialog(qrId); // 显示确认对话框
  }
};

// 简单的二维码内容解析函数
function parseQRContent(qrContent) {
  // 解析 "qrlogin://qr-001?type=sse" 格式
  const match = qrContent.match(/qrlogin:\/\/([^?]+)/);
  return match ? match[1] : null;
}

// 确认登录
const confirmLogin = async (qrId, confirmed) => {
  const response = await fetch('/api/qrlogin/confirm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      qrId: qrId,
      confirmed: confirmed
      // 注意：不需要指定 communicationType，后端会自动处理
    })
  });

  return await response.json();
};
```

## 配置说明

### 线程池配置
项目已添加 SSE 专用线程池 `sseTaskExecutor`：
- 核心线程数：10
- 最大线程数：50
- 队列容量：200
- 适合 SSE 长连接场景

### 超时配置
- SSE 连接超时：30秒
- 二维码有效期：5分钟
- 自动清理失效连接

## 通信方式选择指南

### 推荐优先级
1. **SSE (Server-Sent Events)** - 🌟 推荐
   - 单向推送，适合二维码登录场景
   - 更简单稳定，浏览器原生支持
   - 自动重连机制
   - 更好的错误处理

2. **WebSocket** - 备选方案
   - 双向通信能力
   - 适合需要客户端主动发送消息的场景
   - 连接管理相对复杂

3. **HTTP 轮询** - 降级方案
   - 最大兼容性
   - 网络环境限制时使用
   - 实时性较差，资源消耗大

### 自动降级策略
```javascript
async function createQRLogin() {
  // 1. 优先尝试 SSE
  if (typeof EventSource !== 'undefined') {
    return useSSELogin();
  }

  // 2. 降级到 WebSocket
  if (typeof WebSocket !== 'undefined') {
    return useWebSocketLogin();
  }

  // 3. 最后使用 HTTP 轮询
  return usePollingLogin();
}
```

## 最佳实践

1. **通信方式选择**
   - 优先使用 SSE，简单可靠
   - WebSocket 作为备选
   - HTTP 轮询作为最后降级方案

2. **错误处理**
   - 监听所有 SSE 事件类型
   - 实现连接重试机制
   - 设置合理的超时时间

3. **资源管理**
   - 及时关闭 SSE 连接
   - 避免内存泄漏
   - 监控连接数量

4. **安全考虑**
   - 验证二维码有效性
   - 检查用户权限
   - 防止重复扫描

5. **性能优化**
   - 使用专用线程池处理 SSE
   - 合理设置连接超时
   - 监控系统资源使用

## 监控和调试

- 查看活跃 SSE 连接数
- 监控线程池使用情况
- 日志记录关键事件
- 性能指标收集
