# 数据库初始化指南

## 概述

本文档说明如何为AI大模型API转发系统初始化数据库。系统使用MySQL数据库，包含用户管理和AI配置相关的表结构。

## 数据库要求

- **MySQL版本**: 5.7+ 或 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci

## 初始化步骤

### 1. 创建数据库

```sql
CREATE DATABASE IF NOT EXISTS pure_ai_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE pure_ai_system;
```

### 2. 执行表结构创建

执行项目中的 `src/main/resources/schema.sql` 文件来创建所有必要的表结构。

```bash
# 方式1：使用MySQL命令行
mysql -u your_username -p pure_ai_system < src/main/resources/schema.sql

# 方式2：在MySQL客户端中执行
SOURCE /path/to/your/project/src/main/resources/schema.sql;
```

## 表结构说明

### 核心用户表

#### 1. user（用户表）
- 存储用户的核心认证信息
- 包含用户名、密码、账户状态等
- 支持刷新令牌机制

#### 2. user_profile（用户资料表）
- 存储用户的扩展信息
- 包含邮箱、昵称、头像等
- 与user表一对一关系

#### 3. role（角色表）
- 存储系统角色信息
- 默认包含USER和ADMIN角色

#### 4. user_role（用户角色关联表）
- 用户与角色的多对多关系

### AI系统表

#### 5. user_ai_config（用户AI配置表）
- 存储用户的AI偏好设置
- 包含默认模型、参数配置等
- 每个用户一条记录

#### 6. user_api_keys（用户API密钥表）
- 存储用户的AI提供商API密钥
- 支持多个提供商（OpenAI、Anthropic、Google）
- 密钥加密存储，支持优先级和负载均衡

#### 7. api_key_load_balance（负载均衡状态表）
- 记录API密钥的负载状态
- 用于智能负载均衡算法
- 包含请求计数、错误统计、健康状态

#### 8. chat_sessions（聊天会话表）
- 存储聊天会话的配置信息
- 支持会话过期机制
- 记录使用的模型和API密钥

### 其他表

#### 9. access_log（访问日志表）
- 记录用户访问日志
- 用于统计和分析

## 初始数据

系统会自动插入以下初始数据：

```sql
-- 基础角色
INSERT INTO role (name) VALUES ('ROLE_USER'), ('ROLE_ADMIN') ON DUPLICATE KEY UPDATE name=name;
```

## 配置文件更新

确保 `application.yml` 中的数据库配置正确：

```yaml
spring:
  datasource:
    primary:
      url: ********************************************************************************************************************
      username: your_username
      password: your_password
      driver-class-name: com.mysql.cj.jdbc.Driver
```

## 验证安装

执行以下SQL验证表结构是否正确创建：

```sql
-- 检查所有表是否存在
SHOW TABLES;

-- 检查用户表结构
DESCRIBE user;
DESCRIBE user_profile;

-- 检查AI相关表结构
DESCRIBE user_ai_config;
DESCRIBE user_api_keys;
DESCRIBE api_key_load_balance;
DESCRIBE chat_sessions;

-- 检查角色数据
SELECT * FROM role;
```

## 注意事项

1. **数据一致性**: 系统不使用外键约束，数据一致性通过应用层逻辑保证，这符合微服务架构的最佳实践。

2. **时间字段**: 使用DATETIME(3)支持毫秒精度，与Java的Instant类型兼容。所有时间统一使用UTC时区存储，前端负责根据用户系统时间进行时区转换。

3. **字符集**: 使用utf8mb4字符集支持emoji和特殊字符。

4. **索引优化**: 为常用查询字段添加了索引，提高查询性能。

5. **数据安全**: API密钥采用加密存储，不会以明文形式保存。

## 故障排除

### 常见问题

1. **字符集问题**: 确保数据库和表都使用utf8mb4字符集
2. **表创建顺序**: 虽然没有外键约束，但建议按照schema.sql中的顺序创建表
3. **权限问题**: 确保数据库用户有足够的权限创建表和索引

### 重置数据库

如果需要重置数据库：

```sql
-- 删除所有表（注意：这会删除所有数据）
DROP TABLE IF EXISTS chat_sessions;
DROP TABLE IF EXISTS api_key_load_balance;
DROP TABLE IF EXISTS user_api_keys;
DROP TABLE IF EXISTS user_ai_config;
DROP TABLE IF EXISTS access_log;
DROP TABLE IF EXISTS user_role;
DROP TABLE IF EXISTS user_profile;
DROP TABLE IF EXISTS role;
DROP TABLE IF EXISTS user;

-- 然后重新执行schema.sql
```

## 备份建议

建议定期备份数据库，特别是用户数据和API密钥配置：

```bash
# 备份整个数据库
mysqldump -u username -p pure_ai_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 仅备份用户数据
mysqldump -u username -p pure_ai_system user user_profile user_role > user_backup.sql
```
