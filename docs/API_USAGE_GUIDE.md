# AI大模型API转发系统使用指南

## 概述

本系统提供完全兼容OpenAI API格式的AI大模型转发服务，支持多个AI提供商（OpenAI、Anthropic、Google AI），具备智能负载均衡、API密钥管理等功能。

## 快速开始

### 1. 系统健康检查

首先检查系统是否正常运行：

```bash
curl -X GET "http://localhost:8080/api/ai/test/health"
```

### 2. 用户认证

系统需要用户认证，请先登录获取JWT Token：

```bash
curl -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

## API密钥管理

### 1. 添加API密钥

```bash
curl -X POST "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "OPENAI",
    "keyName": "主要密钥",
    "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "priority": 1,
    "isActive": true
  }'
```

响应示例：
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "keyId": 1,
    "compatibleApiKey": "sk-1001_1_a1b2c3d4e5f6",
    "message": "API密钥已添加，兼容格式密钥已生成"
  }
}
```

### 2. 获取API密钥列表

```bash
curl -X GET "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 测试API密钥

```bash
curl -X POST "http://localhost:8080/api/ai/config/api-keys/1/test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## OpenAI兼容API使用

### 1. 聊天完成（非流式）

使用系统生成的兼容格式API密钥：

```bash
curl -X POST "http://localhost:8080/v1/chat/completions" \
  -H "Authorization: Bearer sk-1001_1_a1b2c3d4e5f6" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "temperature": 0.7,
    "max_tokens": 150,
    "stream": false
  }'
```

### 2. 聊天完成（流式）

```bash
curl -X POST "http://localhost:8080/v1/chat/completions" \
  -H "Authorization: Bearer sk-1001_1_a1b2c3d4e5f6" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "写一首关于春天的诗"
      }
    ],
    "temperature": 0.8,
    "max_tokens": 500,
    "stream": true
  }'
```

### 3. 获取模型列表

```bash
curl -X GET "http://localhost:8080/v1/models" \
  -H "Authorization: Bearer sk-1001_1_a1b2c3d4e5f6"
```

## 用户配置管理

### 1. 获取用户AI配置

```bash
curl -X GET "http://localhost:8080/api/ai/config/user" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. 更新用户AI配置

```bash
curl -X PUT "http://localhost:8080/api/ai/config/user" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "preferredModel": "gpt-4",
    "defaultTemperature": 0.7,
    "defaultMaxTokens": 2048,
    "defaultTopP": 1.0,
    "streamEnabled": true,
    "timeoutSeconds": 60,
    "systemPrompt": "你是一个专业的AI助手，请用中文回答问题。"
  }'
```

## 在AI工具中使用

### 1. ChatGPT客户端配置

- **API端点**: `http://localhost:8080/v1`
- **API密钥**: 使用系统生成的兼容格式密钥（如：`sk-1001_1_a1b2c3d4e5f6`）
- **模型**: 选择您配置的模型（如：`gpt-3.5-turbo`）

### 2. Cursor IDE配置

在Cursor的设置中：
- **OpenAI API Base URL**: `http://localhost:8080/v1`
- **OpenAI API Key**: `sk-1001_1_a1b2c3d4e5f6`

### 3. 其他OpenAI兼容工具

任何支持自定义OpenAI API端点的工具都可以使用：
- 将API端点设置为：`http://localhost:8080/v1`
- 使用系统生成的兼容格式API密钥

## 负载均衡和监控

### 1. 查看API密钥负载统计

```bash
curl -X GET "http://localhost:8080/api/ai/config/api-keys/1/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. 系统统计信息

```bash
curl -X GET "http://localhost:8080/api/ai/test/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 错误处理

### 常见错误码

- **401**: API密钥无效或已过期
- **429**: 请求频率限制
- **500**: 服务器内部错误
- **503**: 所有API密钥不可用

### 错误响应格式

```json
{
  "error": {
    "type": "invalid_request_error",
    "message": "Invalid API key",
    "code": "invalid_api_key"
  }
}
```

## 最佳实践

### 1. API密钥管理

- 为不同用途配置多个API密钥
- 设置合适的优先级实现负载均衡
- 定期检查API密钥的健康状态
- 及时更新失效的API密钥

### 2. 性能优化

- 使用流式响应提升用户体验
- 根据需要调整超时时间
- 合理设置max_tokens避免不必要的消耗

### 3. 安全建议

- 定期轮换API密钥
- 监控API使用情况
- 设置合理的请求频率限制

## 故障排除

### 1. 连接问题

检查系统健康状态：
```bash
curl -X GET "http://localhost:8080/v1/health"
```

### 2. 认证问题

验证兼容格式API密钥：
```bash
curl -X POST "http://localhost:8080/api/ai/test/parse-key" \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "sk-1001_1_a1b2c3d4e5f6"}'
```

### 3. 模型支持

检查模型是否支持：
```bash
curl -X GET "http://localhost:8080/api/ai/test/models/support/gpt-3.5-turbo"
```

## 技术支持

如遇到问题，请检查：
1. 系统日志：查看应用日志了解详细错误信息
2. 数据库连接：确保数据库正常运行
3. 网络连接：确保能访问AI提供商的API端点
4. API密钥：确保原始API密钥有效且有足够额度
