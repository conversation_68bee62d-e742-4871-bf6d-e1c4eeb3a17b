# Function Calling 功能测试文档

## 概述

本文档描述了为OpenAI兼容API添加Function Calling支持的实现和测试方法。

## 实现的功能

### 1. OpenAI兼容格式支持
- 支持标准的OpenAI Function Calling格式
- 包含 `tools` 和 `tool_choice` 参数
- 支持 `function` 类型的工具定义

### 2. 多提供商支持
- **OpenAI**: 原生支持，直接转发
- **Gemini**: 支持OpenAI兼容格式，直接转发
- **Claude**: 自动转换格式（`parameters` → `input_schema`）

### 3. 响应格式转换
- **OpenAI/Gemini**: 响应格式相同，直接返回
- **Claude**: 自动转换 `tool_use` 为 `tool_calls` 格式

## API 请求格式

### 基本Function Calling请求示例

```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "北京今天的天气怎么样？"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "获取指定城市的天气信息",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "城市名称，例如：北京、上海"
            },
            "unit": {
              "type": "string",
              "enum": ["celsius", "fahrenheit"],
              "description": "温度单位"
            }
          },
          "required": ["location"]
        }
      }
    }
  ],
  "tool_choice": "auto"
}
```

### 工具选择策略

- `"auto"`: 自动选择是否使用工具（默认）
- `"none"`: 不使用任何工具
- `"required"`: 强制使用工具
- `{"type": "function", "function": {"name": "function_name"}}`: 强制使用指定工具

## 响应格式

### 包含Function Call的响应

```json
{
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": null,
        "tool_calls": [
          {
            "id": "call_abc123",
            "type": "function",
            "function": {
              "name": "get_weather",
              "arguments": "{\"location\": \"北京\", \"unit\": \"celsius\"}"
            }
          }
        ]
      },
      "finish_reason": "tool_calls"
    }
  ],
  "usage": {
    "prompt_tokens": 82,
    "completion_tokens": 17,
    "total_tokens": 99
  }
}
```

## 测试方法

### 1. 使用curl测试

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user", 
        "content": "北京今天的天气怎么样？"
      }
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "get_weather",
          "description": "获取指定城市的天气信息",
          "parameters": {
            "type": "object",
            "properties": {
              "location": {
                "type": "string",
                "description": "城市名称"
              }
            },
            "required": ["location"]
          }
        }
      }
    ]
  }'
```

### 2. 测试不同提供商

#### OpenAI测试
- 模型: `gpt-4`, `gpt-3.5-turbo`
- 预期: 直接支持，无需转换

#### Gemini测试  
- 模型: `gemini-pro`, `gemini-1.5-pro`
- 预期: 支持OpenAI兼容格式

#### Claude测试
- 模型: `claude-3-sonnet`, `claude-3-haiku`
- 预期: 自动转换格式，响应转换为OpenAI格式

### 3. 流式响应测试

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "计算 15 * 23"}],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "calculate",
          "description": "执行数学计算",
          "parameters": {
            "type": "object",
            "properties": {
              "expression": {"type": "string"}
            },
            "required": ["expression"]
          }
        }
      }
    ],
    "stream": true
  }'
```

## 实现细节

### 1. 请求处理流程
1. 接收OpenAI格式的Function Calling请求
2. 根据提供商类型转换工具格式（Claude特殊处理）
3. 发送请求到对应的AI提供商
4. 接收响应并转换格式（Claude特殊处理）
5. 返回OpenAI兼容格式的响应

### 2. Claude格式转换
- **请求转换**: `tools[].function.parameters` → `tools[].input_schema`
- **响应转换**: `tool_use` → `tool_calls`

### 3. 错误处理
- 格式转换失败时返回原始数据
- 提供详细的错误日志
- 保证服务稳定性

## 注意事项

1. **兼容性**: 完全兼容OpenAI Function Calling API
2. **性能**: Claude需要额外的格式转换，可能有轻微性能影响
3. **错误处理**: 转换失败时会降级到原始格式
4. **日志**: 提供详细的调试日志用于问题排查

## JSON Output 支持

### 1. 请求格式

```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "请返回北京的天气信息"
    }
  ],
  "response_format": {
    "type": "json_object"
  }
}
```

### 2. 提供商支持

| 提供商 | 支持方式 | 实现 |
|--------|----------|------|
| **OpenAI** | ✅ 原生支持 | `response_format` 参数 |
| **Gemini** | ✅ 兼容支持 | `response_format` 参数 |
| **Claude** | ✅ 自动转换 | 自动添加JSON输出提示词 |

### 3. Claude自动转换

对于Claude，系统会自动在用户消息后添加：
```
请以有效的JSON格式返回响应，不要包含任何其他文本或解释。
```

### 4. 统一响应格式

所有提供商都返回相同的OpenAI兼容格式：
```json
{
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "{\"temperature\": 25, \"condition\": \"sunny\"}"
      }
    }
  ]
}
```

## Stream Options 支持

### 1. 请求格式

```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "user",
      "content": "计算 15 * 23"
    }
  ],
  "stream": true,
  "stream_options": {
    "include_usage": true
  }
}
```

### 2. 提供商支持

| 提供商 | 支持方式 | 实现 |
|--------|----------|------|
| **OpenAI** | ✅ 原生支持 | `stream_options` 参数 |
| **Gemini** | ✅ 兼容支持 | `stream_options` 参数 |
| **Claude** | ✅ 自动包含 | 在 `message_stop` 事件中自动包含usage |

### 3. 响应格式

#### OpenAI/Gemini 流式响应（包含usage）
```
data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-4","choices":[{"index":0,"delta":{"role":"assistant","content":"15 * 23 = 345"},"finish_reason":null}]}

data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-4","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"usage":{"prompt_tokens":10,"completion_tokens":8,"total_tokens":18}}

data: [DONE]
```

#### Claude 自动包含usage
Claude在 `message_stop` 事件中自动包含usage信息，无需额外参数：
```
data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"15 * 23 = 345"}}

data: {"type":"message_stop","usage":{"input_tokens":10,"output_tokens":8}}
```

### 4. 测试示例

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "计算 15 * 23"}
    ],
    "stream": true,
    "stream_options": {
      "include_usage": true
    }
  }'
```

## 后续优化

1. 添加Function Calling的缓存机制
2. 优化Claude格式转换的性能
3. 添加更多的工具类型支持
4. 完善错误处理和重试机制
5. 添加JSON Schema验证支持
