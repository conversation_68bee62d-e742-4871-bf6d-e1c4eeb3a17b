# AI大模型API转发系统架构文档

## 📋 项目概述

### 系统简介
AI大模型API转发系统是一个高性能、高可用的中间件服务，**完全兼容OpenAI API格式**，为所有支持自定义API的AI工具提供统一的大模型访问接口。系统支持多个AI提供商（OpenAI、Anthropic、Google AI）的API转发，具备智能负载均衡、配置管理、安全认证等核心功能。

### 🎯 兼容性支持
**支持所有使用OpenAI兼容API的软件和工具：**
- **IDE/编辑器**: Cursor, VS Code插件, JetBrains IDEs, Vim/Neovim, Emacs
- **聊天客户端**: ChatGPT Next Web, ChatBox, OpenCat, 各种第三方客户端
- **开发框架**: LangChain, LlamaIndex, AutoGPT, Vercel AI SDK, Streamlit
- **移动应用**: 各种第三方ChatGPT应用和AI助手
- **企业工具**: 内部聊天机器人、文档问答系统、客户服务自动化

### 核心特性
- 🔌 **OpenAI完全兼容**：标准的`/v1/chat/completions`端点，即插即用
- 🔄 **智能负载均衡**：多API Key轮询、健康检查、故障自动转移
- ⚙️ **灵活配置管理**：四层配置优先级，实时生效，前端完全可控
- 🔒 **安全可靠**：API Key加密存储、多重认证、权限控制
- 🚀 **高性能**：异步流式处理、Redis缓存、连接池复用
- 📊 **监控审计**：请求统计、错误追踪、使用分析
- 🌐 **多提供商支持**：OpenAI、Anthropic、Google AI统一接口

### 技术栈
- **后端框架**：Spring Boot 2.7.18 + Spring Security + Spring WebFlux
- **数据库**：MySQL 8.0 + Redis 6.0
- **认证授权**：JWT + Spring Security
- **API文档**：SpringDoc OpenAPI 3.0
- **监控日志**：Logback + Actuator

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  AI工具/软件    │    │  OpenAI兼容层   │    │   配置管理API   │    │   内部聊天API   │
│                 │    │                 │    │                 │    │                 │
│ ├─ Cursor IDE   │◄──►│ /v1/chat/      │    │ AiConfigController│    │ AiChatController│
│ ├─ VS Code      │    │ completions     │◄──►│                 │◄──►│                 │
│ ├─ ChatGPT Web  │    │                 │    │ ├─ 用户配置     │    │ ├─ 流式聊天     │
│ ├─ LangChain    │    │ ├─ 格式转换     │    │ ├─ 密钥管理     │    │ ├─ 会话管理     │
│ ├─ AutoGPT      │    │ ├─ 认证适配     │    │ ├─ 负载均衡     │    │ └─ 模型路由     │
│ └─ 其他AI工具   │    │ ├─ 响应标准化   │    │ └─ API Key映射  │    │                 │
└─────────────────┘    │ └─ 流式处理     │    └─────────────────┘    └─────────────────┘
                       └─────────────────┘             │                       │
                                │                       ▼                       ▼
                                ▼              ┌─────────────────┐    ┌─────────────────┐
                       ┌─────────────────┐    │   配置服务层    │    │   聊天服务层    │
                       │  标准API端点    │    │                 │    │                 │
                       │                 │    │ ├─ 配置管理     │    │ ├─ 负载均衡器   │
                       │ /v1/models      │    │ ├─ 密钥加密     │    │ ├─ 模型适配器   │
                       │ /v1/chat/       │    │ ├─ 权限控制     │    │ ├─ 流式处理器   │
                       │ completions     │    │ └─ 审计日志     │    │ └─ 会话管理器   │
                       │ /v1/embeddings  │    └─────────────────┘    └─────────────────┘
                       └─────────────────┘             │                       │
                                │                       ▼                       ▼
                                └──────────────►┌─────────────────────────────────────────┐
                                               │            数据存储层                   │
                                               │                                         │
                                               │ ├─ MySQL: 配置数据、用户信息、审计日志  │
                                               │ ├─ Redis: 会话缓存、负载均衡状态       │
                                               │ └─ 配置缓存: 热点配置快速访问          │
                                               └─────────────────────────────────────────┘
                                                                │
                                                                ▼
                                               ┌─────────────────────────────────────────┐
                                               │          AI模型提供商                   │
                                               │                                         │
                                               │ ├─ OpenAI API (GPT-3.5/4, 多Key轮询)  │
                                               │ ├─ Anthropic API (Claude, 多Key轮询)   │
                                               │ └─ Google AI API (Gemini, 多Key轮询)   │
                                               └─────────────────────────────────────────┘
```

### 核心组件

#### 1. OpenAI兼容层 (OpenAI Compatibility Layer)
- **OpenAiCompatibleController**: 提供标准OpenAI API端点 (`/v1/chat/completions`, `/v1/models`)
- **FormatConverterService**: 双向转换OpenAI格式与内部格式
- **ApiKeyMappingService**: 生成和解析OpenAI兼容的API Key格式
- **StreamAdapterService**: 适配SSE流式响应为OpenAI标准格式

#### 2. 控制器层 (Controller Layer)
- **AiConfigController**: 配置管理API，处理用户设置、密钥管理、负载均衡配置
- **AiChatController**: 内部聊天API，处理流式对话、会话管理、模型选择

#### 3. 服务层 (Service Layer)
- **AiConfigService**: 配置管理服务，负责配置的CRUD操作和权限控制
- **AiChatService**: 聊天服务，负责消息处理和流式响应
- **LoadBalancerService**: 负载均衡服务，实现智能API Key选择
- **ModelAdapterService**: 模型适配服务，统一不同提供商的API格式

#### 4. 数据访问层 (Data Access Layer)
- **ConfigMapper**: 配置数据访问
- **ApiKeyMapper**: API密钥数据访问
- **SessionMapper**: 会话数据访问
- **AuditMapper**: 审计日志数据访问

## 📊 数据库设计

### 核心表结构

#### 用户AI配置表 (user_ai_config)
```sql
CREATE TABLE user_ai_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL UNIQUE,
    preferred_model VARCHAR(50) DEFAULT 'gpt-3.5-turbo',
    default_temperature DECIMAL(3,2) DEFAULT 0.7,
    default_max_tokens INT DEFAULT 4096,
    default_top_p DECIMAL(3,2) DEFAULT 1.0,
    stream_enabled BOOLEAN DEFAULT TRUE,
    timeout_seconds INT DEFAULT 30,
    system_prompt TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);
```

#### 用户API密钥表 (user_api_keys)
```sql
CREATE TABLE user_api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    api_key_encrypted TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    priority INT DEFAULT 1,
    usage_count BIGINT DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_provider (user_id, provider),
    INDEX idx_active_priority (is_active, priority)
);
```

#### 负载均衡状态表 (api_key_load_balance)
```sql
CREATE TABLE api_key_load_balance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE') NOT NULL,
    api_key_id BIGINT NOT NULL,
    current_requests INT DEFAULT 0,
    total_requests BIGINT DEFAULT 0,
    error_count INT DEFAULT 0,
    last_error_at TIMESTAMP NULL,
    is_healthy BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_provider (user_id, provider),
    INDEX idx_healthy (is_healthy)
);
```

#### 聊天会话表 (chat_sessions)
```sql
CREATE TABLE chat_sessions (
    session_id VARCHAR(64) PRIMARY KEY,
    user_id BIGINT NOT NULL,
    model VARCHAR(50),
    provider ENUM('OPENAI', 'ANTHROPIC', 'GOOGLE'),
    api_key_id BIGINT,
    temperature DECIMAL(3,2),
    max_tokens INT,
    custom_prompt TEXT,
    message_count INT DEFAULT 0,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at)
);
```

## 🔧 配置管理

### 配置优先级体系
系统采用四层配置优先级，确保最大的灵活性：

1. **请求参数** (最高优先级) - 单次请求的临时覆盖
2. **会话配置** - 当前会话的持久化设置
3. **用户配置** - 用户的个人偏好设置
4. **系统默认配置** (最低优先级) - 全局默认值

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| model | String | gpt-3.5-turbo | 使用的AI模型 |
| temperature | Decimal | 0.7 | 生成随机性 (0.0-2.0) |
| max_tokens | Integer | 4096 | 最大生成token数 |
| top_p | Decimal | 1.0 | 核采样参数 (0.0-1.0) |
| stream_enabled | Boolean | true | 是否启用流式输出 |
| timeout_seconds | Integer | 30 | 请求超时时间 |
| system_prompt | Text | null | 系统提示词 |

### 环境配置
```yaml
# application.yml
ai:
  security:
    max-timeout: 300000        # 最大超时限制（5分钟）
    max-tokens-limit: 32768    # 最大token硬限制
    rate-limit:
      requests-per-minute: 60  # 每分钟最大请求数
  encryption:
    key-rotation-days: 30      # API Key轮换周期
  providers:
    openai:
      base-url: "https://api.openai.com/v1"
      models: ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
    anthropic:
      base-url: "https://api.anthropic.com/v1"
      models: ["claude-3-sonnet", "claude-3-opus"]
    google:
      base-url: "https://generativelanguage.googleapis.com/v1"
      models: ["gemini-pro", "gemini-pro-vision"]
```

## 🔄 负载均衡策略

### 智能负载均衡算法
系统实现了多种负载均衡策略的组合：

1. **轮询策略 (Round Robin)**
   - 按顺序轮流使用API Key
   - 确保负载均匀分布

2. **最少连接策略 (Least Connections)**
   - 优先选择当前并发请求数最少的API Key
   - 适应不同响应时间的场景

3. **权重策略 (Weighted)**
   - 根据API Key的优先级分配权重
   - 支持用户自定义优先级

4. **健康检查 (Health Check)**
   - 实时监控API Key的健康状态
   - 自动剔除故障的API Key
   - 支持故障恢复后的自动重新加入

### 故障处理机制
- **熔断器模式**: 连续失败达到阈值时暂时停用API Key
- **重试机制**: 支持指数退避的重试策略
- **降级策略**: 在所有API Key不可用时的降级处理
- **监控告警**: 实时监控API Key状态，异常时发送告警

## 🔒 安全设计

### API Key安全
- **加密存储**: 使用AES-256加密算法存储API Key
- **访问控制**: 基于JWT的用户身份验证
- **权限隔离**: 用户只能访问自己的API Key
- **审计日志**: 记录所有API Key的使用情况

### 数据安全
- **传输加密**: 全程HTTPS通信
- **敏感信息脱敏**: 日志中自动脱敏API Key等敏感信息
- **会话管理**: 会话自动过期和清理机制
- **输入验证**: 严格的参数校验和SQL注入防护

## 📈 性能优化

### 缓存策略
- **配置缓存**: Redis缓存用户配置，减少数据库查询
- **会话缓存**: 会话信息缓存，提升响应速度
- **连接池**: HTTP连接池复用，减少连接开销

### 异步处理
- **流式响应**: 使用SSE实现实时流式输出
- **异步调用**: WebFlux异步处理，提升并发能力
- **队列机制**: 请求队列缓冲，平滑流量峰值

### 监控指标
- **响应时间**: 平均响应时间、P95、P99响应时间
- **吞吐量**: QPS、并发用户数
- **错误率**: 4xx、5xx错误率统计
- **资源使用**: CPU、内存、数据库连接池使用率

## 📚 API接口文档

### OpenAI兼容API (主要接口)

#### 1. 聊天完成接口 (兼容所有AI工具)
```http
POST /v1/chat/completions
Authorization: Bearer sk-{userId}_{keyId}_{hash}
Content-Type: application/json
```

**请求示例 (标准OpenAI格式):**
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": true,
  "temperature": 0.7,
  "max_tokens": 2048,
  "top_p": 1.0
}
```

**响应示例 (流式):**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"usage":{"prompt_tokens":9,"completion_tokens":12,"total_tokens":21}}

data: [DONE]
```

#### 2. 模型列表接口
```http
GET /v1/models
Authorization: Bearer sk-{userId}_{keyId}_{hash}
```

**响应示例:**
```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-3.5-turbo",
      "object": "model",
      "created": 1677610602,
      "owned_by": "openai"
    },
    {
      "id": "gpt-4",
      "object": "model",
      "created": 1687882411,
      "owned_by": "openai"
    }
  ]
}
```

### 配置管理API (管理后台)

#### 1. 获取用户配置
```http
GET /api/ai/config/user
Authorization: Bearer {jwt_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "preferredModel": "gpt-3.5-turbo",
    "defaultTemperature": 0.7,
    "defaultMaxTokens": 4096,
    "defaultTopP": 1.0,
    "streamEnabled": true,
    "timeoutSeconds": 30,
    "systemPrompt": "你是一个有用的AI助手"
  }
}
```

#### 2. 更新用户配置
```http
PUT /api/ai/config/user
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "preferredModel": "gpt-4",
  "defaultTemperature": 0.8,
  "defaultMaxTokens": 2048,
  "streamEnabled": true,
  "timeoutSeconds": 45,
  "systemPrompt": "你是一个专业的编程助手"
}
```

#### 3. 获取API密钥列表
```http
GET /api/ai/config/api-keys
Authorization: Bearer {jwt_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "provider": "OPENAI",
      "keyName": "主要密钥",
      "isActive": true,
      "priority": 1,
      "usageCount": 1250,
      "lastUsedAt": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 4. 添加API密钥
```http
POST /api/ai/config/api-keys
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "provider": "OPENAI",
  "keyName": "备用密钥",
  "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "priority": 2
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "keyId": 123,
    "compatibleApiKey": "sk-1001_123_a1b2c3d4e5f6",
    "message": "API密钥已添加，兼容格式密钥已生成"
  }
}
```

#### 5. 测试API密钥
```http
POST /api/ai/config/api-keys/{keyId}/test
Authorization: Bearer {jwt_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "测试成功",
  "data": {
    "isValid": true,
    "responseTime": 1250,
    "model": "gpt-3.5-turbo",
    "testMessage": "API密钥有效，连接正常",
    "compatibleApiKey": "sk-1001_123_a1b2c3d4e5f6"
  }
}
```

### 聊天API

#### 1. 流式聊天
```http
POST /api/ai/chat/stream
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "sessionId": "session_123456789",
  "message": "你好，请介绍一下人工智能",
  "model": "gpt-3.5-turbo",
  "temperature": 0.7,
  "maxTokens": 2048,
  "streamEnabled": true
}
```

**SSE响应流:**
```
data: {"type":"start","sessionId":"session_123456789"}

data: {"type":"content","content":"你好！"}

data: {"type":"content","content":"人工智能"}

data: {"type":"end","usage":{"promptTokens":15,"completionTokens":200,"totalTokens":215}}
```

#### 2. 创建新会话
```http
GET /api/ai/chat/session/new
Authorization: Bearer {jwt_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "会话创建成功",
  "data": "session_1705123456789"
}
```

#### 3. 获取可用模型
```http
GET /api/ai/chat/models
Authorization: Bearer {jwt_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "OPENAI": [
      {
        "id": "gpt-3.5-turbo",
        "name": "GPT-3.5 Turbo",
        "maxTokens": 4096,
        "description": "快速、经济的对话模型"
      },
      {
        "id": "gpt-4",
        "name": "GPT-4",
        "maxTokens": 8192,
        "description": "更强大的推理能力"
      }
    ],
    "ANTHROPIC": [
      {
        "id": "claude-3-sonnet",
        "name": "Claude 3 Sonnet",
        "maxTokens": 200000,
        "description": "平衡性能和速度"
      }
    ]
  }
}
```

## 🔌 AI工具配置示例

### Cursor IDE配置
```json
{
  "openai.apiKey": "sk-1001_123_a1b2c3d4e5f6",
  "openai.apiBase": "https://your-domain.com/v1",
  "openai.model": "gpt-3.5-turbo"
}
```

### VS Code配置 (CodeGPT插件)
```json
{
  "codegpt.apiKey": "sk-1001_123_a1b2c3d4e5f6",
  "codegpt.apiUrl": "https://your-domain.com/v1/chat/completions",
  "codegpt.model": "gpt-3.5-turbo"
}
```

### LangChain配置
```python
from langchain.llms import OpenAI

llm = OpenAI(
    openai_api_key="sk-1001_123_a1b2c3d4e5f6",
    openai_api_base="https://your-domain.com/v1",
    model_name="gpt-3.5-turbo"
)
```

### ChatGPT Next Web配置
```bash
# 环境变量
OPENAI_API_KEY=sk-1001_123_a1b2c3d4e5f6
BASE_URL=https://your-domain.com/v1
```

## 🚀 部署指南

### 环境要求
- **Java**: JDK 11+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **内存**: 最低2GB，推荐4GB+
- **CPU**: 最低2核，推荐4核+

### 部署步骤

#### 1. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE ai_proxy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行建表脚本
SOURCE /path/to/schema.sql;

-- 插入初始配置数据
SOURCE /path/to/init_data.sql;
```

#### 2. 配置文件
```yaml
# application-prod.yml
spring:
  datasource:
    url: ***************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}

jwt:
  secret: ${JWT_SECRET}
  expiration: 604800

ai:
  encryption:
    secret: ${AI_ENCRYPTION_SECRET}
```

#### 3. 启动应用
```bash
# 构建应用
mvn clean package -Pprod

# 启动应用
java -jar target/pure-1.0-SNAPSHOT.jar --spring.profiles.active=prod
```

#### 4. Docker部署
```dockerfile
FROM openjdk:11-jre-slim

COPY target/pure-1.0-SNAPSHOT.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ai_proxy_db
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}

  redis:
    image: redis:6.0-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
```

### 健康检查
```bash
# 应用健康检查
curl http://localhost:8080/actuator/health

# AI服务健康检查
curl -H "Authorization: Bearer {token}" \
     http://localhost:8080/api/ai/chat/health
```

## 🔍 故障排查

### 常见问题

#### 1. API Key无效
**症状**: 返回401 Unauthorized错误
**解决方案**:
- 检查API Key是否正确
- 验证API Key是否有足够的配额
- 确认API Key对应的模型权限

#### 2. 负载均衡失效
**症状**: 请求总是使用同一个API Key
**解决方案**:
- 检查Redis连接状态
- 验证负载均衡配置
- 查看API Key健康状态

#### 3. 流式响应中断
**症状**: SSE连接意外断开
**解决方案**:
- 检查网络连接稳定性
- 增加超时时间配置
- 查看服务器资源使用情况

### 日志分析
```bash
# 查看应用日志
tail -f logs/application.log

# 查看错误日志
grep "ERROR" logs/application.log | tail -20

# 查看API调用日志
grep "AI_API_CALL" logs/application.log | tail -10
```

## 📊 监控与运维

### 关键指标监控
- **API调用成功率**: > 99.5%
- **平均响应时间**: < 2秒
- **API Key健康率**: > 95%
- **系统可用性**: > 99.9%

### 告警规则
- API调用失败率 > 5% 时触发告警
- 平均响应时间 > 5秒 时触发告警
- 可用API Key数量 < 2 时触发告警
- 系统内存使用率 > 85% 时触发告警

### 运维建议
1. **定期备份**: 每日备份数据库和配置文件
2. **日志轮转**: 配置日志文件自动轮转和清理
3. **性能调优**: 根据监控数据调整JVM参数和连接池配置
4. **安全更新**: 定期更新依赖库和安全补丁

---

## 📝 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持OpenAI、Anthropic、Google AI三大提供商
- 实现智能负载均衡和配置管理
- 提供完整的API接口和文档

### 后续规划
- [ ] 支持更多AI提供商
- [ ] 增加成本统计和分析功能
- [ ] 实现更智能的负载均衡算法
- [ ] 添加API使用量预测功能
- [ ] 支持自定义模型微调接口

---

**文档版本**: v1.0.0
**最后更新**: 2024-01-15
**维护团队**: AI Platform Team
