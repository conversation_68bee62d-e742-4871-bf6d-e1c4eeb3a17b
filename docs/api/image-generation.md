# OpenAI兼容图片生成API

## 概述

本服务提供完全兼容OpenAI `/v1/images/generations` 接口的图片生成功能，支持将请求转发到ChatGPT和Gemini，不支持Claude（Claude不支持图片生成）。

## 接口信息

- **端点**: `POST /v1/images/generations`
- **认证**: <PERSON><PERSON>（在Authorization头中）
- **内容类型**: `application/json`

## 支持的模型

### OpenAI模型（转发到ChatGPT）
- `dall-e-2`: DALL-E 2模型
- `dall-e-3`: DALL-E 3模型  
- `gpt-image-1`: GPT Image 1模型

### Google模型（转发到Gemini）
- `imagen-3.0-generate-001`: Imagen 3.0模型

## 请求格式

```json
{
  "model": "dall-e-3",
  "prompt": "A beautiful sunset over the ocean with sailboats",
  "n": 1,
  "size": "1024x1024",
  "quality": "standard",
  "style": "vivid",
  "response_format": "url",
  "user": "user-123456"
}
```

### 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `model` | string | 是 | 使用的模型ID |
| `prompt` | string | 是 | 图片描述提示词（最大4000字符） |
| `n` | integer | 否 | 生成图片数量（默认1，DALL-E 3只支持1） |
| `size` | string | 否 | 图片尺寸（默认1024x1024） |
| `quality` | string | 否 | 图片质量：standard/hd（仅DALL-E 3支持） |
| `style` | string | 否 | 图片风格：vivid/natural（仅DALL-E 3支持） |
| `response_format` | string | 否 | 响应格式：url/b64_json（默认url） |
| `user` | string | 否 | 用户标识（用于监控） |

### 模型限制

#### DALL-E 3
- 支持尺寸：`1024x1024`, `1024x1792`, `1792x1024`
- 只能生成1张图片（`n=1`）
- 支持`quality`和`style`参数

#### DALL-E 2  
- 支持尺寸：`256x256`, `512x512`, `1024x1024`
- 可生成1-10张图片
- 不支持`quality`和`style`参数

## 响应格式

```json
{
  "created": 1677652288,
  "data": [
    {
      "url": "https://oaidalleapiprodscus.blob.core.windows.net/private/...",
      "revised_prompt": "A beautiful sunset over the ocean with several sailboats in the distance..."
    }
  ]
}
```

### 响应字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `created` | integer | 创建时间戳 |
| `data` | array | 图片数据数组 |
| `data[].url` | string | 图片URL（response_format=url时） |
| `data[].b64_json` | string | Base64编码图片（response_format=b64_json时） |
| `data[].revised_prompt` | string | 修订后的提示词（DALL-E 3会自动优化） |

## 使用示例

### cURL示例

```bash
curl -X POST "https://your-domain.com/v1/images/generations" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "dall-e-3",
    "prompt": "A futuristic city skyline at sunset with flying cars",
    "n": 1,
    "size": "1024x1024",
    "quality": "hd",
    "style": "vivid"
  }'
```

### Python示例

```python
import requests

response = requests.post(
    "https://your-domain.com/v1/images/generations",
    headers={
        "Authorization": "Bearer your-api-key",
        "Content-Type": "application/json"
    },
    json={
        "model": "dall-e-3",
        "prompt": "A cute robot playing with a cat in a garden",
        "n": 1,
        "size": "1024x1024",
        "quality": "standard",
        "style": "natural"
    }
)

data = response.json()
image_url = data["data"][0]["url"]
print(f"Generated image: {image_url}")
```

### JavaScript示例

```javascript
const response = await fetch('https://your-domain.com/v1/images/generations', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: 'dall-e-3',
    prompt: 'A magical forest with glowing mushrooms and fairy lights',
    n: 1,
    size: '1024x1024',
    quality: 'hd',
    style: 'vivid'
  })
});

const data = await response.json();
const imageUrl = data.data[0].url;
console.log('Generated image:', imageUrl);
```

## 错误处理

### 常见错误码

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 认证失败 |
| 429 | Too Many Requests | 请求频率限制 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误响应格式

```json
{
  "error": {
    "type": "invalid_request_error",
    "message": "DALL-E 3只支持生成1张图片",
    "code": "invalid_request_error",
    "param": null,
    "details": null
  }
}
```

## 注意事项

1. **模型支持**: Claude不支持图片生成，请求会被拒绝
2. **参数验证**: 系统会自动验证模型与参数的兼容性
3. **提示词优化**: DALL-E 3会自动优化提示词，原始和修订后的提示词都会返回
4. **超时设置**: 图片生成可能需要较长时间，请设置合适的超时时间
5. **费用计算**: 图片生成会消耗相应的API配额

## 兼容性

本接口完全兼容OpenAI的图片生成API，可以直接替换OpenAI的端点使用，无需修改客户端代码。
