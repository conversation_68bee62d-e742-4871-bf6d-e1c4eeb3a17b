# 多模态支持文档

## 概述

本系统现已支持OpenAI兼容的多模态API，允许客户端发送包含文本和图片的消息。支持所有主要AI提供商（OpenAI、<PERSON>、Gemini）的多模态功能。

## 支持的格式

### 1. 纯文本消息（向后兼容）

```json
{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ]
}
```

### 2. 多模态消息（文本 + 图片）

```json
{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请描述这张图片："
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://example.com/image.jpg",
            "detail": "high"
          }
        }
      ]
    }
  ]
}
```

### 3. Base64编码图片

```json
{
  "type": "image_url",
  "image_url": {
    "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
  }
}
```

## 支持的图片格式

- **URL格式**: HTTP/HTTPS链接，支持 `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`
- **Base64格式**: `data:image/{format};base64,{data}`
- **图片质量控制**: `detail` 参数支持 `low`, `high`, `auto`

## 限制

- **图片数量**: 每个请求最多支持10张图片
- **图片大小**: 遵循各AI提供商的限制
- **支持的提供商**: OpenAI、Claude（不支持音频）、Gemini

## API提供商兼容性

| 提供商 | 多模态支持 | 图片格式 | 特殊说明 |
|--------|------------|----------|----------|
| OpenAI | ✅ 完全支持 | URL + Base64 | 标准实现 |
| Claude | ✅ 支持图片 | URL + Base64 | 不支持音频输入 |
| Gemini | ✅ 完全支持 | URL + Base64 | 原生多模态 |

## 使用示例

### Python示例

```python
import requests
import base64

# 读取本地图片并转换为base64
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

base64_image = encode_image("path/to/your/image.jpg")

response = requests.post(
    "http://your-server/v1/chat/completions",
    headers={
        "Authorization": "Bearer your-api-key",
        "Content-Type": "application/json"
    },
    json={
        "model": "gpt-4o",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "这张图片里有什么？"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                            "detail": "high"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 300
    }
)

print(response.json())
```

### JavaScript示例

```javascript
const fs = require('fs');

// 读取并编码图片
function encodeImage(imagePath) {
    const imageBuffer = fs.readFileSync(imagePath);
    return imageBuffer.toString('base64');
}

const base64Image = encodeImage('path/to/your/image.jpg');

const response = await fetch('http://your-server/v1/chat/completions', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your-api-key',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
            {
                role: 'user',
                content: [
                    {
                        type: 'text',
                        text: '分析这张图片的内容'
                    },
                    {
                        type: 'image_url',
                        image_url: {
                            url: `data:image/jpeg;base64,${base64Image}`,
                            detail: 'auto'
                        }
                    }
                ]
            }
        ],
        max_tokens: 500
    })
});

const result = await response.json();
console.log(result);
```

### cURL示例

```bash
# 使用网络图片URL
curl -X POST "http://your-server/v1/chat/completions" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "请描述这张图片"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg",
              "detail": "high"
            }
          }
        ]
      }
    ],
    "max_tokens": 300
  }'
```

## 错误处理

### 常见错误

1. **图片数量超限**
```json
{
  "error": {
    "type": "invalid_request_error",
    "message": "图片数量超过限制，最多支持10张图片"
  }
}
```

2. **无效的图片URL**
```json
{
  "error": {
    "type": "invalid_request_error", 
    "message": "第1个图片URL格式无效"
  }
}
```

3. **缺少必要字段**
```json
{
  "error": {
    "type": "invalid_request_error",
    "message": "第1个内容部分缺少type字段"
  }
}
```

## 最佳实践

### 1. 图片优化
- 使用适当的图片分辨率（避免过大的图片）
- 选择合适的`detail`参数：
  - `low`: 快速处理，成本较低
  - `high`: 详细分析，成本较高
  - `auto`: 自动选择（推荐）

### 2. 错误处理
- 始终检查API响应的错误信息
- 实现重试机制处理临时错误
- 验证图片格式和大小

### 3. 性能优化
- 缓存base64编码的图片
- 使用CDN提供图片URL
- 批量处理多张图片时注意请求大小

## 技术实现

### 架构设计
- **向后兼容**: 现有纯文本客户端无需修改
- **智能路由**: 自动检测多模态内容并路由到支持的提供商
- **格式转换**: 统一的OpenAI格式，自动适配各提供商

### 内部处理流程
1. 接收OpenAI格式的多模态请求
2. 验证内容格式和图片URL
3. 根据模型选择合适的AI提供商
4. 转换为提供商特定格式
5. 发送请求并处理响应
6. 返回标准OpenAI格式响应

## 更新日志

### v1.0.0 (2025-01-20)
- ✅ 支持OpenAI标准多模态格式
- ✅ 兼容OpenAI、Claude、Gemini
- ✅ 图片URL和Base64格式支持
- ✅ 完整的错误处理和验证
- ✅ 向后兼容纯文本消息
