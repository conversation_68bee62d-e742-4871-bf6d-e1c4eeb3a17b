# AI大模型API转发系统

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Java](https://img.shields.io/badge/Java-11+-orange.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)](https://redis.io/)

## 🚀 项目简介

AI大模型API转发系统是一个高性能、高可用的中间件服务，**完全兼容OpenAI API格式**，为所有支持自定义API的AI工具提供统一的大模型访问接口。系统支持多个AI提供商（OpenAI、Anthropic、Google AI）的API转发，具备智能负载均衡、配置管理、安全认证等核心功能。

### 🎯 兼容性支持

**即插即用，支持所有使用OpenAI兼容API的软件：**

- **🔧 IDE/编辑器**: Cursor, VS Code插件, JetBrains IDEs, Vim/Neovim, Emacs, Sublime Text
- **💬 聊天客户端**: ChatGPT Next Web, ChatBox, OpenCat, 各种第三方客户端
- **🛠️ 开发框架**: LangChain, LlamaIndex, AutoGPT, Vercel AI SDK, Streamlit, Gradio
- **📱 移动应用**: 各种第三方ChatGPT应用和AI助手
- **🏢 企业工具**: 内部聊天机器人、文档问答系统、客户服务自动化

### ✨ 核心特性

- 🔌 **OpenAI完全兼容**: 标准的`/v1/chat/completions`端点，即插即用
- 🔄 **智能负载均衡**: 多API Key轮询、健康检查、故障自动转移
- ⚙️ **灵活配置管理**: 四层配置优先级，实时生效，前端完全可控
- 🔒 **安全可靠**: API Key加密存储、多重认证、权限控制
- 🚀 **高性能**: 异步流式处理、Redis缓存、连接池复用
- 📊 **监控审计**: 请求统计、错误追踪、使用分析
- 🌐 **多提供商支持**: OpenAI、Anthropic、Google AI统一接口

### 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  AI工具/软件    │    │  OpenAI兼容层   │    │   配置管理API   │    │   内部聊天API   │
│                 │    │                 │    │                 │    │                 │
│ ├─ Cursor IDE   │◄──►│ /v1/chat/      │    │ AiConfigController│    │ AiChatController│
│ ├─ VS Code      │    │ completions     │◄──►│                 │◄──►│                 │
│ ├─ ChatGPT Web  │    │                 │    │ ├─ 用户配置     │    │ ├─ 流式聊天     │
│ ├─ LangChain    │    │ ├─ 格式转换     │    │ ├─ 密钥管理     │    │ ├─ 会话管理     │
│ ├─ AutoGPT      │    │ ├─ 认证适配     │    │ ├─ 负载均衡     │    │ └─ 模型路由     │
│ └─ 其他AI工具   │    │ ├─ 响应标准化   │    │ └─ API Key映射  │    │                 │
└─────────────────┘    │ └─ 流式处理     │    └─────────────────┘    └─────────────────┘
                       └─────────────────┘             │                       │
                                │                       ▼                       ▼
                                ▼              ┌─────────────────┐    ┌─────────────────┐
                       ┌─────────────────┐    │   配置服务层    │    │   聊天服务层    │
                       │  标准API端点    │    │                 │    │                 │
                       │                 │    │ ├─ 配置管理     │    │ ├─ 负载均衡器   │
                       │ /v1/models      │    │ ├─ 密钥加密     │    │ ├─ 模型适配器   │
                       │ /v1/chat/       │    │ ├─ 权限控制     │    │ ├─ 流式处理器   │
                       │ completions     │    │ └─ 审计日志     │    │ └─ 会话管理器   │
                       │ /v1/embeddings  │    └─────────────────┘    └─────────────────┘
                       └─────────────────┘             │                       │
                                │                       ▼                       ▼
                                └──────────────►┌─────────────────────────────────────────┐
                                               │            数据存储层                   │
                                               │                                         │
                                               │ ├─ MySQL: 配置数据、用户信息、审计日志  │
                                               │ ├─ Redis: 会话缓存、负载均衡状态       │
                                               │ └─ 配置缓存: 热点配置快速访问          │
                                               └─────────────────────────────────────────┘
                                                                │
                                                                ▼
                                               ┌─────────────────────────────────────────┐
                                               │          AI模型提供商                   │
                                               │                                         │
                                               │ ├─ OpenAI API (GPT-3.5/4, 多Key轮询)  │
                                               │ ├─ Anthropic API (Claude, 多Key轮询)   │
                                               │ └─ Google AI API (Gemini, 多Key轮询)   │
                                               └─────────────────────────────────────────┘
```

## 🛠️ 技术栈

- **后端框架**: Spring Boot 2.7.18 + Spring Security + Spring WebFlux
- **数据库**: MySQL 8.0 + Redis 6.0
- **认证授权**: JWT + Spring Security
- **API文档**: SpringDoc OpenAPI 3.0
- **监控日志**: Logback + Actuator
- **构建工具**: Maven 3.6+
- **容器化**: Docker + Docker Compose

## 📦 快速开始

### 环境要求

- Java 11+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 使用Docker Compose (推荐)

1. **克隆项目**
```bash
git clone https://github.com/your-org/ai-proxy-system.git
cd ai-proxy-system
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库密码、JWT密钥等
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **验证部署**
```bash
# 健康检查
curl http://localhost:8080/actuator/health

# API文档
open http://localhost:8080/swagger-ui.html
```

### 手动部署

详细的手动部署步骤请参考 [部署指南](DEPLOYMENT_GUIDE.md)。

## 📚 文档

- [📖 架构设计文档](AI_MODEL_PROXY_ARCHITECTURE.md)
- [🔧 API接口文档](API_REFERENCE.md)
- [🗄️ 数据库设计文档](DATABASE_SCHEMA.md)
- [🚀 部署指南](DEPLOYMENT_GUIDE.md)

## 🔧 配置说明

### 配置优先级

系统采用四层配置优先级，确保最大的灵活性：

1. **请求参数** (最高优先级) - 单次请求的临时覆盖
2. **会话配置** - 当前会话的持久化设置
3. **用户配置** - 用户的个人偏好设置
4. **系统默认配置** (最低优先级) - 全局默认值

### 主要配置项

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| model | String | gpt-3.5-turbo | 使用的AI模型 |
| temperature | Decimal | 0.7 | 生成随机性 (0.0-2.0) |
| max_tokens | Integer | 4096 | 最大生成token数 |
| stream_enabled | Boolean | true | 是否启用流式输出 |
| timeout_seconds | Integer | 30 | 请求超时时间 |

## 🔌 API接口

### OpenAI兼容API (主要接口)

```http
# 聊天完成 - 兼容所有AI工具
POST /v1/chat/completions
Authorization: Bearer sk-{userId}_{keyId}_{hash}

# 模型列表
GET /v1/models
Authorization: Bearer sk-{userId}_{keyId}_{hash}

# 文本嵌入 (计划支持)
POST /v1/embeddings
Authorization: Bearer sk-{userId}_{keyId}_{hash}
```

### 配置管理API (管理后台)

```http
# 获取用户配置
GET /api/ai/config/user

# 更新用户配置
PUT /api/ai/config/user

# 管理API密钥
GET /api/ai/config/api-keys
POST /api/ai/config/api-keys
PUT /api/ai/config/api-keys/{keyId}
DELETE /api/ai/config/api-keys/{keyId}

# 测试API密钥并获取兼容格式
POST /api/ai/config/api-keys/{keyId}/test
```

### AI工具配置示例

#### Cursor IDE
```json
{
  "openai.apiKey": "sk-1001_123_a1b2c3d4e5f6",
  "openai.apiBase": "https://your-domain.com/v1",
  "openai.model": "gpt-3.5-turbo"
}
```

#### LangChain
```python
from langchain.llms import OpenAI

llm = OpenAI(
    openai_api_key="sk-1001_123_a1b2c3d4e5f6",
    openai_api_base="https://your-domain.com/v1"
)
```

详细的API文档请参考 [API接口文档](API_REFERENCE.md)。

## 🔒 安全特性

### API Key安全
- **加密存储**: 使用AES-256加密算法存储API Key
- **访问控制**: 基于JWT的用户身份验证
- **权限隔离**: 用户只能访问自己的API Key
- **审计日志**: 记录所有API Key的使用情况

### 数据安全
- **传输加密**: 全程HTTPS通信
- **敏感信息脱敏**: 日志中自动脱敏API Key等敏感信息
- **会话管理**: 会话自动过期和清理机制
- **输入验证**: 严格的参数校验和SQL注入防护

## 📊 负载均衡

### 智能负载均衡算法

系统实现了多种负载均衡策略的组合：

1. **轮询策略 (Round Robin)**: 按顺序轮流使用API Key
2. **最少连接策略 (Least Connections)**: 优先选择当前并发请求数最少的API Key
3. **权重策略 (Weighted)**: 根据API Key的优先级分配权重
4. **健康检查 (Health Check)**: 实时监控API Key的健康状态

### 故障处理机制
- **熔断器模式**: 连续失败达到阈值时暂时停用API Key
- **重试机制**: 支持指数退避的重试策略
- **降级策略**: 在所有API Key不可用时的降级处理
- **监控告警**: 实时监控API Key状态，异常时发送告警

## 🔍 监控与运维

### 关键指标监控
- **API调用成功率**: > 99.5%
- **平均响应时间**: < 2秒
- **API Key健康率**: > 95%
- **系统可用性**: > 99.9%

### 健康检查端点
```bash
# 应用健康检查
curl http://localhost:8080/actuator/health

# AI服务健康检查
curl -H "Authorization: Bearer {token}" \
     http://localhost:8080/api/ai/chat/health

# 监控指标
curl http://localhost:8080/actuator/metrics
```

## 🧪 开发指南

### 本地开发环境

1. **启动依赖服务**
```bash
# 启动MySQL和Redis
docker-compose -f docker-compose.dev.yml up -d mysql redis
```

2. **配置开发环境**
```bash
# 复制开发配置
cp src/main/resources/application-dev.yml.example src/main/resources/application-dev.yml
# 编辑配置文件
```

3. **启动应用**
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 代码规范

- 遵循阿里巴巴Java开发手册
- 使用Lombok减少样板代码
- 统一异常处理和响应格式
- 完善的单元测试覆盖

### 测试

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify

# 生成测试报告
mvn jacoco:report
```

## 🤝 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献

### 贡献流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮件支持: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/ai-proxy-system/issues)
- 📖 文档中心: [项目文档](docs/)

## 🗺️ 路线图

### v1.1.0 (计划中)
- [ ] 支持更多AI提供商 (Cohere, Hugging Face)
- [ ] 增加成本统计和分析功能
- [ ] 实现更智能的负载均衡算法
- [ ] 添加API使用量预测功能

### v1.2.0 (计划中)
- [ ] 支持自定义模型微调接口
- [ ] 增加多租户支持
- [ ] 实现分布式部署
- [ ] 添加GraphQL API支持

## 📊 项目统计

![GitHub stars](https://img.shields.io/github/stars/your-org/ai-proxy-system?style=social)
![GitHub forks](https://img.shields.io/github/forks/your-org/ai-proxy-system?style=social)
![GitHub issues](https://img.shields.io/github/issues/your-org/ai-proxy-system)
![GitHub pull requests](https://img.shields.io/github/issues-pr/your-org/ai-proxy-system)

---

**项目维护**: AI Platform Team  
**最后更新**: 2024-01-15  
**版本**: v1.0.0
