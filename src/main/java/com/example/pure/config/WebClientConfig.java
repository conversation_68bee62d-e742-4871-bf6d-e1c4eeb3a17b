package com.example.pure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;


/**
 * WebClient配置类
 * 用于创建和配置WebClient实例
 */
@Configuration
public class WebClientConfig {

    /*  private static Long a静态类型属于那个类的对象无论new多少类都只有一个值,如果别的类赋值这个变量就会改为这个新值(1个）
        private final Long a=6 final构造函数初始化后,指向的数据为基本数据类型(int, double, boolean, char)不能修改,但是
        private final List<String> list = new ArrayList<>();list.add("Hello");这样可以，没有修改指向对象而是修改了
    *   指向对象的内容,还有Object, String, Array, 自定义的类都是这样
    */

    /**
     * 创建一个不带代理的、用于直接连接的 WebClient Bean
     * 这个 Bean 被标记为 @Primary，因此它将是默认的注入选择
     * @return 配置好的直接连接 WebClient 实例
     */
    @Bean("directWebClient") // 除了通过Bean方式注入配置获取WebClient还可以在使用的类通过new WebClientConfig().directWebClient()获取
    @Primary
    public WebClient directWebClient() {
        return WebClient.builder()
                /* codecs()方法内部源码会new一个T(这里为ClientCodecConfigurer)类型的对象,然后codecs的方法接收的参数
                 * Consumer<T>的对象有一个accept方法用户手动把spring新建的T对象进去下面代码(configurer),
                 * 在accept方法体内实现配置逻辑
                 *   ->{ configurer
                        .defaultCodecs()......},codecs接收Consumer<T>也是为了使用内部accept方法来实现配置
                 * */
                .codecs((configurer) -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(256 * 1024 * 1024)) // 增加到256MB，支持大视频文件
                .build();
    }

    /**
     * 创建一个配置了代理的 WebClient Bean
     * @return 配置好的代理连接 WebClient 实例
     */
    @Bean("proxyWebClient")
    public WebClient proxyWebClient() {
        // 1. 配置代理服务器
        HttpClient httpClient = HttpClient.create()
                .proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP)
                        .host("127.0.0.1") // 您的代理主机名
                        .port(7897));     // 您的代理端口号

        // 2. 将配置了代理的 HttpClient 应用到 WebClient
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(256 * 1024 * 1024)) // 增加到256MB，支持大视频文件
                .build();
    }
}
