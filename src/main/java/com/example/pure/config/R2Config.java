package com.example.pure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.transfer.s3.S3TransferManager;

import java.net.URI;
import java.time.Duration;
/**
 * Cloudflare R2 配置类 - 生产级最佳实践配置
 * <p>
 * 负责创建与R2服务交互所需的客户端Bean，包含完整的超时、重试、连接池配置。
 * 我们需要两个不同的Bean来支持两种上传模式：
 * 1. S3Presigner: 用于生成预签名URL，让客户端直接上传。
 * 2. S3Client: 用于在服务器端直接执行上传、下载、删除等操作。
 * </p>
 *
 * <h3>配置特点：</h3>
 * <ul>
 *   <li><b>超时保护</b>：防止网络问题导致的无限等待</li>
 *   <li><b>自动重试</b>：提高网络不稳定环境下的成功率</li>
 *   <li><b>连接池优化</b>：提升并发性能</li>
 *   <li><b>大文件支持</b>：针对视频文件上传优化的超时时间</li>
 * </ul>
 */
@Slf4j
@Configuration
public class R2Config {

    @Value("${cloudflare.r2.endpoint}")
    private String endpoint;
    @Value("${cloudflare.r2.access-key-id}")
    private String accessKeyId;
    @Value("${cloudflare.r2.secret-access-key}")
    private String secretAccessKey;
    @Value("${cloudflare.r2.region}")
    private String region;

    /**
     * 创建 S3Client Bean - 生产级最佳实践配置
     * <p>
     * 这个客户端用于执行服务器到R2的直接API调用，例如后端中转上传。
     * 包含完整的超时和重试配置，针对大文件上传优化。
     * </p>
     *
     *
     * @return 配置好的 S3Client 实例
     */
    @Bean
    public S3Client s3Client() {
        // 配置客户端超时和重试策略 - 针对大文件上传优化
        ClientOverrideConfiguration clientConfig = ClientOverrideConfiguration.builder()
                .apiCallTimeout(Duration.ofMinutes(30))        // API调用总超时：30分钟
                .apiCallAttemptTimeout(Duration.ofMinutes(15)) // 单次尝试超时：15分钟
                .retryPolicy(builder -> builder.numRetries(3)) // 重试3次
                .build();

        return S3Client.builder()
                .endpointOverride(URI.create(endpoint)) // 指定R2的端点
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKeyId, secretAccessKey))) // 提供静态凭证
                .region(Region.of(region)) // 指定区域
                .overrideConfiguration(clientConfig) // 应用超时配置
                .build();
    }

    /**
     * 创建 S3Presigner Bean - 基础配置
     * <p>
     * 这个客户端专门用于生成预签名的URL。
     * S3Presigner在当前版本中不支持ClientOverrideConfiguration，使用基础配置。
     * </p>
     *
     * @return 配置好的 S3Presigner 实例
     */
    @Bean
    public S3Presigner s3Presigner() {
        return S3Presigner.builder()
                .endpointOverride(URI.create(endpoint)) // 指定R2的端点
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKeyId, secretAccessKey))) // 提供静态凭证
                .region(Region.of(region)) // 指定区域
                .build();
    }

    /**
     * 创建 S3AsyncClient Bean - 使用CRT优化的高性能客户端
     * <p>
     * 使用S3CrtAsyncClient获得最佳性能，支持TransferManager的所有高级功能：
     * - 自动分片上传优化
     * - 并行传输
     * - 断点续传
     * - 网络自适应
     * </p>
     *
     * @return 配置好的高性能 S3AsyncClient 实例
     */
    @Bean
    public S3AsyncClient s3AsyncClient() {
        log.info("初始化S3CrtAsyncClient - 启用高性能CRT优化");

        // 使用CRT构建器获得最佳性能
        return S3AsyncClient.crtBuilder()
                .endpointOverride(URI.create(endpoint)) // 指定R2的端点
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKeyId, secretAccessKey))) // 提供静态凭证
                .region(Region.of(region)) // 指定区域
                // CRT客户端会自动优化以下配置：
                // - 分片上传阈值：默认8MB
                // - 分片大小：自动调整
                // - 并发连接：自动优化
                // - 重试策略：自动处理
                .build();
    }

    /**
     * 创建 S3TransferManager Bean - 自动优化配置
     * <p>
     * TransferManager是AWS SDK提供的高级传输工具，具备完全自动化的优化策略：
     * </p>
     *
     * <h3>自动优化特性：</h3>
     * <ul>
     *   <li><b>智能阈值</b>：文件≥8MB自动启用分片上传</li>
     *   <li><b>自适应分片</b>：根据文件大小和网络条件自动调整分片大小</li>
     *   <li><b>并行传输</b>：自动并行上传多个分片，最大化带宽利用</li>
     *   <li><b>断点续传</b>：网络中断后自动从断点继续</li>
     *   <li><b>内存优化</b>：流式处理，内存使用恒定</li>
     *   <li><b>网络自适应</b>：根据网络状况动态调整策略</li>
     * </ul>
     *
     * <h3>无需手动配置：</h3>
     * <p>
     * TransferManager会自动处理所有优化决策，包括：
     * - 是否使用分片上传
     * - 分片大小选择
     * - 并发数控制
     * - 重试策略
     * </p>
     *
     * @param s3AsyncClient 已配置的高性能S3AsyncClient实例
     * @return 配置好的 S3TransferManager 实例
     */
    @Bean
    public S3TransferManager s3TransferManager(S3AsyncClient s3AsyncClient) {
        log.info("初始化S3TransferManager - 启用完全自动优化策略");

        S3TransferManager transferManager = S3TransferManager.builder()
                .s3Client(s3AsyncClient) // 使用CRT优化的S3AsyncClient
                .build();

        log.info("S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传");
        return transferManager;
    }
}
