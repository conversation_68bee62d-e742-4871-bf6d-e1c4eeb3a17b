package com.example.pure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * AI大模型API转发系统配置类
 * <p>
 * 读取application.yml中的AI相关配置
 * </p>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai")
public class AiConfig {

    /**
     * 安全配置
     */
    private Security security = new Security();

    /**
     * 加密配置
     */
    private Encryption encryption = new Encryption();

    /**
     * 提供商配置
     */
    private Map<String, Provider> providers;

    /**
     * 负载均衡配置
     */
    private LoadBalance loadBalance = new LoadBalance();

    /**
     * 默认配置
     */
    private Defaults defaults = new Defaults();

    /**
     * 安全配置
     */
    @Data
    public static class Security {
        /**
         * 最大超时限制（毫秒）
         */
        private Long maxTimeout = 300000L;

        /**
         * 最大token硬限制
         */
        private Integer maxTokensLimit = 32768;

        /**
         * 速率限制配置
         */
        private RateLimit rateLimit = new RateLimit();

        @Data
        public static class RateLimit {
            /**
             * 每分钟最大请求数
             */
            private Integer requestsPerMinute = 60;
        }
    }

    /**
     * 加密配置
     */
    @Data
    public static class Encryption {
        /**
         * API密钥加密密钥
         */
        private String secret = "default-secret-key-for-ai-api-encryption";

        /**
         * API Key轮换周期（天）
         */
        private Integer keyRotationDays = 30;
    }

    /**
     * 提供商配置
     */
    @Data
    public static class Provider {
        /**
         * 基础URL
         */
        private String baseUrl;

        /**
         * 支持的模型列表
         */
        private List<String> models;

        /**
         * 请求超时时间（毫秒）
         */
        private Long timeout = 60000L;

        /**
         * 是否使用 OpenAI 兼容格式
         * <p>
         * 当设置为 true 时，将使用 OpenAI 兼容的 API 格式调用该提供商
         * 适用于 Claude (Anthropic) 和 Gemini (Google) 的 OpenAI 兼容端点
         * </p>
         */
        private Boolean openaiCompatible = false;
    }

    /**
     * 负载均衡配置
     */
    @Data
    public static class LoadBalance {
        /**
         * 最大错误次数阈值
         */
        private Integer maxErrorCount = 5;

        /**
         * 最大错误率阈值
         */
        private Double maxErrorRate = 0.1;

        /**
         * 最大并发请求数
         */
        private Integer maxConcurrentRequests = 10;

        /**
         * 健康检查间隔（秒）
         */
        private Integer healthCheckInterval = 300;
    }

    /**
     * 默认配置
     */
    @Data
    public static class Defaults {
        /**
         * 默认模型
         */
        private String model = "gpt-3.5-turbo";

        /**
         * 默认温度参数
         */
        private Double temperature = 0.7;

        /**
         * 默认最大token数
         */
        private Integer maxTokens = 8192;

        /**
         * 默认top_p参数
         */
        private Double topP = 1.0;

        /**
         * 默认是否启用流式输出
         */
        private Boolean streamEnabled = true;

        /**
         * 默认超时时间（秒）
         */
        private Integer timeoutSeconds = 30;
    }

    // ========================
    // 便捷方法
    // ========================

    /**
     * 获取OpenAI提供商配置
     */
    public Provider getOpenAiProvider() {
        return providers != null ? providers.get("openai") : null;
    }

    /**
     * 获取Anthropic提供商配置
     */
    public Provider getAnthropicProvider() {
        return providers != null ? providers.get("anthropic") : null;
    }

    /**
     * 获取Google AI提供商配置
     */
    public Provider getGoogleProvider() {
        return providers != null ? providers.get("google") : null;
    }

    /**
     * 检查模型是否被支持
     */
    public boolean isModelSupported(String model) {
        if (providers == null) {
            return false;
        }

        return providers.values().stream()
                .anyMatch(provider -> provider.getModels() != null && 
                         provider.getModels().contains(model));
    }

    /**
     * 根据模型名称获取提供商名称
     */
    public String getProviderByModel(String model) {
        if (providers == null) {
            return null;
        }

        return providers.entrySet().stream()
                .filter(entry -> entry.getValue().getModels() != null && 
                               entry.getValue().getModels().contains(model))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }
}
