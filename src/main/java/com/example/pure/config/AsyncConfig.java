package com.example.pure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步处理配置类
 * <p>
 * 配置异步任务执行器，包括：
 * - 文件处理线程池配置
 * - 通用异步任务线程池配置
 * - 异常处理配置
 * </p>
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {

    /**
     * CPU密集型任务线程池
     * <p>
     * 专门用于处理CPU密集型操作的线程池，如文件上传处理、数据计算等
     * 线程池参数经过优化，适合CPU密集型任务
     * </p>
     *
     * @return 配置好的线程池任务执行器
     */
    @Bean(name = "cpuIntensiveTaskExecutor")
    public AsyncTaskExecutor cpuIntensiveTaskExecutor() {
        log.info("创建CPU密集型异步任务线程池");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数 - 始终保持运行的线程数量,CPU密集型最佳实践为当前核心数或加1为可能存在的IO操作保留一个线程
        executor.setCorePoolSize(6);
        // 最大线程数 - 线程池最大容量，最佳实践为当前核心线程数2倍
        executor.setMaxPoolSize(12);
        // 队列容量 - 当核心线程都在运行时，新任务会进入队列等待
        executor.setQueueCapacity(100);
        // 线程名前缀 - 便于调试和监控
        executor.setThreadNamePrefix("cpuIntensiveTask-async-");
        // 拒绝策略 - 当队列满且所有线程都在运行时的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 空闲线程存活时间 - 超过核心线程数的线程在空闲一段时间后会被销毁
        executor.setKeepAliveSeconds(60);
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池关闭的超时时间
        executor.setAwaitTerminationSeconds(60);
        // 设置异步任务超时时间为30分钟
        executor.setAwaitTerminationMillis(30 * 60 * 1000L);
        // 初始化线程池
        executor.initialize();

        // 使用 DelegatingSecurityContextAsyncTaskExecutor 包装以传播安全上下文
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }
    @Bean(name = "ioIntensiveTaskExecutor")
    public AsyncTaskExecutor ioTaskExecutor() {
        // IO操作有硬盘传输、网络传输、DB查询，下载和上传较多的也用IO操作因为CPU只是调用下载，传输是IO操作
        log.info("创建文件操作异步任务线程池");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        /* 核心线程数 - 始终保持运行的线程数量，io密集型最佳实践为2倍到4倍的核心数，设置为多倍是因为IO操作时会阻塞线程
            比如12线程正在cpu发出IO操作然后阻塞，然后又来了11个需要异步的操作，如果设置12线程会阻塞等刚刚的IO操作完成才
            执行下面的11个请求，设置为多倍可以在他IO阻塞时候把这个线程释放出来执行下面的11个请求
        * */
        executor.setCorePoolSize(26);
        // 最大线程数 - 线程池最大容量，最佳实践为线程数 = 核心数 × (1 + I/O等待时间/CPU处理时间)
        executor.setMaxPoolSize(127);
        // 队列容量 - 当核心线程都在运行时，新任务会进入队列等待
        executor.setQueueCapacity(100);
        // 线程名前缀 - 便于调试和监控
        executor.setThreadNamePrefix("ioIntensiveTask-async-");
        // 拒绝策略 - 当队列满且所有线程都在运行时的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 空闲线程存活时间 - 超过核心线程数的线程在空闲一段时间后会被销毁
        executor.setKeepAliveSeconds(60);
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池关闭的超时时间
        executor.setAwaitTerminationSeconds(60);
        // 设置异步任务超时时间为30分钟
        executor.setAwaitTerminationMillis(30 * 60 * 1000L);
        // 初始化线程池
        executor.initialize();

        // 使用 DelegatingSecurityContextAsyncTaskExecutor 包装以传播安全上下文
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }

    /**
     * 通用异步任务线程池（默认线程池）
     * <p>
     * 用于处理通用的异步任务
     * 如果@Async注解未指定执行器，则使用此默认线程池
     * </p>
     *
     * @return 配置好的线程池任务执行器
     */
    @Bean("taskExecutor")
    @Override
    public AsyncTaskExecutor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 设置核心线程数
        executor.setCorePoolSize(6);

        // 设置最大线程数
        executor.setMaxPoolSize(10);

        // 设置队列容量
        executor.setQueueCapacity(50);

        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);

        // 设置线程名前缀
        executor.setThreadNamePrefix("async-");

        // 设置拒绝策略：由调用线程处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化线程池
        executor.initialize();

        log.info("通用异步任务线程池初始化完成 - 核心线程数: {}, 最大线程数: {}",
            executor.getCorePoolSize(), executor.getMaxPoolSize());

        // 使用 DelegatingSecurityContextAsyncTaskExecutor 包装以传播安全上下文
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }

    /**
     * SSE 专用线程池
     * <p>
     * 专门用于处理 Server-Sent Events 相关的异步任务
     * 针对 SSE 长连接特性进行优化配置
     * </p>
     *
     * @return 配置好的 SSE 线程池任务执行器
     */
    @Bean(name = "sseTaskExecutor")
    public AsyncTaskExecutor sseTaskExecutor() {
        log.info("创建 SSE 专用异步任务线程池");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数 - SSE 连接通常较多，设置较大的核心线程数
        executor.setCorePoolSize(10);
        // 最大线程数 - 支持更多并发 SSE 连接
        executor.setMaxPoolSize(50);
        // 队列容量 - SSE 任务通常执行时间较短，设置适中的队列
        executor.setQueueCapacity(200);
        // 线程名前缀 - 便于调试和监控
        executor.setThreadNamePrefix("sse-async-");
        // 拒绝策略 - 使用调用者运行策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 空闲线程存活时间 - SSE 连接可能持续时间较长，设置较长的存活时间
        executor.setKeepAliveSeconds(120);
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池关闭的超时时间
        executor.setAwaitTerminationSeconds(60);
        // 初始化线程池
        executor.initialize();

        // 使用 DelegatingSecurityContextAsyncTaskExecutor 包装以传播安全上下文
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }

    /**
     * 异步任务异常处理器
     * <p>
     * 当异步任务抛出未捕获的异常时，该处理器将被调用
     * </p>
     *
     * @return 异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("执行异步任务出现未捕获异常 - 方法: {}, 参数: {}", method.getName(), params, ex);
        };
    }
}
