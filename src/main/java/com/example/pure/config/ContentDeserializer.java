package com.example.pure.config;

import com.example.pure.model.dto.request.openai.OpenAiChatRequest.ContentPart;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * 内容反序列化器
 * <p>
 * 支持 OpenAI 消息内容的两种格式：
 * 1. 字符串格式：content: "Hello"
 * 2. 数组格式：content: [{"type": "text", "text": "Hello"}]
 * </p>
 */
@Slf4j
public class ContentDeserializer extends JsonDeserializer<List<ContentPart>> {
    
    @Override
    public List<ContentPart> deserialize(JsonParser parser, DeserializationContext context) 
            throws IOException {
        
        JsonToken token = parser.getCurrentToken();
        
        if (token == JsonToken.VALUE_STRING) {
            // 字符串格式：转换为文本类型的 ContentPart
            String textContent = parser.getValueAsString();
            log.debug("反序列化字符串格式内容: {}", textContent);
            
            ContentPart textPart = new ContentPart();
            textPart.setType("text");
            textPart.setText(textContent);
            
            return List.of(textPart);
            
        } else if (token == JsonToken.START_ARRAY) {
            // 数组格式：正常反序列化为 ContentPart 列表
            log.debug("反序列化数组格式内容");
            
            ObjectMapper mapper = (ObjectMapper) parser.getCodec();
            return mapper.readValue(parser, 
                mapper.getTypeFactory().constructCollectionType(List.class, ContentPart.class));
                
        } else {
            // 不支持的格式
            throw new IOException("Content must be either string or array, but got: " + token);
        }
    }
}
