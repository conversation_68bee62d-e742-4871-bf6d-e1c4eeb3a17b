package com.example.pure.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 安全相关常量类
 */
public final class SecurityConstants {


    public static final Long ADMIN_ID = 1L;

    /**
     * JWT相关常量
     */
    public static final String TOKEN_PREFIX = "Bearer ";
    public static final String HEADER_STRING = "Authorization";
    /**
     * 角色常量
     */
    public static final String ROLE_USER = "ROLE_USER";
    public static final String ROLE_ADMIN = "ROLE_ADMIN";

    // 角色前缀
    public static final String ROLE_PREFIX = "ROLE_";

    /**
     * Redis中用户设备信息的key前缀
     */
    public static final String USER_DEVICES_KEY_PREFIX = "user:devices:";

    /**
     * Redis中验证码的key前缀
     */
    public static final String EMAIL_CODE_PREFIX = "email:code:";


    /**
     * 普通 URL
     */
    public static final String[] PUBLIC_URLS = {
            "/api/auth/login",     // 登录接口
            "/api/user/password",   //修改密码
            "/api/user/createUser",  // 注册接口
            "/api/auth/refresh/**",  // 刷新令牌接口
            "/api/auth/captcha",  //获取验证码
            "/api/public/**",       // 其他公开接口
            "/api/qrcode/**",      // 二维码生成接口
            "/api/qrlogin/**",  // 二维码登录生成的相关接口
            "/ws/**",              // WebSocket端点
            "/ws-raw/**",
            "/oauth/render/**",           // 第三方授权登录
            "/oauth/callback/**",       // 第三方授权登录回调
            "/oauth/refresh/**",       // 第三方登录的刷新令牌接口
            "/api/verification/**",  //发送邮箱验证码
            "/api/user/update/password",//找回密码
            "/api/user/createUser", // 创建用户
            "/api/videoUrl/**",
            "/video/**",           //视频在线播放
            "/video-legacy/**",    //视频播放接口
            "/image/**",            //图片访问接口
            "/image-legacy/**",     //图片访问接口
            "/download/**",      //下载文件接口
            "/download-legacy/**", //下载视频接口
            "/api/ai-chat/**",     //AI聊天接口
            "/api/ai",
            "/v1/**",              //OpenAI兼容API接口
            "/favicon.ico",        // 网站图标
            "/error",             // 错误页面
            "/*.html",            // HTML文件
            "/**/*.html",         // HTML文件
            "/**/*.css",          // CSS文件
            "/**/*.js",           // JS文件
            "/static/**"          // 静态资源

    };
    /**
     * Swagger文档 URL
     */
    public static final String[] SWAGGER_URLS = {
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/doc.html",
            "/webjars/**",
            "/openapi.json",
            "/v2/**"
    };

    public static String SCANNED = "Scanned";
    /**
     * 用户权限接口 URL
     */
    public static final String[] USER_URLS = {
            "/api/user/get/**",        // 用户相关接口
            "/api/profile/**",         // 个人资料接口
            "/api/ai/config/**"        // AI配置管理接口
    };
    /**
     * 管理员权限接口 URL
     */
    public static final String[] ADMIN_URLS = {
            "/api/admin/**",       // 管理员接口
            "/api/management/**"   // 系统管理接口
    };


    public static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    public static final String RANDEM="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    public static final String GITHUB_CLIENTID="********************";
    public static final String GITHUB_CLIENTSECRET="95fccebfc1bd36664068cee13485a54cbe29034f";
    public static final String GITEE_CLENTID="f669b4c8f06bc432ff0e0e8925b65eeee7486c9105b3101c956f327bbcf5957c";
    public static final String GITEE_CLIENTSECRET="8831b185340965218b3697fa1746715f77fe8a6a04425ef5b2daccdc106d16a3";

    private SecurityConstants() {
        throw new IllegalStateException("Utility class");
    }

    // 允许上传的MIME类型
    public static final Set<String> ALLOWED_MIME_TYPES = new HashSet<>(Arrays.asList(
            "image/jpeg", "image/png", "image/gif", "image/bmp", "image/svg+xml",
            "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain", "text/html", "text/css", "text/javascript",
            "application/json", "application/xml", "application/zip", "application/x-rar-compressed",
            "video/mp4", "video/mpeg", "video/quicktime", "audio/mpeg", "audio/wav"
    ));

    /**
     * 支持的图片文件扩展名类型
     */
    public static final Set<String> IMAGE_TYPE = new HashSet<>(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"
    ));

    public static final String GOOGLE_VERIFY_URL = "https://www.recaptcha.net/recaptcha/api/siteverify";

    // ==================== 视频缓存相关常量 ====================

    /**
     * 视频播放URL缓存key前缀 - 缓存3小时
     */
    public static final String VIDEO_PLAY_URL_CACHE_PREFIX = "video:play_url:";

    /**
     * 雪碧图URL缓存key前缀 - 缓存3小时
     */
    public static final String SPRITE_SHEET_URL_CACHE_PREFIX = "video:sprite_url:";

    /**
     * 视频信息缓存名称（用于@Cacheable注解）
     */
    public static final String VIDEO_INFO_CACHE_NAME = "videoInfo";

    /**
     * 临时URL缓存时间（秒）- 3小时
     */
    public static final int TEMP_URL_CACHE_SECONDS = 3 * 60 * 60;

    public static final Long SEVEN_DAY =604800L;

    // ==================== 二维码登录相关常量 ====================

    /**
     * 二维码登录协议前缀
     * <p>
     * 用于生成二维码内容，格式：qrlogin://qr-id?type=sse
     * </p>
     */
    public static final String QR_LOGIN_SCHEME = "qrlogin://";

    /**
     * SSE 连接超时时间（毫秒）- 150秒
     * <p>
     * 设置为比二维码有效期稍长，确保在二维码有效期内连接不会超时
     * 二维码有效期：120秒，SSE超时：150秒
     * </p>
     */
    public static final long SSE_TIMEOUT = 150000L;

    /**
     * 二维码登录 Redis 键前缀
     * <p>
     * 用于在 Redis 中存储二维码登录信息
     * 完整键格式：qrlogin:qr-id
     * </p>
     */
    public static final String QR_LOGIN_REDIS_PREFIX = "qrlogin:";

    /**
     * 默认通信类型
     * <p>
     * 当无法解析通信类型时使用的默认值
     * </p>
     */
    public static final String DEFAULT_COMMUNICATION_TYPE = "WEBSOCKET";
}
