package com.example.pure.actuator;

import com.example.pure.mapper.primary.UserMapper;

import com.example.pure.model.dto.response.user.UserDTO;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Supplier;

/**
 * 用户相关指标收集器
 * 实现MeterBinder接口，用于注册用户统计指标
 * 可通过/actuator/metrics/app.users.total访问
 *
 * <AUTHOR> Name
 * @since 1.0.0
 */
@Component
public class UserMetrics implements MeterBinder {

    private final UserMapper userMapper;

    public UserMetrics(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    /**
     * 绑定用户统计指标到注册表
     * 实现MeterBinder接口的方法
     * 注册一个测量用户总数的计量器
     *
     * @param registry 指标注册表
     */
    @Override
    public void bindTo(MeterRegistry registry) {
        // 注册用户总数指标
        Gauge.builder("app.users.total",
            new Supplier<Number>() {
                @Override
                public Number get() {
                    List<UserDTO> users = userMapper.findAll();
                    return users.size();
                }
            })
            .description("Total number of users in the system")
            .register(registry);
    }
}
