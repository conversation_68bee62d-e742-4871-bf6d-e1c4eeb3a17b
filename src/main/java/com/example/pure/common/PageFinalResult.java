package com.example.pure.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List; // Moved import to the correct location

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageFinalResult<T> {
    List<T> list;
    PageResult pageResult;

    /**
     * 通用分页查询结果封装类
     *
     * <p>用于封装任何类型的分页查询结果，包含当前页数据列表以及
     * 完整的的分页元数据（总记录数、总页数、当前页码、每页大小）。
     * </p>
     *
     * <p>此类设计为通用目的，不依赖于特定的分页插件（如PageHelper）。
     * 在手动执行分页查询（例如，先查询总数，再查询当前页数据）后，
     * 可以使用本类来构建标准化的分页响应。
     * </p>
     *
     * <p>示例 (手动分页场景):
     * <pre>{@code
     * // 1. 查询总记录数
     * long totalItems = userMapper.countUsers(keyword); // Changed to long for consistency
     *
     * // 2. 查询当前页数据
     * int offset = pageRequest.getOffset();
     * int limit = pageRequest.getPageSize();
     * List<UserDTO> currentPageData = userMapper.findUsersByPage(offset, limit, keyword);
     *
     * // 3. 构建分页结果
     * PageResult<UserDTO> result = PageResult.of(currentPageData,
     *                                             pageRequest.getPageNum(),
     *                                             pageRequest.getPageSize(),
     *                                             totalItems);
     * }
     * </pre>
     * </p>
     *
     * @param
     * <AUTHOR>
     * @since 1.0.0
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PageResult {

        /**
         * 总记录数
         * <p>符合查询条件的总项目数量。</p>
         */
        private long total;

        /**
         * 当前页码
         * <p>从1开始计数。</p>
         */
        private int pageNum;

        /**
         * 每页显示条数
         * <p>查询时设定的每页期望返回的数据项数量。</p>
         */
        private int pageSize;

        /**
         * 总页数
         * <p>根据总记录数和每页大小计算得出的总页码数量。</p>
         */
        private int pages;

        /**
         * 创建分页结果对象的静态工厂方法。
         *
         * <p>推荐使用此方法来构建PageResult实例，它会自动计算总页数。
         * 适用于手动执行分页查询后封装结果的场景。
         * </p>
         *
         * @param pageNum  当前页码 (从1开始)
         * @param pageSize 每页大小
         * @param total    总记录数
         * @return 封装好的分页结果对象
         */
        public static PageResult of (int pageNum, int pageSize, long total) {
            // 计算总页数，确保除数为正数，并向上取整
            int pages = (pageSize <= 0) ? 0 : (int) Math.ceil((double) total / pageSize);
            if (pages == 0 && total > 0) { // 至少有一页，即使pageSize无效或total>0但pageSize很大
                 pages = 1;
            }

            return PageResult.builder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .total(total)
                    .pages(pages)
                    .build();
        }
    }
}
