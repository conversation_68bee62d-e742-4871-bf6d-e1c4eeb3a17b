package com.example.pure.security;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.mapper.primary.RoleMapper;
import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.model.entity.Role;
import com.example.pure.model.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义UserDetailsService实现，调用loadUserByUsername方法就能认证用户并返回包含用户认证后的数据的UserDetails类
 * 用于Spring Security的用户认证，用户安全信息的逻辑
 */
@Slf4j
@Service//Spring自动创建这个类的实例，并管理。就能在其他类的构造方法自动注入这个类
public class CustomUserDetailsService implements UserDetailsService {

    private final UserMapper userMapper;
    private final RoleMapper roleMapper;

    public CustomUserDetailsService(
            UserMapper userMapper,
            RoleMapper roleMapper
    ) {
        this.userMapper = userMapper;
        this.roleMapper = roleMapper;
    }

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("正在加载用户信息: {}", username);
        /*
        config.work（()->{ System.out.println("config") }）这表示work方法（URLInterface url）需要一个函数式接口的实现，
        等于
        config.work(new URLInterface(){   nwe接口后{}匿名内部实现类直接实现接口
        @Override
        public void play(){
        System.out.println("config");
        }
        })
        也就是work方法里的参数是一个函数式接口(URLInterface url)这URLInterface里只有一个唯一的方法也就是play(),
        所以play方法的实现就是System.out.println("config")
        ()表示参数，->{}把参数传入并运行主要代码
        .map(() -> {}) 和 .map(role -> new SimpleGrantedAuthority(role.getName()))一样
        表达式主体 (Expression body):
        只有 一个表达式。
        不需要 花括号 {}。
        不需要 return 关键字 (表达式的值会被 隐式返回)。
        语法更简洁。
        语句主体 (Statement body):
        包含 多个语句，或者即使只有一个语句，但你需要更复杂的逻辑。
        需要 花括号 {} 包裹。
        如果要返回值，必须 使用 return 关键字显式返回。
            * 返回的类型为对象的集合，集合里的元素是任何类型的权限（GrantedAuthority 接口）类型的对象
            * 获取用户角色转换成流，才能使用.map方法映射（对流中的每个元素重复运行同样的操作）（将一个值转换成另一个值）
            * 映射每个流中的元素重复运行同样的操作，把流中的元素传入括号变成role对象，然后给新建的类的对象当做构造方法的参数
            * 把流中元素收集起来（把收集到的元素放到新的List中）
            */
            //从数据库获取用户的角色，检查用户权限来是否能运行API
         /*
          获取用户角色并转换为GrantedAuthority集合
         Lambda写法：
         Collection<? extends GrantedAuthority> authorities = userRoleService.getUserRoles(user.getId())
             .stream()
             .map(role -> new SimpleGrantedAuthority(role.getName()))
            .collect(Collectors.toList());
        */
        // 传统写法：
        //查询到的用户角色如果有多个，每一行映射存放到一个Role的实例对象中，roles也就是拥有多个对象（第一个对象name为ROLE_USER,第二个对象name为ROLE_ADMIN）
        // 从数据库获取用户信息

        User user =userMapper.findByUserWithPasswordByUsername(username);
        log.info("id: {},password: {}",user.getId(),user.getPassword());
        if (user == null) {
            log.warn("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        // 检查用户是否被封禁
        if (!ObjectUtils.isEmpty(user.getBanExpiresAt()) && user.getBanExpiresAt().isAfter(Instant.now())) {
            Duration remaining = Duration.between(Instant.now(), user.getBanExpiresAt());
            long remainingDays = remaining.toDays();
            long remainingHours = remaining.toHours() % 24;

            throw new LockedException(
                String.format("账号已被封禁，封禁截止时间：%s，剩余时间：%d天%d小时",
                             user.getBanExpiresAt(), remainingDays, remainingHours)
            );
        }

        // 获取用户角色
        List<Role> roles = roleMapper.findByUserId(user.getId());
        List<GrantedAuthority> authorities = new ArrayList<>();
        List<SimpleGrantedAuthority> newAuthorities = roles.stream() // 1. 创建 Stream
                .filter(Objects::nonNull) // 2. 过滤掉 null 的 Role 对象
                .map(Role::getName)       // 3. 获取 Role 的 name 属性 (方法引用)
                .filter(name -> name != null && !name.trim().isEmpty()) // 4. 过滤掉 null 或空白的 name
                .map(String::trim)        // 5. 去除 name 的前后空白
                .map(name -> {
                    // 6. 统一格式化逻辑
                    if (name.toUpperCase().startsWith(SecurityConstants.ROLE_PREFIX)) {
                        // 如果已经有 ROLE_ 前缀 (忽略大小写), 只需确保 ROLE_ 部分正确，后面转大写
                        return SecurityConstants.ROLE_PREFIX + name.substring(SecurityConstants.ROLE_PREFIX.length()).toUpperCase();
                    } else {
                        // 如果没有前缀，添加前缀并转大写
                        return SecurityConstants.ROLE_PREFIX + name.toUpperCase();
                    }
                })
                .distinct() // 7. (可选) 去除重复的角色名，避免重复授权
                .map(SimpleGrantedAuthority::new) // 8. 将格式化后的字符串映射为 SimpleGrantedAuthority 对象
                .collect(Collectors.toList()); // 9. 收集结果到一个新的 List


        authorities.addAll(newAuthorities); // 10. 将新生成的权限添加到已有的权限集合中
        log.debug("用户 {} 的角色信息已加载, 角色数量: {}", username, newAuthorities.size());

        // 创建并返回CustomUserDetails实例
        return new CustomUserDetails(user, authorities);
    }
}
