package com.example.pure.model.adapter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API密钥验证结果
 * <p>
 * 用于返回API密钥验证的结果信息
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "API密钥验证结果")
public class ApiKeyValidationResult {
    
    /**
     * 是否有效
     */
    @Schema(description = "是否有效", example = "true")
    private boolean valid;
    
    /**
     * 验证消息
     */
    @Schema(description = "验证消息", example = "API密钥有效")
    private String message;
    
    /**
     * 响应时间（毫秒）
     */
    @Schema(description = "响应时间（毫秒）", example = "150")
    private Long responseTime;
}
