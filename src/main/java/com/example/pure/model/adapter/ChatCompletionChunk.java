package com.example.pure.model.adapter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 聊天完成响应块（流式）
 * <p>
 * 用于模型适配服务的流式响应格式
 * </p>
 */
@Data
@Schema(description = "聊天完成响应块")
public class ChatCompletionChunk {

    /**
     * 响应ID
     */
    @Schema(description = "响应ID", example = "chatcmpl-123")
    private String id;

    /**
     * http请求后获取的结果为JSON string对象类型
     */
    @Schema(description = "对象类型", example = "chat.completion.chunk")
    private String object;

    /**
     * 创建时间戳
     */
    @Schema(description = "创建时间戳", example = "1677652288")
    private Long created;

    /**
     * 使用的模型
     */
    @Schema(description = "使用的模型", example = "gpt-3.5-turbo")
    private String model;

    /**
     * 选择列表
     */
    @Schema(description = "选择列表")
    private List<Choice> choices;

    /**
     * 流式选择项
     */
    @Data
    @Schema(description = "流式选择项")
    public static class Choice {
        /**
         * 选择索引
         */
        @Schema(description = "选择索引", example = "0")
        private Integer index;

        /**
         * 增量内容
         */
        @Schema(description = "增量内容")
        private Delta delta;

        /**
         * 结束原因
         */
        @Schema(description = "结束原因", example = "stop", allowableValues = {"stop", "length", "content_filter"})
        private String finishReason;
    }

    /**
     * 增量内容
     */
    @Data
    @Schema(description = "增量内容")
    public static class Delta {
        /**
         * 内容片段
         */
        @Schema(description = "内容片段", example = "Hello")
        private String content;
    }
}
