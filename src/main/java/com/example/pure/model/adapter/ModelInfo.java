package com.example.pure.model.adapter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模型信息
 * <p>
 * 用于描述AI模型的基本信息
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "模型信息")
public class ModelInfo {
    
    /**
     * 模型ID
     */
    @Schema(description = "模型ID", example = "gpt-3.5-turbo")
    private String id;
    
    /**
     * 模型名称
     */
    @Schema(description = "模型名称", example = "GPT-3.5 Turbo")
    private String name;
    
    /**
     * 模型描述
     */
    @Schema(description = "模型描述", example = "Fast and efficient language model")
    private String description;
    
    /**
     * 提供商
     */
    @Schema(description = "提供商", example = "OpenAI")
    private String provider;
    
    /**
     * 是否支持流式输出
     */
    @Schema(description = "是否支持流式输出", example = "true")
    private Boolean supportsStreaming;
    
    /**
     * 最大上下文长度
     */
    @Schema(description = "最大上下文长度", example = "4096")
    private Integer maxContextLength;

    /**
     * 兼容原有构造函数的构造方法
     * @param id 模型ID
     * @param name 模型名称
     * @param maxTokens 最大token数（兼容参数）
     * @param description 模型描述
     */
    public ModelInfo(String id, String name, Integer maxTokens, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.maxContextLength = maxTokens;
        this.supportsStreaming = true; // 默认支持流式
        this.provider = "Unknown"; // 默认提供商
    }
}
