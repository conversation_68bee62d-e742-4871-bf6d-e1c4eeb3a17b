package com.example.pure.model.adapter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 聊天完成响应（非流式）
 * <p>
 * 用于模型适配服务的完整响应格式
 * </p>
 */
@Data
@Schema(description = "聊天完成响应")
public class ChatCompletionResponse {

    /**
     * 响应ID
     */
    @Schema(description = "响应ID", example = "chatcmpl-123")
    private String id;

    /**
     * 对象类型
     */
    @Schema(description = "对象类型", example = "chat.completion")
    private String object;

    /**
     * 创建时间戳
     */
    @Schema(description = "创建时间戳", example = "1677652288")
    private Long created;

    /**
     * 使用的模型
     */
    @Schema(description = "使用的模型", example = "gpt-3.5-turbo")
    private String model;

    /**
     * 选择列表
     */
    @Schema(description = "选择列表")
    private List<Choice> choices;

    /**
     * 使用统计
     */
    @Schema(description = "使用统计")
    private Usage usage;

    /**
     * 选择项
     */
    @Data
    @Schema(description = "选择项")
    public static class Choice {
        /**
         * 选择索引
         */
        @Schema(description = "选择索引", example = "0")
        private Integer index;

        /**
         * 消息内容
         */
        @Schema(description = "消息内容")
        private ChatCompletionRequest.ChatMessage message;

        /**
         * 结束原因
         */
        @Schema(description = "结束原因", example = "stop", allowableValues = {"stop", "length", "content_filter"})
        private String finishReason;
    }

    /**
     * 使用统计
     */
    @Data
    @Schema(description = "使用统计")
    public static class Usage {
        /**
         * 提示token数
         */
        @Schema(description = "提示token数", example = "10")
        private Integer promptTokens;

        /**
         * 完成token数
         */
        @Schema(description = "完成token数", example = "20")
        private Integer completionTokens;

        /**
         * 总token数
         */
        @Schema(description = "总token数", example = "30")
        private Integer totalTokens;
    }
}
