package com.example.pure.model.adapter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聊天完成请求
 * <p>
 * 用于模型适配服务的统一请求格式
 * </p>
 */
@Data
@Schema(description = "聊天完成请求")
public class ChatCompletionRequest {

    /**
     * 模型名称
     */
    @Schema(description = "模型名称", example = "gpt-3.5-turbo")
    private String model;

    /**
     * 消息列表
     */
    @Schema(description = "消息列表")
    private List<ChatMessage> messages;

    /**
     * 温度参数 (0.0-2.0)
     */
    @Schema(description = "温度参数", example = "0.7", minimum = "0", maximum = "2")
    private Double temperature;

    /**
     * 最大生成token数
     */
    @Schema(description = "最大生成token数", example = "2048")
    private Integer maxTokens;

    /**
     * 核采样参数 (0.0-1.0)
     */
    @Schema(description = "核采样参数", example = "1.0", minimum = "0", maximum = "1")
    private Double topP;

    /**
     * 是否启用流式响应
     */
    @Schema(description = "是否启用流式响应", example = "true")
    private Boolean stream;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词", example = "你是一个有用的AI助手")
    private String systemPrompt;

    /**
     * 是否包含推理过程
     */
    @Schema(description = "是否包含推理过程", example = "false")
    private Boolean includeReasoning;

    /**
     * 工具列表（Function Calling支持）
     */
    @Schema(description = "工具列表，支持Function Calling")
    private Object tools;

    /**
     * 工具选择策略
     */
    @Schema(description = "工具选择策略", example = "auto")
    private Object toolChoice;

    /**
     * 响应格式控制
     */
    @Schema(description = "响应格式控制")
    private Object responseFormat;

    /**
     * 流式响应选项
     */
    @Schema(description = "流式响应选项")
    private Object streamOptions;

    /**
     * 聊天消息
     * <p>
     * 用于模型适配服务的统一消息格式
     * </p>
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "聊天消息")
    public static class ChatMessage {

        /**
         * 角色（system, user, assistant）
         */
        @Schema(description = "角色", example = "user", allowableValues = {"system", "user", "assistant"})
        private String role;

        /**
         * 消息内容（支持多模态）
         * <p>
         * 可以是：
         * 1. String - 纯文本消息
         * 2. Object - 多模态消息（包含文本和图片等）
         * </p>
         */
        @Schema(description = "消息内容，支持文本或多模态格式", example = "Hello, how are you?")
        private Object content;
    }
}
