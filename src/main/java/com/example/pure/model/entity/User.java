// src/main/java/com/example/demo13/model/User.java
package com.example.pure.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.Instant;

/**
 * 存储到数据库用
 * 用户实体类(Entity)
 */
@Data
public class User {
    /**
     * 用户ID，类的变量如果有final（值不可被修改）就必须通过构造器初始化，可变就用private通过set设置或修改
     * 设置一次的用构造器，多次修改的用get，set
     *
     */
    @Schema(description = "用户ID",example = "1")
    private Long id;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空") // message为出现不符合要求会把message的字符串抛出到异常的error.getDefaultMessage()输出给客户端
    @Length(min = 4, max = 50, message = "用户名长度必须在4-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "用户名只能包含字母、数字、下划线和连字符")
    @Schema(description = "用户名", required = true,example = "A")
    private String username;



    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @Schema(description = "密码", required = true,example = "AA")
    private String password;

    /**
     * 账号是否未过期
     */
    private boolean accountNonExpired = true;

    /**
     * 账号是否未锁定
     */
    private boolean accountNonLocked = true;

    /**
     * 凭证是否未过期
     */
    private boolean credentialsNonExpired = true;

    /**
     * 账号是否启用
     */
    private boolean enabled = true;

    /**
     * 最后登录时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant  lastLoginTime;

    /**
     * 创建时间
     * 使用UTC时间，最佳实践(为了提高缓存命中率所有用户共享大部分API响应减轻服务器负载)为使用Instant的UTC时间，统一使
     * 用UTC存储到数据库，再统一返回给前端再转换对应时区时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant  createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant  updatedTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant banExpiresAt;



    /**
     * 刷新令牌
     */
    private String refreshToken;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant refreshTokenExpires;



    public void setPassword(String password) {
        this.password = password;
    }
}
