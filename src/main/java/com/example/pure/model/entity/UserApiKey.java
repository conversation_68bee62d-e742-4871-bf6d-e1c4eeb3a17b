package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.Instant;

/**
 * 用户API密钥实体类
 * <p>
 * 存储用户的AI提供商API密钥信息，支持多个提供商和负载均衡
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserApiKey {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * AI提供商类型
     */
    private ProviderType provider;

    /**
     * 密钥名称（用户自定义）
     */
    private String keyName;

    /**
     * 加密后的API密钥
     */
    private String apiKeyEncrypted;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 优先级（数字越小优先级越高）
     */
    private Integer priority;

    /**
     * 使用次数统计
     */
    private Long usageCount;

    /**
     * 最后使用时间
     */
    private Instant lastUsedAt;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;

    /**
     * AI提供商类型枚举
     */
    public enum ProviderType {
        OPENAI("OpenAI"),
        ANTHROPIC("Anthropic"),
        GOOGLE("Google AI");

        private final String displayName;

        ProviderType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
