package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 聊天会话实体类
 * <p>
 * 存储聊天会话的配置信息和状态
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ChatSession {

    /**
     * 会话ID（主键）
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 使用的模型
     */
    private String model;

    /**
     * AI提供商类型
     */
    private UserApiKey.ProviderType provider;

    /**
     * 使用的API密钥ID
     */
    private Long apiKeyId;

    /**
     * 温度参数
     */
    private BigDecimal temperature;

    /**
     * 最大token数
     */
    private Integer maxTokens;

    /**
     * 自定义提示词
     */
    private String customPrompt;

    /**
     * 消息数量
     */
    private Integer messageCount;

    /**
     * 过期时间
     */
    private Instant expiresAt;

    /**
     * 创建时间
     */
    private Instant createdAt;
}
