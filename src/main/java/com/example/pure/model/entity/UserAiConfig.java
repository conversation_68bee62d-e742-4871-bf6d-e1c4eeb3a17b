package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 用户AI配置实体类
 * <p>
 * 存储用户的AI聊天偏好设置，包括默认模型、参数配置等
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserAiConfig {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 首选模型
     */
    private String preferredModel;

    /**
     * 默认温度参数 (0.0-2.0)
     */
    private BigDecimal defaultTemperature;

    /**
     * 默认最大token数
     */
    private Integer defaultMaxTokens;

    /**
     * 默认top_p参数 (0.0-1.0)
     */
    private BigDecimal defaultTopP;

    /**
     * 是否启用流式输出
     */
    private Boolean streamEnabled;

    /**
     * 超时时间(秒)
     */
    private Integer timeoutSeconds;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;
}
