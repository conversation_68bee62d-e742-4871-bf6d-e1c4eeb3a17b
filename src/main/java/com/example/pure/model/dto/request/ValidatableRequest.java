package com.example.pure.model.dto.request;

/**
 * 可验证的请求接口
 * <p>
 * 用于统一请求参数验证和日志记录
 * </p>
 */
public interface ValidatableRequest {
    
    /**
     * 验证请求参数
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    void validate() throws IllegalArgumentException;
    
    /**
     * 获取请求类型描述
     * @return 请求类型，用于日志记录
     */
    String getRequestType();
    
    /**
     * 获取请求日志信息
     * @return 日志信息，用于记录请求详情
     */
    String getLogInfo();
}
