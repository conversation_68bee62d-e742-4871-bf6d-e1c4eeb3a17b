package com.example.pure.model.dto.response.captcha;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * 用于反序列化 Google reCAPTCHA v2/v3 siteverify API 响应的 DTO。
 * 使用 @JsonIgnoreProperties(ignoreUnknown = true) 注解是为了增强健壮性，
 * 如果未来 Google 在响应中添加了新的字段，应用不会因为无法识别新字段而抛出异常。
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecaptchaResponse {
    /**
     * 验证是否成功。
     */
    private boolean success;

    /**
     * 验证的时间戳 (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)。
     */
    @JsonProperty("challenge_ts")
    private Instant challengeTs;

    /**
     * 发起 reCAPTCHA 验证请求的网站主机名。
     */
    private String hostname;

    /**
     * reCAPTCHA v3 中，本次验证的得分 (0.0 到 1.0)。
     */
    private double score;

    /**
     * reCAPTCHA v3 中，执行此请求的操作名称。
     */
    private String action;


    /**
     * 当验证失败时，返回的错误代码列表。
     */
    @JsonProperty("error-codes")
    private List<String> errorCodes;
}
