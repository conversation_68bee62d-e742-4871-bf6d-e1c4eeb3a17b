package com.example.pure.model.dto.request.videocomment;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 评论点赞/踩请求的数据传输对象 (DTO)
 * <p>
 * 用于封装客户端发起的对评论的点赞或踩操作请求。
 * </p>
 */
@Data
public class CommentLikeRequest {
    /**
     * 被操作的评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long commentId;

    /**
     * 操作类型。
     * 必须是 "like" 或 "dislike"。
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "like|dislike", message = "类型必须是 'like' 或 'dislike'")
    private String type;
}
