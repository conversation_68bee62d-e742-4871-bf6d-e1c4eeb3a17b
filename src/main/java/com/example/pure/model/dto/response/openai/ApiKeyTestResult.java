package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API密钥测试结果
 * <p>
 * 用于返回API密钥测试的详细结果信息
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "API密钥测试结果")
public class ApiKeyTestResult {

    /**
     * 测试是否成功
     */
    @Schema(description = "测试是否成功", example = "true")
    private boolean valid;

    /**
     * 测试消息
     */
    @Schema(description = "测试消息", example = "API密钥有效，连接正常")
    private String message;

    /**
     * 响应时间（毫秒）
     */
    @Schema(description = "响应时间（毫秒）", example = "150")
    private Long responseTime;

    /**
     * 提供商名称
     */
    @Schema(description = "提供商名称", example = "OpenAI")
    private String providerName;

    /**
     * 兼容格式的API密钥
     */
    @Schema(description = "兼容格式的API密钥", example = "sk-1001_1_a1b2c3d4e5f6")
    private String compatibleKey;
}
