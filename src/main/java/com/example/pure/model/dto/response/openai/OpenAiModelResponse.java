package com.example.pure.model.dto.response.openai;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI模型列表响应DTO
 * <p>
 * 完全兼容OpenAI API格式的模型列表响应
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OpenAI模型列表响应")
public class OpenAiModelResponse {

    /**
     * 对象类型
     */
    @Schema(description = "对象类型", example = "list")
    private String object = "list";

    /**
     * 模型数据列表
     */
    @Schema(description = "模型数据列表")
    private List<ModelData> data;

    /**
     * 模型数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "模型数据")
    public static class ModelData {
        /**
         * 模型ID
         */
        @Schema(description = "模型ID", example = "gpt-3.5-turbo")
        private String id;

        /**
         * 对象类型
         */
        @Schema(description = "对象类型", example = "model")
        private String object = "model";

        /**
         * 创建时间戳
         */
        @Schema(description = "创建时间戳", example = "1677610602")
        private Long created;

        /**
         * 拥有者
         */
        @JsonProperty("owned_by")
        @Schema(description = "拥有者", example = "openai")
        private String ownedBy;

        /**
         * 权限列表（可选）
         */
        @Schema(description = "权限列表")
        private List<Permission> permission;

        /**
         * 根节点（可选）
         */
        @Schema(description = "根节点")
        private String root;

        /**
         * 父节点（可选）
         */
        @Schema(description = "父节点")
        private String parent;
    }

    /**
     * 权限信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "权限信息")
    public static class Permission {
        /**
         * 权限ID
         */
        @Schema(description = "权限ID")
        private String id;

        /**
         * 对象类型
         */
        @Schema(description = "对象类型", example = "model_permission")
        private String object = "model_permission";

        /**
         * 创建时间戳
         */
        @Schema(description = "创建时间戳")
        private Long created;

        /**
         * 是否允许创建引擎
         */
        @JsonProperty("allow_create_engine")
        @Schema(description = "是否允许创建引擎")
        private Boolean allowCreateEngine;

        /**
         * 是否允许采样
         */
        @JsonProperty("allow_sampling")
        @Schema(description = "是否允许采样")
        private Boolean allowSampling;

        /**
         * 是否允许日志概率
         */
        @JsonProperty("allow_logprobs")
        @Schema(description = "是否允许日志概率")
        private Boolean allowLogprobs;

        /**
         * 是否允许搜索索引
         */
        @JsonProperty("allow_search_indices")
        @Schema(description = "是否允许搜索索引")
        private Boolean allowSearchIndices;

        /**
         * 是否允许查看
         */
        @JsonProperty("allow_view")
        @Schema(description = "是否允许查看")
        private Boolean allowView;

        /**
         * 是否允许微调
         */
        @JsonProperty("allow_fine_tuning")
        @Schema(description = "是否允许微调")
        private Boolean allowFineTuning;

        /**
         * 组织
         */
        @Schema(description = "组织")
        private String organization;

        /**
         * 组织
         */
        @Schema(description = "组织")
        private String group;

        /**
         * 是否阻塞
         */
        @JsonProperty("is_blocking")
        @Schema(description = "是否阻塞")
        private Boolean isBlocking;
    }
}
