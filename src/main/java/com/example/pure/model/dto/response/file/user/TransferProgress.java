package com.example.pure.model.dto.response.file.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件传输进度信息数据类
 * <p>
 * 封装文件传输过程中的进度信息，包括传输速度、进度百分比、时间估算等
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferProgress {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 传输类型（upload、download、copy等）
     */
    private String transferType;

    /**
     * 文件总大小（字节）
     */
    private long totalBytes;

    /**
     * 已传输字节数
     */
    private long transferredBytes;

    /**
     * 传输进度百分比（0.0 - 1.0）
     */
    private double progressPercent;

    /**
     * 当前传输速度（字节/秒）
     */
    private double currentSpeedBytesPerSecond;

    /**
     * 平均传输速度（字节/秒）
     */
    private double averageSpeedBytesPerSecond;

    /**
     * 传输开始时间（毫秒时间戳）
     */
    private long startTimeMs;

    /**
     * 当前时间（毫秒时间戳）
     */
    private long currentTimeMs;

    /**
     * 已用时间（毫秒）
     */
    private long elapsedTimeMs;

    /**
     * 预计剩余时间（毫秒）
     */
    private long estimatedRemainingTimeMs;

    /**
     * 传输状态
     */
    private TransferStatus status;

    /**
     * 传输状态枚举
     */
    public enum TransferStatus {
        /**
         * 未开始
         */
        NOT_STARTED,

        /**
         * 进行中
         */
        IN_PROGRESS,

        /**
         * 已完成
         */
        COMPLETED,

        /**
         * 出错
         */
        ERROR,

        /**
         * 已取消
         */
        CANCELLED
    }

    /**
     * 获取格式化的传输速度（MB/s）
     *
     * @return 格式化的速度字符串
     */
    public String getFormattedCurrentSpeed() {
        double speedMBps = currentSpeedBytesPerSecond / (1024.0 * 1024.0);
        return String.format("%.2f MB/s", speedMBps);
    }

    /**
     * 获取格式化的平均传输速度（MB/s）
     *
     * @return 格式化的平均速度字符串
     */
    public String getFormattedAverageSpeed() {
        double speedMBps = averageSpeedBytesPerSecond / (1024.0 * 1024.0);
        return String.format("%.2f MB/s", speedMBps);
    }

    /**
     * 获取格式化的进度百分比
     *
     * @return 格式化的百分比字符串
     */
    public String getFormattedProgress() {
        return String.format("%.1f%%", progressPercent * 100);
    }

    /**
     * 获取格式化的文件大小
     *
     * @return 格式化的文件大小字符串
     */
    public String getFormattedFileSize() {
        return formatBytes(totalBytes);
    }

    /**
     * 获取格式化的已传输大小
     *
     * @return 格式化的已传输大小字符串
     */
    public String getFormattedTransferredSize() {
        return formatBytes(transferredBytes);
    }

    /**
     * 获取格式化的已用时间
     *
     * @return 格式化的时间字符串
     */
    public String getFormattedElapsedTime() {
        return formatDuration(elapsedTimeMs);
    }

    /**
     * 获取格式化的预计剩余时间
     *
     * @return 格式化的时间字符串
     */
    public String getFormattedEstimatedRemainingTime() {
        if (estimatedRemainingTimeMs <= 0) {
            return "计算中...";
        }
        return formatDuration(estimatedRemainingTimeMs);
    }

    /**
     * 格式化字节数为可读的大小字符串
     *
     * @param bytes 字节数
     * @return 格式化的大小字符串
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 格式化时间长度为可读的时间字符串
     *
     * @param durationMs 时间长度（毫秒）
     * @return 格式化的时间字符串
     */
    private String formatDuration(long durationMs) {
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
