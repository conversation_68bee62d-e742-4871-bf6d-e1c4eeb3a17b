package com.example.pure.model.dto.response.openai;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI图片生成响应DTO
 * <p>
 * 完全兼容OpenAI API格式的图片生成响应
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OpenAI图片生成响应")
public class OpenAiImageResponse {

    /**
     * 创建时间戳（Unix时间戳）
     */
    @Schema(description = "创建时间戳", example = "1677652288")
    private Long created;

    /**
     * 生成的图片数据列表
     */
    @Schema(description = "生成的图片数据列表")
    private List<ImageData> data;

    /**
     * 图片数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "图片数据")
    public static class ImageData {
        
        /**
         * 图片URL（当response_format为url时）
         */
        @Schema(description = "图片URL", 
                example = "https://oaidalleapiprodscus.blob.core.windows.net/private/...")
        private String url;

        /**
         * Base64编码的图片数据（当response_format为b64_json时）
         */
        @JsonProperty("b64_json")
        @Schema(description = "Base64编码的图片数据")
        private String b64Json;

        /**
         * 修订后的提示词（DALL-E 3会自动优化提示词）
         */
        @JsonProperty("revised_prompt")
        @Schema(description = "修订后的提示词", 
                example = "A beautiful sunset over the ocean with several sailboats in the distance...")
        private String revisedPrompt;
    }

    /**
     * 创建成功响应（URL格式）
     */
    public static OpenAiImageResponse createUrlResponse(List<String> urls, List<String> revisedPrompts) {
        List<ImageData> imageDataList = new java.util.ArrayList<>();
        
        for (int i = 0; i < urls.size(); i++) {
            ImageData imageData = ImageData.builder()
                    .url(urls.get(i))
                    .revisedPrompt(revisedPrompts != null && i < revisedPrompts.size() ? 
                                   revisedPrompts.get(i) : null)
                    .build();
            imageDataList.add(imageData);
        }

        return OpenAiImageResponse.builder()
                .created(System.currentTimeMillis() / 1000)
                .data(imageDataList)
                .build();
    }

    /**
     * 创建成功响应（Base64格式）
     */
    public static OpenAiImageResponse createBase64Response(List<String> base64Images, List<String> revisedPrompts) {
        List<ImageData> imageDataList = new java.util.ArrayList<>();
        
        for (int i = 0; i < base64Images.size(); i++) {
            ImageData imageData = ImageData.builder()
                    .b64Json(base64Images.get(i))
                    .revisedPrompt(revisedPrompts != null && i < revisedPrompts.size() ? 
                                   revisedPrompts.get(i) : null)
                    .build();
            imageDataList.add(imageData);
        }

        return OpenAiImageResponse.builder()
                .created(System.currentTimeMillis() / 1000)
                .data(imageDataList)
                .build();
    }

    /**
     * 创建单张图片URL响应
     */
    public static OpenAiImageResponse createSingleUrlResponse(String url, String revisedPrompt) {
        ImageData imageData = ImageData.builder()
                .url(url)
                .revisedPrompt(revisedPrompt)
                .build();

        return OpenAiImageResponse.builder()
                .created(System.currentTimeMillis() / 1000)
                .data(List.of(imageData))
                .build();
    }

    /**
     * 创建单张图片Base64响应
     */
    public static OpenAiImageResponse createSingleBase64Response(String base64Image, String revisedPrompt) {
        ImageData imageData = ImageData.builder()
                .b64Json(base64Image)
                .revisedPrompt(revisedPrompt)
                .build();

        return OpenAiImageResponse.builder()
                .created(System.currentTimeMillis() / 1000)
                .data(List.of(imageData))
                .build();
    }
}
