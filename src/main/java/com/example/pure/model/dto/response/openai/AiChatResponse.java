package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * AI聊天响应DTO
 * <p>
 * 用于返回AI的回复消息
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "AI聊天响应")
public class AiChatResponse {

    /**
     * 响应内容
     */
    @Schema(description = "AI回复的内容", example = "你好！我是AI助手，很高兴为您介绍Java...")
    private String content;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型", example = "text", allowableValues = {"start", "text", "code", "error", "end"})
    private String type;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    /**
     * 响应时间戳
     */
    @Schema(description = "响应时间戳")
    private LocalDateTime timestamp;

    /**
     * 是否为流式响应的结束标志
     */
    @Schema(description = "是否为流式响应的结束标志", example = "false")
    private boolean isEnd;

    /**
     * 错误信息（如果有）
     */
    @Schema(description = "错误信息", example = "")
    private String error;

    /**
     * 创建文本响应
     */
    public static AiChatResponse text(String content, String sessionId) {
        return AiChatResponse.builder()
                .content(content)
                .type("text")
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .isEnd(false)
                .build();
    }

    /**
     * 创建结束响应
     */
    public static AiChatResponse end(String sessionId) {
        return AiChatResponse.builder()
                .content("")
                .type("end")
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static AiChatResponse error(String error, String sessionId) {
        return AiChatResponse.builder()
                .content("")
                .type("error")
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .error(error)
                .build();
    }
}
