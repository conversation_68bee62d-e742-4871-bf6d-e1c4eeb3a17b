package com.example.pure.model.dto.response.openai;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI流式聊天响应DTO
 * <p>
 * 完全兼容OpenAI API格式的流式聊天响应
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OpenAI流式聊天响应")
public class OpenAiStreamResponse {

    /**
     * 响应ID
     */
    @Schema(description = "响应ID", example = "chatcmpl-123")
    private String id;

    /**
     * 对象类型
     */
    @Schema(description = "对象类型", example = "chat.completion.chunk")
    private String object;

    /**
     * 创建时间戳
     */
    @Schema(description = "创建时间戳", example = "1677652288")
    private Long created;

    /**
     * 使用的模型
     */
    @Schema(description = "使用的模型", example = "gpt-3.5-turbo")
    private String model;

    /**
     * 选择列表
     */
    @Schema(description = "选择列表")
    private List<StreamChoice> choices;

    /**
     * 使用统计（仅在最后一个chunk中包含）
     */
    @Schema(description = "使用统计")
    private OpenAiChatResponse.Usage usage;

    /**
     * 流式选择项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "流式选择项")
    public static class StreamChoice {
        /**
         * 选择索引
         */
        @Schema(description = "选择索引", example = "0")
        private Integer index;

        /**
         * 增量内容
         */
        @Schema(description = "增量内容")
        private Delta delta;

        /**
         * 结束原因
         */
        @JsonProperty("finish_reason")
        @Schema(description = "结束原因", example = "stop", allowableValues = {"stop", "length", "content_filter"})
        private String finishReason;
    }

    /**
     * 增量内容
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "增量内容")
    public static class Delta {
        /**
         * 角色（仅在第一个chunk中包含）
         */
        @Schema(description = "角色", example = "assistant")
        private String role;

        /**
         * 内容片段
         */
        @Schema(description = "内容片段", example = "Hello")
        private String content;

        /**
         * 推理过程片段（兼容OpenAI o1格式）
         * <p>
         * 包含AI思考过程的增量内容
         * 仅在启用推理过程时使用
         * </p>
         */
        @JsonProperty("reasoning_content")
        @Schema(description = "推理过程片段")
        private String reasoningContent;
    }

    /**
     * 创建开始响应
     */
    public static OpenAiStreamResponse createStart(String id, String model) {
        return OpenAiStreamResponse.builder()
                .id(id)
                .object("chat.completion.chunk")
                .created(System.currentTimeMillis() / 1000)
                .model(model)
                .choices(List.of(StreamChoice.builder()
                        .index(0)
                        .delta(Delta.builder().role("assistant").build())
                        .build()))
                .build();
    }

    /**
     * 创建内容响应
     */
    public static OpenAiStreamResponse createContent(String id, String model, String content) {
        return OpenAiStreamResponse.builder()
                .id(id)
                .object("chat.completion.chunk")
                .created(System.currentTimeMillis() / 1000)
                .model(model)
                .choices(List.of(StreamChoice.builder()
                        .index(0)
                        .delta(Delta.builder().content(content).build())
                        .build()))
                .build();
    }

    /**
     * 创建推理过程响应（兼容OpenAI o1格式）
     */
    public static OpenAiStreamResponse createReasoning(String id, String model, String reasoningContent) {
        return OpenAiStreamResponse.builder()
                .id(id)
                .object("chat.completion.chunk")
                .created(System.currentTimeMillis() / 1000)
                .model(model)
                .choices(List.of(StreamChoice.builder()
                        .index(0)
                        .delta(Delta.builder().reasoningContent(reasoningContent).build())
                        .build()))
                .build();
    }

    /**
     * 创建结束响应
     */
    public static OpenAiStreamResponse createEnd(String id, String model, OpenAiChatResponse.Usage usage) {
        return OpenAiStreamResponse.builder()
                .id(id)
                .object("chat.completion.chunk")
                .created(System.currentTimeMillis() / 1000)
                .model(model)
                .choices(List.of(StreamChoice.builder()
                        .index(0)
                        .delta(Delta.builder().build())
                        .finishReason("stop")
                        .build()))
                .usage(usage)
                .build();
    }
}
