package com.example.pure.model.dto.response.openai;

import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI聊天完成响应DTO
 * <p>
 * 完全兼容OpenAI API格式的聊天响应
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OpenAI聊天完成响应")
public class OpenAiChatResponse {

    /**
     * 响应ID
     */
    @Schema(description = "响应ID", example = "chatcmpl-123")
    private String id;

    /**
     * 对象类型
     */
    @Schema(description = "对象类型", example = "chat.completion")
    private String object;

    /**
     * 创建时间戳
     */
    @Schema(description = "创建时间戳", example = "1677652288")
    private Long created;

    /**
     * 使用的模型
     */
    @Schema(description = "使用的模型", example = "gpt-3.5-turbo")
    private String model;

    /**
     * 选择列表
     */
    @Schema(description = "选择列表")
    private List<Choice> choices;

    /**
     * 使用统计
     */
    @Schema(description = "使用统计")
    private Usage usage;

    /**
     * 选择项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "选择项")
    public static class Choice {
        /**
         * 选择索引
         */
        @Schema(description = "选择索引", example = "0")
        private Integer index;

        /**
         * 消息内容（支持多模态）
         */
        @Schema(description = "消息内容")
        private OpenAiChatRequest.OpenAiMessage message;

        /**
         * 推理过程内容（兼容OpenAI o1格式）
         * <p>
         * 包含AI的思考过程，仅在请求中设置include_reasoning=true时返回
         * 兼容OpenAI o1系列模型的reasoning_content格式
         * </p>
         */
        @JsonProperty("reasoning_content")
        @Schema(description = "推理过程内容")
        private String reasoningContent;

        /**
         * 结束原因
         */
        @JsonProperty("finish_reason")
        @Schema(description = "结束原因", example = "stop", allowableValues = {"stop", "length", "content_filter"})
        private String finishReason;
    }

    /**
     * 使用统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "使用统计")
    public static class Usage {
        /**
         * 提示token数
         */
        @JsonProperty("prompt_tokens")
        @Schema(description = "提示token数", example = "9")
        private Integer promptTokens;

        /**
         * 完成token数
         */
        @JsonProperty("completion_tokens")
        @Schema(description = "完成token数", example = "12")
        private Integer completionTokens;

        /**
         * 总token数
         */
        @JsonProperty("total_tokens")
        @Schema(description = "总token数", example = "21")
        private Integer totalTokens;
    }
}
