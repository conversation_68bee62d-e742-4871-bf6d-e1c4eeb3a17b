package com.example.pure.model.dto.request.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * AI聊天请求DTO
 * <p>
 * 用于接收用户的聊天消息请求
 * </p>
 */
@Data
@Schema(description = "AI聊天请求")
public class AiChatRequest {

    /**
     * 用户输入的消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 1000, message = "消息内容不能超过1000个字符")
    @Schema(description = "用户输入的消息内容", example = "你好，请介绍一下Java")
    private String message;

    /**
     * 会话ID（可选，用于维持对话上下文）
     */
    @Schema(description = "会话ID", example = "session_123456")
    private String sessionId;

    /**
     * 消息类型（可选，用于区分不同类型的请求）
     */
    @Schema(description = "消息类型", example = "chat", allowableValues = {"chat", "code", "help"})
    private String messageType = "chat";
}
