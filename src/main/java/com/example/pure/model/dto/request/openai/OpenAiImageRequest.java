package com.example.pure.model.dto.request.openai;

import com.example.pure.model.dto.request.ValidatableRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * OpenAI图片生成请求DTO
 * <p>
 * 完全兼容OpenAI API格式的图片生成请求
 * 支持DALL-E模型的所有标准参数
 * </p>
 */
@Data
@Schema(description = "OpenAI图片生成请求")
public class OpenAiImageRequest implements ValidatableRequest {

    /**
     * 使用的模型ID
     * 支持的模型：dall-e-2, dall-e-3, gpt-image-1等
     */
    @NotBlank(message = "模型不能为空")
    @Schema(description = "模型ID", 
            example = "dall-e-3", 
            required = true,
            allowableValues = {"dall-e-2", "dall-e-3", "gpt-image-1"})
    private String model;

    /**
     * 图片描述提示词
     * 最大长度根据模型而定：DALL-E 2为1000字符，DALL-E 3为4000字符
     */
    @NotBlank(message = "提示词不能为空")
    @Size(max = 4000, message = "提示词不能超过4000个字符")
    @Schema(description = "图片描述提示词", 
            example = "A beautiful sunset over the ocean with sailboats",
            required = true)
    private String prompt;

    /**
     * 生成图片的数量
     * DALL-E 2支持1-10张，DALL-E 3只支持1张
     */
    @Min(value = 1, message = "图片数量不能小于1")
    @Max(value = 10, message = "图片数量不能大于10")
    @Schema(description = "生成图片的数量", 
            example = "1", 
            minimum = "1", 
            maximum = "10")
    private Integer n = 1;

    /**
     * 图片尺寸
     * DALL-E 2: 256x256, 512x512, 1024x1024
     * DALL-E 3: 1024x1024, 1024x1792, 1792x1024
     */
    @Pattern(regexp = "^(256x256|512x512|1024x1024|1024x1792|1792x1024)$", 
             message = "图片尺寸格式不正确")
    @Schema(description = "图片尺寸", 
            example = "1024x1024",
            allowableValues = {"256x256", "512x512", "1024x1024", "1024x1792", "1792x1024"})
    private String size = "1024x1024";

    /**
     * 图片质量（仅DALL-E 3支持）
     * standard: 标准质量
     * hd: 高清质量
     */
    @Pattern(regexp = "^(standard|hd)$", message = "图片质量参数不正确")
    @Schema(description = "图片质量（仅DALL-E 3支持）", 
            example = "standard",
            allowableValues = {"standard", "hd"})
    private String quality = "standard";

    /**
     * 图片风格（仅DALL-E 3支持）
     * vivid: 生动风格，产生超现实和戏剧性的图像
     * natural: 自然风格，产生更自然、不那么超现实的图像
     */
    @Pattern(regexp = "^(vivid|natural)$", message = "图片风格参数不正确")
    @Schema(description = "图片风格（仅DALL-E 3支持）", 
            example = "vivid",
            allowableValues = {"vivid", "natural"})
    private String style = "vivid";

    /**
     * 响应格式
     * url: 返回图片URL
     * b64_json: 返回base64编码的JSON
     */
    @JsonProperty("response_format")
    @Pattern(regexp = "^(url|b64_json)$", message = "响应格式参数不正确")
    @Schema(description = "响应格式", 
            example = "url",
            allowableValues = {"url", "b64_json"})
    private String responseFormat = "url";

    /**
     * 用户标识（可选）
     * 用于监控和检测滥用
     */
    @Size(max = 256, message = "用户标识不能超过256个字符")
    @Schema(description = "用户标识，用于监控和检测滥用", 
            example = "user-123456")
    private String user;

    /**
     * 验证模型和参数的兼容性
     */
    public void validateModelCompatibility() {
        if ("dall-e-3".equals(model)) {
            // DALL-E 3只支持生成1张图片
            if (n != null && n > 1) {
                throw new IllegalArgumentException("DALL-E 3只支持生成1张图片");
            }
            // DALL-E 3支持的尺寸
            if (size != null && !size.matches("^(1024x1024|1024x1792|1792x1024)$")) {
                throw new IllegalArgumentException("DALL-E 3支持的尺寸：1024x1024, 1024x1792, 1792x1024");
            }
        } else if ("dall-e-2".equals(model)) {
            // DALL-E 2不支持quality和style参数
            if (quality != null && !"standard".equals(quality)) {
                throw new IllegalArgumentException("DALL-E 2不支持quality参数");
            }
            if (style != null && !"vivid".equals(style)) {
                throw new IllegalArgumentException("DALL-E 2不支持style参数");
            }
            // DALL-E 2支持的尺寸
            if (size != null && !size.matches("^(256x256|512x512|1024x1024)$")) {
                throw new IllegalArgumentException("DALL-E 2支持的尺寸：256x256, 512x512, 1024x1024");
            }
        }
    }

    // ========================
    // ValidatableRequest接口实现
    // ========================

    @Override
    public void validate() throws IllegalArgumentException {
        validateModelCompatibility();
    }

    @Override
    public String getRequestType() {
        return "图片生成";
    }

    @Override
    public String getLogInfo() {
        return String.format("模型: %s, 提示词: %s, 尺寸: %s, 数量: %d",
                model,
                prompt != null && prompt.length() > 50 ? prompt.substring(0, 50) + "..." : prompt,
                size,
                n);
    }
}
