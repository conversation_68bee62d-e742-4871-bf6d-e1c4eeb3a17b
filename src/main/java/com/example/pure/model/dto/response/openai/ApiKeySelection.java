package com.example.pure.model.dto.response.openai;

import com.example.pure.model.entity.UserApiKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * API密钥选择结果
 * <p>
 * 负载均衡服务选择API密钥后的返回结果，包含密钥信息和解密后的密钥值
 * </p>
 */
@Data
@AllArgsConstructor
@Schema(description = "API密钥选择结果")
public class ApiKeySelection {

    /**
     * 选中的API密钥信息
     */
    @Schema(description = "选中的API密钥信息")
    private final UserApiKey apiKey;

    /**
     * 解密后的API密钥值
     */
    @Schema(description = "解密后的API密钥值")
    private final String decryptedKey;

}
