package com.example.pure.model.dto.request.user;

import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.util.QRLoginRedisUtil.QRCodeStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 二维码登录 SSE 响应数据传输对象
 * <p>
 * 用于 Server-Sent Events 流式推送二维码登录状态变化
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QRLoginSSEResponse {

    /**
     * 二维码唯一标识
     */
    private String qrId;

    /**
     * 事件类型
     * start: 开始监听
     * status: 状态变化
     * login: 登录成功
     * cancel: 登录取消
     * error: 错误事件
     * end: 结束监听
     */
    private String eventType;

    /**
     * 二维码状态
     */
    private QRCodeStatus status;

    /**
     * 是否过期
     */
    private boolean expired;

    /**
     * 用户信息（仅在登录成功时有值）
     */
    private UserDTO userInfo;

    /**
     * JWT访问令牌（仅在登录成功时有值）
     */
    private String accessToken;

    /**
     * JWT刷新令牌（仅在登录成功时有值）
     */
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private Date tokenExpiration;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 错误信息（仅在错误时有值）
     */
    private String error;

    /**
     * 事件时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp;

    /**
     * 是否为结束事件
     */
    @Builder.Default
    private boolean isEnd = false;

    /**
     * 创建开始事件
     */
    public static QRLoginSSEResponse createStartEvent(String qrId) {
        return QRLoginSSEResponse.builder()
                .qrId(qrId)
                .eventType("start")
                .message("开始监听二维码状态")
                .timestamp(LocalDateTime.now())
                .isEnd(false)
                .build();
    }

    /**
     * 创建状态变化事件
     */
    public static QRLoginSSEResponse createStatusEvent(String qrId, QRCodeStatus status, boolean expired) {
        return QRLoginSSEResponse.builder()
                .qrId(qrId)
                .eventType("status")
                .status(status)
                .expired(expired)
                .message("二维码状态已更新: " + status.name())
                .timestamp(LocalDateTime.now())
                .isEnd(false)
                .build();
    }

    /**
     * 创建登录成功事件
     */
    public static QRLoginSSEResponse createLoginSuccessEvent(String qrId, UserDTO userInfo,
                                                           String accessToken, String refreshToken,
                                                           Date tokenExpiration) {
        return QRLoginSSEResponse.builder()
                .qrId(qrId)
                .eventType("login")
                .status(QRCodeStatus.CONFIRMED)
                .userInfo(userInfo)
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenExpiration(tokenExpiration)
                .message("登录成功")
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }

    /**
     * 创建登录取消事件
     */
    public static QRLoginSSEResponse createCancelEvent(String qrId) {
        return QRLoginSSEResponse.builder()
                .qrId(qrId)
                .eventType("cancel")
                .status(QRCodeStatus.CANCELED)
                .message("用户取消了登录")
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }

    /**
     * 创建错误事件
     */
    public static QRLoginSSEResponse createErrorEvent(String qrId, String error) {
        return QRLoginSSEResponse.builder()
                .qrId(qrId)
                .eventType("error")
                .error(error)
                .message("发生错误: " + error)
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }

    /**
     * 创建结束事件
     */
    public static QRLoginSSEResponse createEndEvent(String qrId) {
        return QRLoginSSEResponse.builder()
                .qrId(qrId)
                .eventType("end")
                .message("监听结束")
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }
}
