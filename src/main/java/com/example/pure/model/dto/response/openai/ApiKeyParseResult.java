package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API密钥解析结果
 * <p>
 * 用于返回兼容格式API密钥的解析结果
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "API密钥解析结果")
public class ApiKeyParseResult {

    /**
     * 解析是否成功
     */
    @Schema(description = "解析是否成功", example = "true")
    private boolean valid;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    /**
     * 密钥ID
     */
    @Schema(description = "密钥ID", example = "1")
    private Long keyId;

    /**
     * 哈希值
     */
    @Schema(description = "哈希值", example = "a1b2c3d4e5f6")
    private String hash;
}
