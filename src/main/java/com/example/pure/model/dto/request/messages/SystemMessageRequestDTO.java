package com.example.pure.model.dto.request.messages;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemMessageRequestDTO {

    private Long recipientUserId;
    private String title;
    private String content;
    // notification,system
    private String messageType;
}
