package com.example.pure.model.dto.request.file.user;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * HTTP Range请求的数据传输对象
 * <p>
 * 封装Range请求的解析结果和相关信息
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RangeRequest {

    /**
     * 范围起始位置（字节）
     */
    private long rangeStart;

    /**
     * 范围结束位置（字节）
     */
    private long rangeEnd;

    /**
     * 内容长度（字节数）
     */
    private long contentLength;

    /**
     * 文件总大小（字节）
     */
    private long fileSize;

    /**
     * 是否为范围请求
     */
    private boolean isRangeRequest;

    /**
     * 原始Range头部值
     */
    private String originalRangeHeader;

    /**
     * 计算内容长度
     * <p>
     * 根据起始和结束位置计算实际的内容长度
     * </p>
     *
     * @return 内容长度
     */
    public long calculateContentLength() {
        return rangeEnd - rangeStart + 1;
    }

    /**
     * 验证范围是否有效
     * <p>
     * 检查范围的起始和结束位置是否合理
     * </p>
     *
     * @return 如果范围有效返回true，否则返回false
     */
    public boolean isValidRange() {
        return rangeStart >= 0 &&
               rangeEnd >= rangeStart &&
               rangeStart < fileSize &&
               rangeEnd < fileSize;
    }

    /**
     * 获取Content-Range头部值
     * <p>
     * 生成标准的Content-Range头部值，格式为: bytes start-end/total
     * </p>
     *
     * @return Content-Range头部值
     */
    public String getContentRangeHeader() {
        return String.format("bytes %d-%d/%d", rangeStart, rangeEnd, fileSize);
    }

    /**
     * 获取无效范围的Content-Range头部值
     * <p>
     * 当范围无效时使用的Content-Range头部值，格式为: bytes star/total
     * </p>
     *
     * @return 无效范围的Content-Range头部值
     */
    public String getInvalidRangeHeader() {
        return String.format("bytes */%d", fileSize);
    }

    /**
     * 创建完整文件的范围请求
     * <p>
     * 创建一个表示整个文件的范围请求对象
     * </p>
     *
     * @param fileSize 文件大小
     * @return 完整文件的范围请求对象
     */
    public static RangeRequest createFullFileRange(long fileSize) {
        return RangeRequest.builder()
                .rangeStart(0)
                .rangeEnd(fileSize - 1)
                .contentLength(fileSize)
                .fileSize(fileSize)
                .isRangeRequest(false)
                .originalRangeHeader(null)
                .build();
    }
}
