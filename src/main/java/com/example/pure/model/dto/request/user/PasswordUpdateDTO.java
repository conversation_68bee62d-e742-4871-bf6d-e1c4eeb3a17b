package com.example.pure.model.dto.request.user;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class PasswordUpdateDTO {
    @NotBlank(message = "邮箱不能为空")
    @Email
    private String email;


    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    private String newPassword;

    @NotBlank
    @Size(min = 6,max = 6,message = "验证码不能为空" )
    private String verifyCode;
}
