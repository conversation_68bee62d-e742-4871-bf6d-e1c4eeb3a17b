package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OpenAI兼容API密钥验证结果
 * <p>
 * 用于返回OpenAI兼容格式API密钥的验证结果
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OpenAI兼容API密钥验证结果")
public class OpenAiApiKeyValidationResult {

    /**
     * 验证是否成功
     */
    @Schema(description = "验证是否成功", example = "true")
    private boolean valid;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    /**
     * 密钥ID
     */
    @Schema(description = "密钥ID", example = "1")
    private Long keyId;

    /**
     * 验证消息
     */
    @Schema(description = "验证消息", example = "API密钥有效")
    private String message;
}
