package com.example.pure.model.dto.request.user;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Email;

@Data
public class UserUpdateDTO {
    @NotNull(message = "用户名不能为空")
    @Size(min = 4, max = 20, message = "用户名长度必须在4-20之间")
    private String username;

    @Email(message = "邮箱格式不正确")
    private String email;

    private String nickname;  // 可选的昵称
}
