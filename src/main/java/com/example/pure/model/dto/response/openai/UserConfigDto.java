package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 用户AI配置DTO
 * <p>
 * 用于用户AI配置的传输和展示
 * </p>
 */
@Data
@Schema(description = "用户AI配置")
public class UserConfigDto {

    /**
     * 首选模型
     */
    @Schema(description = "首选模型", example = "gpt-3.5-turbo")
    private String preferredModel;

    /**
     * 默认温度参数
     */
    @DecimalMin(value = "0.0", message = "温度参数不能小于0")
    @DecimalMax(value = "2.0", message = "温度参数不能大于2")
    @Schema(description = "默认温度参数", example = "0.7", minimum = "0", maximum = "2")
    private BigDecimal defaultTemperature;

    /**
     * 默认最大token数
     */
    @Min(value = 1, message = "最大token数不能小于1")
    @Max(value = 32768, message = "最大token数不能大于32768")
    @Schema(description = "默认最大token数", example = "4096")
    private Integer defaultMaxTokens;

    /**
     * 默认top_p参数
     */
    @DecimalMin(value = "0.0", message = "top_p参数不能小于0")
    @DecimalMax(value = "1.0", message = "top_p参数不能大于1")
    @Schema(description = "默认top_p参数", example = "1.0", minimum = "0", maximum = "1")
    private BigDecimal defaultTopP;

    /**
     * 是否启用流式输出
     */
    @Schema(description = "是否启用流式输出", example = "true")
    private Boolean streamEnabled;

    /**
     * 超时时间(秒)
     */
    @Min(value = 1, message = "超时时间不能小于1秒")
    @Max(value = 300, message = "超时时间不能大于300秒")
    @Schema(description = "超时时间(秒)", example = "30")
    private Integer timeoutSeconds;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词", example = "你是一个有用的AI助手")
    private String systemPrompt;
}
