package com.example.pure.controller.messages;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.Result;
import com.example.pure.model.dto.response.messages.MessageDTO;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.dto.request.messages.SystemMessageRequestDTO;
import com.example.pure.model.dto.response.messages.UnreadCountDTO;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.messages.MessagesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/messages")
public class MessagesController {

    @Autowired
    private MessagesService messagesService;

    /**
     * 系统发送消息给用户
     *
     * @param messageRequestDTO 请求体，包含接收者ID、标题、内容和消息类型
     * @return Result 操作成功则返回成功标识
     * Result<?>返回通配符类型，Result<?> 表示 Result 对象的 data 字段是某种未知的具体类型(比如可能是String,Integer等，
     * 但不是 Object 本身，除非它恰好是 Result<Object>)。你不能随意写入，读取出来也只能当作 Object
     */
    @PostMapping("/system/send")
    public Result<?> sendSystemMessage(@RequestBody SystemMessageRequestDTO messageRequestDTO) {
        // 调用 Service 层方法处理系统消息发送逻辑
        messagesService.sendSystemMessage(messageRequestDTO);
        // 返回成功响应
        return Result.success();
    }

    /**
     * 获取用户未读消息数
     *
     * @return Result<UnreadCountDTO>
     */
    @GetMapping("/users/unread-count")
    public Result<UnreadCountDTO> getUnreadMessageCount(@AuthenticationPrincipal CustomUserDetails userDetails) {

        Long userId=userDetails.getUserId();
        // 调用 Service 层方法获取未读消息数
        UnreadCountDTO unreadCountDTO = messagesService.getUnreadMessageCount(userId);
        // 返回成功响应，并携带 DTO 数据
        return Result.success(unreadCountDTO);
    }

    /**
     * 标记用户所有未读为已读
     *
     * @return Result
     */
    @PostMapping("/users/mark-all-as-read")
    public Result<?> markAllMessagesAsRead(@AuthenticationPrincipal CustomUserDetails userDetails) {
        Long userId=userDetails.getUserId();
        // 调用 Service 层方法，标记用户所有未读消息为已读
        messagesService.markAllMessagesAsRead(userId);
        // 返回成功响应
        return Result.success();
    }

    /**
     * 标记用户单条消息为已读
     *
     *
     * @param messageId 消息ID
     * @return Result
     */
    @PostMapping("/users/message/{messageId}/mark-as-read")
    public Result<?> markMessageAsRead(@PathVariable Long messageId, Authentication authentication) {
        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        Long userId = customUserDetails.getUserId();
        // 调用 Service 层方法，标记用户单条消息为已读
        messagesService.markMessageAsRead(userId, messageId);
        // 返回成功响应
        return Result.success();
    }

    /**
     * 获取用户消息列表 (分页)
     *
     * @param page   页码
     * @param size   每页数量
     * @param status 消息状态 (可选: unread, read, all)
     * @return Result<PageFinalResult<MessageDTO>> 包含消息DTO列表的分页结果
     */
    @GetMapping("/unread")
    public Result<PageFinalResult<MessageDTO>> getUserMessages(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page, // 从查询参数中获取页码，默认为1
            @RequestParam(defaultValue = "10") int size, // 从查询参数中获取每页数量，默认为10
            @RequestParam(required = false, defaultValue = "all") String status) { // 从查询参数中获取消息状态，可选，默认为 "all"
        // 1. 将接收到的 page 和 size 参数封装到 PageRequestDTO 对象中
        PageRequestDTO pageRequestDTO = new PageRequestDTO();
        pageRequestDTO.setPageNum(page); // 设置当前页码
        pageRequestDTO.setPageSize(size); // 设置每页数量

        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        Long userId = customUserDetails.getUserId();

        // 2. 调用 MessagesService 的 getUserMessages 方法，传入用户ID、分页请求DTO和消息状态
        PageFinalResult<MessageDTO> pageFinalResult = messagesService.getUserMessages(userId, pageRequestDTO, status);

        // 3. 将 Service 返回的 PageFinalResult<MessageDTO> 对象通过 Result.success() 封装后返回给客户端
        //    这符合 Purpose.txt 中定义的分页返回要求
        return Result.success(pageFinalResult);
    }

    @DeleteMapping("/delete/all")
    public Result<?> deleteAllMessages(@AuthenticationPrincipal CustomUserDetails userDetails) {
        Long userId=userDetails.getUserId();
        // 调用 Service 层方法，删除用户所有消息
        messagesService.deleteAllMessages(userId);
        // 返回成功响应
        return Result.success();
    }

    @DeleteMapping("/delete/{messageId}")
    public Result<?> deleteMessage(@PathVariable Long messageId, Authentication authentication) {
        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        Long userId = customUserDetails.getUserId();
        // 调用 Service 层方法，删除用户指定消息
        messagesService.deleteMessageById(userId, messageId);
        // 返回成功响应
        return Result.success();
    }


}
