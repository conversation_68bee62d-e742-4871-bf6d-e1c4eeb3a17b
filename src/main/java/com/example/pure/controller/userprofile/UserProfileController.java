package com.example.pure.controller.userprofile;

import com.example.pure.common.Result;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.service.userprofile.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户详细信息相关的控制器
 * 处理用户详细资料的查询和更新
 */
@Slf4j
@RestController
@RequestMapping("/api/user-profile") // 使用新的路径前缀
public class UserProfileController {

    private final UserProfileService userProfileService;

    @Autowired
    public UserProfileController(UserProfileService userProfileService) {
        this.userProfileService = userProfileService;
    }

    /**
     * 根据用户名查询用户详细信息
     * 需要 ROLE_COMMON 或 ROLE_ADMIN 角色
     * 查询 username 用户名
     * @return 用户详细信息
     */
    @GetMapping("")
    @PreAuthorize("hasAnyRole('COMMON', 'ADMIN')") // 权限保持不变，或根据需要调整
    public Result<UserProfile> getUserProfileByUsername(@AuthenticationPrincipal UserDetails userDetails) {

            String username=userDetails.getUsername();

        // 注意：这里不再需要 @Valid，因为 username 是路径变量，不是请求体
        log.debug("请求获取用户 '{}' 的详细信息", username);
        UserProfile UserProfile = userProfileService.findUserProfileByUsername(username);
        if (UserProfile == null) {
            log.warn("未找到用户 '{}' 的详细信息", username);
            // 返回 404 或根据业务逻辑返回成功但 data 为 null
            throw new BusinessException(404, "未找到用户详细信息") ;
        }
        return Result.success(UserProfile);
    }

    /**
     * 更新指定用户的详细个人信息
     * @param userProfile 包含更新信息的 DTO
     * @return 操作结果
     */
    @PutMapping("")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())")
    public Result<Void> updateUserProfile(@Valid @RequestBody UserProfile userProfile) {

        log.debug("请求更新用户 '{}' 的详细信息", userProfile.getUsername());
        // 确保 DTO 中的 username 与路径变量一致，防止篡改
        // 注意：Service 层实现中已经处理了 username 和 ID 的一致性检查
        userProfile.setUsername(userProfile.getUsername()); // 强制设置路径中的 username
        userProfileService.updateUserProfileByUsername(userProfile);
        return Result.success("更新用户资料成功");
    }

    // 可以根据需要添加其他端点，例如根据用户 ID 查询/更新 (如果前端需要)
    // 例如:
    // @GetMapping("/id/{userId}")
    // @PreAuthorize("hasRole('ADMIN')") // 假设只有管理员能通过 ID 查询
    // public Result<UserProfile> getUserProfileById(@PathVariable Long userId) { ... }{
}
