package com.example.pure.controller.auth;


import com.example.pure.common.PageFinalResult;
import com.example.pure.common.Result;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.model.entity.OperatingLog;
import com.example.pure.service.auth.OperatingLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 操作日志控制器
 * 处理用户操作日志的查询
 */
@RestController
@RequestMapping("/api/logs")
public class OperatingLogController {

    private final OperatingLogService operatingLogService;
    private final UserMapper userMapper;

    public OperatingLogController(OperatingLogService operatingLogService, UserMapper userMapper) {
        this.operatingLogService = operatingLogService;
        this.userMapper = userMapper;
    }

    /**
     * 查询当前用户的操作日志（分页）
     * 需要COMMON或ADMIN角色
     * @param authentication Spring Security提供的认证信息
     * @param pageRequest 分页请求参数
     * @return 操作日志分页结果
     */
    @GetMapping("/my-operations")
    @PreAuthorize("hasAnyRole('COMMON', 'ADMIN')")
    public  Result<PageFinalResult<OperatingLog>> getCurrentUserOperationLogs(Authentication authentication, @Valid PageRequestDTO pageRequest) {
        try {
            // 从认证信息中获取用户名
            String username = authentication.getName();

            // 通过用户名查询用户ID
            UserDTO user = userMapper.findByUsername(username);
            if (user == null) {
               throw new BusinessException("未查询到用户信息");
            }

            // 获取分页查询结果
            PageFinalResult<OperatingLog> pageResult = operatingLogService.getUserOperatingLogs(user.getId(), pageRequest);



            // 返回成功响应，将日志列表作为data，分页信息作为meta
            return Result.success("请求成功，已返回数据",pageResult);
        } catch (Exception e) {
            throw new BusinessException(500, "查询操作日志失败: " + e.getMessage());
        }
    }
}
