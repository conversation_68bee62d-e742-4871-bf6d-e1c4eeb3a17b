package com.example.pure.controller.auth;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.auth.QRLoginDTO.*;
import com.example.pure.model.dto.response.auth.TokenResponse;
import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.service.auth.AuthService;
import com.example.pure.service.auth.QRLoginNotificationService;
import com.example.pure.service.auth.QRLoginSSEService;
import com.example.pure.service.user.UserService;
import com.example.pure.util.JwtUtil;
import com.example.pure.util.QRCodeUtil;
import com.example.pure.util.QRContentParser;
import com.example.pure.util.QRLoginRedisUtil;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;
import com.example.pure.util.QRLoginRedisUtil.QRCodeStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * 二维码登录控制器
 * <p>
 * 提供二维码登录相关的API接口和WebSocket消息处理
 * </p>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/qrlogin")
@Tag(name = "二维码登录", description = "二维码登录相关接口")
public class QRLoginController {

    private final QRLoginRedisUtil qrLoginUtil;
    private final QRCodeUtil qrCodeUtil;
    private final UserService userService;
    private final AuthService authService;
    private final JwtUtil jwtUtil;
    private final QRLoginSSEService qrLoginSSEService;
    private final QRLoginNotificationService notificationService;

    /**
     * 创建二维码登录会话
     * 网页端发送创建请求根据请求参数来创建Websocket或者SSE
     * @return 二维码信息，包含二维码ID、内容和过期时间
     */
    @GetMapping("/create")
    @Operation(summary = "创建二维码登录会话", description = "前端点击了二维码登录按钮，返回二维码ID、Base64编码的二维码图片和过期时间")
    public Result<QRCodeResponse> createQRCode(
            @Parameter(description = "通信类型", required = false)
            @RequestParam(defaultValue = "WEBSOCKET") CommunicationType communicationType) {
        try {
            // 生成二维码唯一标识（包含通信类型）
            String qrId = qrLoginUtil.generateQRCodeId(communicationType.getCode());

            // 获取二维码信息
            QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);

            // 构建二维码内容，包含应用标识、二维码ID和通信类型
            // 移动端扫描时需要根据此内容识别并处理
            String qrContent = QRContentParser.generateQRContent(qrId, communicationType);

            // 生成二维码图片并转为Base64
            String qrBase64 = qrCodeUtil.generateQRCodeAsDataUri(qrContent);

            // 构建响应
            QRCodeResponse response = QRCodeResponse.builder()
                    .qrId(qrId)
                    .qrContent(qrBase64)
                    .expireTime(qrInfo.getExpireTime())
                    .build();

            return Result.success("二维码创建成功", response);
        } catch (Exception e) {
            log.error("创建二维码失败", e);
            throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "创建二维码失败: " + e.getMessage());
        }
    }

    /**
     * 查询二维码状态
     * <p>
     * 用于Web端轮询二维码状态（作为WebSocket的后备方案）
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 二维码当前状态信息
     */
    @GetMapping("/status/{qrId}")
    @Operation(summary = "查询二维码状态", description = "用于Web端轮询二维码状态，作为WebSocket的后备方案")
    public Result<QRStatusResponse> getQRCodeStatus(@PathVariable String qrId) {
        // 获取二维码信息
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);

        // 如果二维码不存在，返回错误
        if (ObjectUtils.isEmpty(qrInfo)) {
            throw  new BusinessException(ResponseCode.NOT_FOUND, "二维码不存在或已失效");
        }

        // 构建状态响应
        QRStatusResponse response = QRStatusResponse.builder()
                .status(qrInfo.getStatus())
                .expired(qrInfo.isExpired())
                .build();

        // 如果二维码状态为已确认，添加用户信息和令牌
        if (qrInfo.getStatus() == QRCodeStatus.CONFIRMED) {
            UserDTO userInfo = qrInfo.getUserInfo();
            response.setUserInfo(userInfo);

            // 生成JWT令牌
            TokenResponse tokenResponse = authService.createQRLoginTokens(userInfo.getUsername());
            response.setAccessToken(tokenResponse.getAccessToken());
            response.setRefreshToken(tokenResponse.getRefreshToken());

            // 登录成功后移除二维码信息
            qrLoginUtil.removeQRCode(qrId);
        }

        return Result.success("获取二维码状态成功", response);
    }

    /**
     * SSE 订阅二维码状态
     * <p>
     * 使用 Server-Sent Events 实现二维码状态的实时推送
     * 客户端通过此接口建立 SSE 连接，实时接收二维码状态变化
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return SSE 发射器，用于推送状态变化
     */
    @GetMapping(value = "/sse/subscribe/{qrId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(
            summary = "SSE 订阅二维码状态",
            description = "使用 Server-Sent Events 实时推送二维码状态变化，支持扫描、确认、取消等状态更新"
    )
    @ApiResponse(responseCode = "200", description = "成功建立 SSE 连接")
    @ApiResponse(responseCode = "404", description = "二维码不存在")
    @ApiResponse(responseCode = "500", description = "服务器内部错误")
    public SseEmitter subscribeQRStatusSSE(
            @Parameter(description = "二维码唯一标识", required = true)
            @PathVariable String qrId) {

        log.info("收到 SSE 二维码状态订阅请求 - QR ID: {}", qrId);

        // 验证二维码是否存在
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (ObjectUtils.isEmpty(qrInfo)) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "二维码不存在或已失效");
        }

        // 创建 SSE 连接
        return qrLoginSSEService.subscribeQRStatus(qrId);
    }

    /**
     * 扫描二维码
     * <p>
     * 移动端扫描二维码后调用该接口，更新二维码状态为已扫描
     * </p>
     * 移动端扫描网页端SQ码，后端服务器通过WebSocket通知消息发送给网页端SQL已扫描，响应移动端，移动端确认登陆后，通知
     * web网页端消息，如果是确认的话就发送数据（包含登陆信息和Token)让网页端登录账号并更新页面
     *
     * @param request 扫描请求参数，包含二维码ID和用户令牌
     * @return 处理结果
     */
    @PostMapping("/scan")
    @Operation(summary = "扫描二维码", description = "移动端扫描二维码后调用该接口，更新二维码状态为已扫描")
    public Result<Map<String, Object>> scanQRCode(@RequestBody QRScanRequest request) {
        // 验证请求参数
        if (request.getQrId() == null || request.getToken() == null) {
            throw new BusinessException(ResponseCode.INVALID_PARAMETER, "缺少必要参数");
        }

        // 使用统一服务自动获取二维码信息并解析通信类型
        QRCodeInfo qrInfo;


        //验证用户是否已经扫描
        if(qrLoginUtil.checkDuplicateVerificationRequest(request.getQrId(), QRCodeStatus.SCANNED)) {
            throw new BusinessException((ResponseCode.BAD_REQUEST),"二维码已经扫描");
        }

        // 验证用户令牌
        if (!userService.validateToken(request.getToken())) {
            throw new BusinessException(ResponseCode.UNAUTHORIZED, "无效的用户令牌");
        }

        // 从令牌中获取用户信息
        String username = jwtUtil.getUsernameFromToken(request.getToken());
        UserDTO userInfo = userService.findUserByUsername(username);

        if (userInfo == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND, "用户不存在");
        }

        // 更新二维码状态为已扫描
        boolean success = qrLoginUtil.scanQRCode(request.getQrId(), userInfo);

        if (!success) {
            throw new BusinessException(ResponseCode.STATUS_ERROR, "无效或已过期的二维码");
        }

        // 自动获取二维码信息并推送状态变化
        notificationService.notifyStatusChangeAuto(request.getQrId());

        // 返回成功响应
        Map<String, Object> data = new HashMap<>();
        data.put("qrId", request.getQrId());
        data.put("message", "二维码已扫描，等待确认");

        return Result.success("扫描成功", data);
    }

    /**
     * 确认二维码登录
     * <p>
     * 移动端用户确认或拒绝登录后调用该接口
     * 如果确认登录，则通过WebSocket向Web客户端发送完整的登录凭证，客户端接着重定向页面
     * </p>
     *
     * @param request 确认请求参数，包含二维码ID和确认结果
     * @return 处理结果
     */
    @PostMapping("/confirm")
    @Operation(summary = "确认二维码登录", description = "移动端用户确认或拒绝登录后调用该接口，支持WebSocket和SSE两种通信方式")
    public Result<Map<String, Object>> confirmQRLogin(@RequestBody QRConfirmRequest request) {
        // 验证请求参数
        if (request.getQrId() == null) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "缺少必要参数");
        }

        // 获取二维码信息
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(request.getQrId());

        if (qrInfo == null || qrInfo.isExpired()) {
            throw BusinessException.of(ResponseCode.STATUS_ERROR, "无效或已过期的二维码");
        }

        if (qrInfo.getStatus() != QRCodeStatus.SCANNED) {
            throw BusinessException.of(ResponseCode.STATUS_ERROR, "二维码未被扫描，无法确认");
        }

        // 注意：通信类型解析已移至统一服务中，无需在控制器中处理

        // 根据确认结果更新二维码状态
        boolean success;
        if (request.isConfirmed()) {
            // 确认登录
            success = qrLoginUtil.confirmQRCodeLogin(request.getQrId());

            if (success) {
                // 获取用户信息
                UserDTO userInfo = qrInfo.getUserInfo();

                // 生成JWT令牌
                TokenResponse tokenResponse = authService.createQRLoginTokens(userInfo.getUsername());

                // 自动推送登录成功事件
                notificationService.notifyLoginSuccessAuto(
                        request.getQrId(),
                        tokenResponse.getAccessToken(),
                        tokenResponse.getRefreshToken(),
                        tokenResponse.getAccessTokenExpiresIn()
                );

                // 登录成功后移除二维码信息
                qrLoginUtil.removeQRCode(request.getQrId());

                log.info("用户 {} 通过二维码扫描成功登录，QR ID: {}", userInfo.getUsername(), request.getQrId());
            }
        } else {
            // 取消登录
            success = qrLoginUtil.cancelQRCodeLogin(request.getQrId());

            // 自动推送取消事件
            if (success) {
                notificationService.notifyLoginCancelAuto(request.getQrId());
                log.info("用户取消了二维码登录，QR ID: {}", request.getQrId());
            }
        }

        if (!success) {
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理确认请求失败");
        }

        // 返回成功响应
        Map<String, Object> data = new HashMap<>();
        data.put("qrId", request.getQrId());
        data.put("confirmed", request.isConfirmed());

        return Result.success(request.isConfirmed() ? "已确认登录" : "已取消登录", data);
    }



    /**
     * 获取二维码登录统计信息
     * <p>
     * 提供系统监控和调试信息，包括活跃连接数、内存使用情况等
     * </p>
     *
     * @return 状态统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取二维码登录统计信息", description = "获取当前活跃连接数、内存使用情况和系统状态")
    public Result<Map<String, Object>> getQRLoginStats() {
        Map<String, Object> stats = notificationService.getSystemStats();
        return Result.success("获取统计信息成功", stats);
    }
}
