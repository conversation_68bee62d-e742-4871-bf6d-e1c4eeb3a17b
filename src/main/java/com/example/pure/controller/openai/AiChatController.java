package com.example.pure.controller.openai;

import com.example.pure.common.Result;
import com.example.pure.model.dto.request.openai.AiChatRequest;
import com.example.pure.service.openai.AiChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;

/**
 * AI聊天控制器
 * <p>
 * 提供AI聊天功能，支持：
 * - 实时流式对话
 * - Server-Sent Events (SSE) 响应
 * - 智能消息匹配
 * - 会话管理
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/ai-chat")
@RequiredArgsConstructor
@Tag(name = "AI聊天", description = "AI聊天相关接口")
public class AiChatController {

    private final AiChatService aiChatService;

    /**
     * AI聊天流式响应接口
     * <p>
     * 使用Server-Sent Events (SSE) 技术实现实时流式对话，
     * 模拟AI大模型的逐字输出效果
     * </p>
     *
     * @param request 聊天请求，包含用户消息和会话信息
     * @param authentication 用户认证信息
     * @return SSE流式响应
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(
            summary = "AI聊天流式响应",
            description = "使用SSE技术实现AI聊天的实时流式响应，模拟大模型逐字输出效果"
    )
    @ApiResponse(responseCode = "200", description = "成功建立SSE连接")
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @ApiResponse(responseCode = "500", description = "服务器内部错误")
    public SseEmitter chatStream(
            @Valid @RequestBody AiChatRequest request,
            @Parameter(description = "用户认证信息", hidden = true) Authentication authentication) {

        log.info("收到AI聊天流式请求 - 用户: {}, 消息: {}, 会话ID: {}",
                authentication != null ? authentication.getName() : "匿名用户",
                request.getMessage(),
                request.getSessionId());

        /*spring检测到SseEmitter返回值自动调用request.startAsync()
        * 然后设置响应体:ContentType:text/event-stream,释放Http请求的线程
        * 保持连接开放，等待其他线程的数据传入
        * */

        // 处理聊天请求并返回SSE流，异常处理交给服务层
        return aiChatService.processChat(request);
    }

    /**
     * 生成新的会话ID
     * <p>
     * 为新的聊天会话生成唯一标识符
     * </p>
     *
     * @return 包含新会话ID的响应
     */
    @GetMapping("/session/new")
    @Operation(summary = "生成新会话ID", description = "为新的聊天会话生成唯一标识符")
    @ApiResponse(responseCode = "200", description = "成功生成会话ID")
    public Result<String> generateNewSession() {
        try {
            String sessionId = aiChatService.generateSessionId();
            log.debug("生成新的会话ID: {}", sessionId);
            return Result.success(sessionId, "会话ID生成成功");
        } catch (Exception e) {
            log.error("生成会话ID时发生错误", e);
            return Result.errorTyped(500, "生成会话ID失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     * <p>
     * 检查AI聊天服务的可用性
     * </p>
     *
     * @return 服务状态信息
     */
    @GetMapping("/health")
    @Operation(summary = "AI聊天服务健康检查", description = "检查AI聊天服务的可用性")
    @ApiResponse(responseCode = "200", description = "服务正常")
    public Result<String> healthCheck() {
        try {
            log.debug("AI聊天服务健康检查");
            return Result.success("AI聊天服务运行正常", "服务状态正常");
        } catch (Exception e) {
            log.error("健康检查时发生错误", e);
            return Result.errorTyped(500, "服务异常: " + e.getMessage());
        }
    }

    /**
     * 获取支持的消息类型
     * <p>
     * 返回AI聊天支持的消息类型列表
     * </p>
     *
     * @return 支持的消息类型列表
     */
    @GetMapping("/message-types")
    @Operation(summary = "获取支持的消息类型", description = "返回AI聊天支持的消息类型列表")
    @ApiResponse(responseCode = "200", description = "成功获取消息类型列表")
    public Result<String[]> getSupportedMessageTypes() {
        try {
            String[] messageTypes = {"chat", "code", "help"};
            return Result.success("获取消息类型成功", messageTypes);
        } catch (Exception e) {
            log.error("获取消息类型时发生错误", e);
            return Result.errorTyped(500, "获取消息类型失败: " + e.getMessage());
        }
    }
}
