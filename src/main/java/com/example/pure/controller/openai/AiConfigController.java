package com.example.pure.controller.openai;

import com.example.pure.common.Result;
import com.example.pure.model.dto.response.openai.ApiKeyTestResult;
import com.example.pure.model.dto.request.openai.AddApiKeyRequest;
import com.example.pure.model.dto.response.openai.ApiKeyDto;
import com.example.pure.model.dto.request.openai.UpdateApiKeyRequest;
import com.example.pure.model.dto.response.openai.UserConfigDto;
import com.example.pure.model.entity.UserAiConfig;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.openai.AiConfigService;
import com.example.pure.service.openai.LoadBalancerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI配置管理控制器
 * <p>
 * 提供用户AI配置和API密钥的管理功能
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/ai/config")
@RequiredArgsConstructor
@Tag(name = "AI配置管理", description = "用户AI配置和API密钥管理接口")
public class AiConfigController {

    private final AiConfigService aiConfigService;
    private final LoadBalancerService loadBalancerService;

    // ========================
    // 用户AI配置管理
    // ========================

    /**
     * 获取用户AI配置
     */
    @GetMapping("/user")
    @Operation(summary = "获取用户AI配置", description = "获取当前用户的AI配置信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<UserConfigDto> getUserConfig(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfig config = aiConfigService.getUserConfig(userId);
            UserConfigDto dto = convertToUserConfigDto(config);

            log.debug("获取用户AI配置成功 - 用户ID: {}", userId);
            return Result.success("获取成功", dto);
        } catch (Exception e) {
            log.error("获取用户AI配置失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户AI配置
     */
    @PutMapping("/user")
    @Operation(summary = "更新用户AI配置", description = "更新当前用户的AI配置信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<UserConfigDto> updateUserConfig(
            @Valid @RequestBody UserConfigDto configDto,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfig config = convertToUserAiConfig(configDto);

            UserAiConfig updatedConfig = aiConfigService.updateUserConfig(userId, config);
            UserConfigDto dto = convertToUserConfigDto(updatedConfig);

            log.info("更新用户AI配置成功 - 用户ID: {}", userId);
            return Result.success("更新成功", dto);
        } catch (Exception e) {
            log.error("更新用户AI配置失败", e);
            return Result.errorTyped(500, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户AI配置
     */
    @PostMapping("/user/reset")
    @Operation(summary = "重置用户AI配置", description = "将用户AI配置重置为默认值")
    @ApiResponse(responseCode = "200", description = "重置成功")
    public Result<UserConfigDto> resetUserConfig(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfig config = aiConfigService.resetUserConfig(userId);
            UserConfigDto dto = convertToUserConfigDto(config);

            log.info("重置用户AI配置成功 - 用户ID: {}", userId);
            return Result.success("重置成功", dto);
        } catch (Exception e) {
            log.error("重置用户AI配置失败", e);
            return Result.errorTyped(500, "重置失败: " + e.getMessage());
        }
    }

    // ========================
    // API密钥管理
    // ========================

    /**
     * 获取用户的所有API密钥
     */
    @GetMapping("/api-keys")
    @Operation(summary = "获取API密钥列表", description = "获取当前用户的所有API密钥")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ApiKeyDto>> getUserApiKeys(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<UserApiKey> apiKeys = aiConfigService.getUserApiKeys(userId);
            List<ApiKeyDto> dtos = apiKeys.stream()
                    .map(this::convertToApiKeyDto)
                    .collect(Collectors.toList());

            log.debug("获取用户API密钥列表成功 - 用户ID: {}, 数量: {}", userId, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户API密钥列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据提供商获取API密钥
     */
    @GetMapping("/api-keys/{provider}")
    @Operation(summary = "根据提供商获取API密钥", description = "获取指定提供商的API密钥列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ApiKeyDto>> getUserApiKeysByProvider(
            @PathVariable UserApiKey.ProviderType provider,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<UserApiKey> apiKeys = aiConfigService.getUserApiKeysByProvider(userId, provider);
            List<ApiKeyDto> dtos = apiKeys.stream()
                    .map(this::convertToApiKeyDto)
                    .collect(Collectors.toList());

            log.debug("获取用户指定提供商API密钥成功 - 用户ID: {}, 提供商: {}, 数量: {}",
                    userId, provider, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户指定提供商API密钥失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 添加API密钥
     */
    @PostMapping("/api-keys")
    @Operation(summary = "添加API密钥", description = "为当前用户添加新的API密钥")
    @ApiResponse(responseCode = "200", description = "添加成功")
    public Result<ApiKeyAddResult> addApiKey(
            @Valid @RequestBody AddApiKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            UserApiKey apiKey = aiConfigService.addApiKey(
                    userId,
                    request.getProvider(),
                    request.getKeyName(),
                    request.getApiKey(),
                    request.getPriority()
            );

            // 生成兼容格式的API密钥
            String compatibleApiKey = aiConfigService.generateCompatibleApiKey(userId, apiKey.getId());

            ApiKeyAddResult result = new ApiKeyAddResult(
                    apiKey.getId(),
                    compatibleApiKey,
                    "API密钥已添加，兼容格式密钥已生成"
            );

            log.info("添加API密钥成功 - 用户ID: {}, 密钥ID: {}, 提供商: {}",
                    userId, apiKey.getId(), request.getProvider());
            return Result.success("添加成功", result);
        } catch (Exception e) {
            log.error("添加API密钥失败", e);
            return Result.errorTyped(500, "添加失败: " + e.getMessage());
        }
    }

    /**
     * 更新API密钥
     */
    @PutMapping("/api-keys/{keyId}")
    @Operation(summary = "更新API密钥", description = "更新指定的API密钥信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<ApiKeyDto> updateApiKey(
            @PathVariable @NotNull Long keyId,
            @Valid @RequestBody UpdateApiKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            UserApiKey apiKey = aiConfigService.updateApiKey(
                    userId,
                    keyId,
                    request.getKeyName(),
                    request.getApiKey(),
                    request.getPriority(),
                    request.getIsActive()
            );

            ApiKeyDto dto = convertToApiKeyDto(apiKey);

            log.info("更新API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
            return Result.success("更新成功", dto);
        } catch (Exception e) {
            log.error("更新API密钥失败", e);
            return Result.errorTyped(500, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除API密钥
     */
    @DeleteMapping("/api-keys/{keyId}")
    @Operation(summary = "删除API密钥", description = "删除指定的API密钥")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<String> deleteApiKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            boolean deleted = aiConfigService.deleteApiKey(userId, keyId);

            if (deleted) {
                log.info("删除API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("删除成功", "删除成功");
            } else {
                return Result.errorTyped(404, "API密钥不存在");
            }
        } catch (Exception e) {
            log.error("删除API密钥失败", e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 测试API密钥
     */
    @PostMapping("/api-keys/{keyId}/test")
    @Operation(summary = "测试API密钥", description = "测试指定API密钥的有效性")
    @ApiResponse(responseCode = "200", description = "测试完成")
    public Result<ApiKeyTestResult> testApiKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            ApiKeyTestResult result = aiConfigService.testApiKey(userId, keyId);

            log.info("测试API密钥完成 - 用户ID: {}, 密钥ID: {}, 结果: {}",
                    userId, keyId, result.isValid());
            return Result.success("测试完成", result);
        } catch (Exception e) {
            log.error("测试API密钥失败", e);
            return Result.errorTyped(500, "测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取API密钥负载统计
     */
    @GetMapping("/api-keys/{keyId}/stats")
    @Operation(summary = "获取API密钥负载统计", description = "获取指定API密钥的负载统计信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<LoadBalancerService.LoadBalanceStats> getApiKeyStats(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            // 这里应该验证用户权限，简化处理
            LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(keyId);

            if (stats != null) {
                log.debug("获取API密钥负载统计成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("获取成功", stats);
            } else {
                return Result.errorTyped(404, "统计信息不存在");
            }
        } catch (Exception e) {
            log.error("获取API密钥负载统计失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    // ========================
    // 私有辅助方法
    // ========================

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null) {
            throw new RuntimeException("用户未认证");
        }

        try {
            CustomUserDetails userDetails=(CustomUserDetails)authentication.getPrincipal();
            return userDetails.getUserId();
        } catch (NumberFormatException e) {
            throw new RuntimeException("无效的用户ID");
        }
    }

    /**
     * 转换为用户配置DTO
     */
    private UserConfigDto convertToUserConfigDto(UserAiConfig config) {
        UserConfigDto dto = new UserConfigDto();
        dto.setPreferredModel(config.getPreferredModel());
        dto.setDefaultTemperature(config.getDefaultTemperature());
        dto.setDefaultMaxTokens(config.getDefaultMaxTokens());
        dto.setDefaultTopP(config.getDefaultTopP());
        dto.setStreamEnabled(config.getStreamEnabled());
        dto.setTimeoutSeconds(config.getTimeoutSeconds());
        dto.setSystemPrompt(config.getSystemPrompt());
        return dto;
    }

    /**
     * 转换为用户配置实体
     */
    private UserAiConfig convertToUserAiConfig(UserConfigDto dto) {
        UserAiConfig config = new UserAiConfig();
        config.setPreferredModel(dto.getPreferredModel());
        config.setDefaultTemperature(dto.getDefaultTemperature());
        config.setDefaultMaxTokens(dto.getDefaultMaxTokens());
        config.setDefaultTopP(dto.getDefaultTopP());
        config.setStreamEnabled(dto.getStreamEnabled());
        config.setTimeoutSeconds(dto.getTimeoutSeconds());
        config.setSystemPrompt(dto.getSystemPrompt());
        return config;
    }

    /**
     * 转换为API密钥DTO
     */
    private ApiKeyDto convertToApiKeyDto(UserApiKey apiKey) {
        ApiKeyDto dto = new ApiKeyDto();
        dto.setId(apiKey.getId());
        dto.setProvider(apiKey.getProvider());
        dto.setKeyName(apiKey.getKeyName());
        dto.setMaskedApiKey(apiKey.getApiKeyEncrypted()); // 已经脱敏
        dto.setIsActive(apiKey.getIsActive());
        dto.setPriority(apiKey.getPriority());
        dto.setUsageCount(apiKey.getUsageCount());
        dto.setLastUsedAt(apiKey.getLastUsedAt());
        dto.setCreatedAt(apiKey.getCreatedAt());

        // 生成兼容格式的API密钥
        if (apiKey.getId() != null && apiKey.getUserId() != null) {
            dto.setCompatibleApiKey(aiConfigService.generateCompatibleApiKey(apiKey.getUserId(), apiKey.getId()));
        }

        // 获取负载统计信息
        try {
            LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(apiKey.getId());
            if (stats != null) {
                dto.setIsHealthy(stats.getIsHealthy());
                dto.setCurrentRequests(stats.getCurrentRequests());
                dto.setErrorCount(stats.getErrorCount());
                dto.setErrorRate(stats.getErrorRate());
            }
        } catch (Exception e) {
            log.warn("获取API密钥负载统计失败 - ID: {}", apiKey.getId(), e);
        }

        return dto;
    }

    /**
     * API密钥添加结果
     */
    public static class ApiKeyAddResult {
        private final Long keyId;
        private final String compatibleApiKey;
        private final String message;

        public ApiKeyAddResult(Long keyId, String compatibleApiKey, String message) {
            this.keyId = keyId;
            this.compatibleApiKey = compatibleApiKey;
            this.message = message;
        }

        public Long getKeyId() { return keyId; }
        public String getCompatibleApiKey() { return compatibleApiKey; }
        public String getMessage() { return message; }
    }
}
