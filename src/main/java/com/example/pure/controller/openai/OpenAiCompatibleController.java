package com.example.pure.controller.openai;

import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.service.openai.OpenAiRequestService;
import com.example.pure.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * OpenAI兼容API控制器
 * <p>
 * 提供完全兼容OpenAI API格式的端点，支持所有使用OpenAI API的AI工具
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
@Tag(name = "OpenAI兼容API", description = "完全兼容OpenAI API格式的端点")
public class OpenAiCompatibleController {

    private final OpenAiRequestService openAiRequestService;
    private final JwtUtil jwtUtil;

    /**
     * 聊天完成接口（兼容OpenAI）
     * <p>
     * 完全兼容OpenAI的/v1/chat/completions端点，支持以下功能：
     * - 自动识别流式/非流式请求（基于stream参数）
     * - 多模态内容支持（文本+图片）
     * - 多提供商支持（OpenAI、Claude、Gemini）
     * - 智能负载均衡和故障转移
     * - 推理过程提取（支持o1模型）
     * - 统一的错误处理和日志记录
     * </p>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li>多模态内容验证 - 检查图片格式、大小、数量限制</li>
     *   <li>请求信息记录 - 统计消息数量、图片数量等</li>
     *   <li>认证和参数验证 - 通过OpenAiRequestHandler统一处理</li>
     *   <li>API密钥选择 - 根据模型名称选择合适的提供商和密钥</li>
     *   <li>请求转发 - 调用对应的流式或非流式服务方法</li>
     *   <li>响应格式转换 - 统一转换为OpenAI兼容格式</li>
     * </ol>
     *
     * <h3>性能优化：</h3>
     * - 流式响应使用异步处理，避免阻塞主线程
     * - 多模态内容预验证，减少无效请求的资源消耗
     * - 智能负载均衡，提高API密钥利用率
     *
     * <h3>安全考虑：</h3>
     * - JWT令牌验证确保请求合法性
     * - 多模态内容安全检查，防止恶意图片上传
     * - 错误信息脱敏，避免泄露内部实现细节
     *
     * @param openAiChatRequest 聊天请求对象，包含模型、消息列表、参数配置等
     * @param httpRequest HTTP请求对象，用于提取认证信息和请求头
     * @return 根据stream参数返回不同类型：
     *         - stream=true: SseEmitter对象，用于流式响应
     *         - stream=false: ResponseEntity<OpenAiChatResponse>，完整响应
     * @throws IllegalArgumentException 当多模态内容验证失败时
     * @throws RuntimeException 当API密钥无效或服务调用失败时
     */
    @PostMapping(value = "/chat/completions", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    @Operation(
            summary = "聊天完成",
            description = "完全兼容OpenAI API的聊天完成接口，根据stream参数自动选择流式或非流式响应"
    )
    @ApiResponse(responseCode = "200", description = "成功")
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @ApiResponse(responseCode = "401", description = "认证失败")
    @ApiResponse(responseCode = "429", description = "请求频率限制")
    @ApiResponse(responseCode = "500", description = "服务器内部错误")
    public Object chatCompletions(
            @Valid @RequestBody OpenAiChatRequest openAiChatRequest,
            HttpServletRequest httpRequest) {

        // 1. 认证
        String apiKey = extractAndValidateApiKey(httpRequest);
        if (apiKey == null) {
            if (Boolean.TRUE.equals(openAiChatRequest.getStream())) {
                return createStreamErrorResponse("Missing or invalid authorization header");
            }
            return createUnauthorizedResponse();
        }

        // 2. 调用服务层处理业务逻辑
        try {
            Object result = openAiRequestService.processChatCompletions(apiKey, openAiChatRequest);

            // 3. 根据返回类型包装响应
            if (Boolean.TRUE.equals(openAiChatRequest.getStream())) {
                // 流式响应直接返回SseEmitter
                return result;
            } else {
                // 非流式响应包装为ResponseEntity
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(result);
            }
        } catch (IllegalArgumentException e) {
            log.warn("请求参数验证失败: {}", e.getMessage());
            if (Boolean.TRUE.equals(openAiChatRequest.getStream())) {
                return createStreamErrorResponse(e.getMessage());
            }
            return ResponseEntity.status(400).body(createErrorResponse(e.getMessage()));
        } catch (Exception e) {
            log.error("处理聊天完成请求失败", e);
            if (Boolean.TRUE.equals(openAiChatRequest.getStream())) {
                return createStreamErrorResponse("Internal server error: " + e.getMessage());
            }
            return ResponseEntity.status(500).body(createErrorResponse("Internal server error: " + e.getMessage()));
        }
    }



    /**
     * 获取模型列表接口（兼容OpenAI）
     * <p>
     * 完全兼容OpenAI的/v1/models端点
     * </p>
     *
     * @param httpRequest HTTP请求对象
     * @return 模型列表响应
     */
    @GetMapping("/models")
    @Operation(
            summary = "获取模型列表",
            description = "完全兼容OpenAI API的模型列表接口"
    )
    @ApiResponse(responseCode = "200", description = "成功")
    @ApiResponse(responseCode = "401", description = "认证失败")
    @ApiResponse(responseCode = "500", description = "服务器内部错误")
    public ResponseEntity<?> listModels(HttpServletRequest httpRequest) {

        // 1. 认证
        String apiKey = extractAndValidateApiKey(httpRequest);
        if (apiKey == null) {
            return createUnauthorizedResponse();
        }

        // 2. 调用服务层处理业务逻辑
        try {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(openAiRequestService.processModelList(apiKey));
        } catch (Exception e) {
            log.error("获取模型列表失败", e);
            return ResponseEntity.status(500).body(createErrorResponse("Internal server error: " + e.getMessage()));
        }
    }

    /**
     * 图片生成接口（兼容OpenAI）
     * <p>
     * 完全兼容OpenAI的/v1/images/generations端点，支持以下功能：
     * - 多提供商支持（OpenAI DALL-E、Google Imagen）
     * - 智能模型选择和负载均衡
     * - 参数兼容性验证
     * - 统一的错误处理和响应格式
     * - 不支持Claude（Claude不提供图片生成功能）
     * </p>
     *
     * <h3>支持的模型：</h3>
     * <ul>
     *   <li><strong>OpenAI:</strong> dall-e-2, dall-e-3, gpt-image-1</li>
     *   <li><strong>Google:</strong> imagen-3.0-generate-001, imagen-3.0-fast-generate-001</li>
     *   <li><strong>不支持:</strong> Claude系列模型（会返回错误）</li>
     * </ul>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li>认证验证 - 通过OpenAiRequestHandler统一处理JWT令牌</li>
     *   <li>参数验证 - 验证prompt、size、quality等参数的有效性</li>
     *   <li>模型兼容性检查 - 确保请求的模型支持图片生成</li>
     *   <li>API密钥选择 - 根据模型名称选择合适的提供商和密钥</li>
     *   <li>请求转发 - 调用对应提供商的图片生成API</li>
     *   <li>响应格式转换 - 统一转换为OpenAI兼容的响应格式</li>
     * </ol>
     *
     * <h3>性能考虑：</h3>
     * - 图片生成通常耗时较长，使用同步处理但设置合理的超时时间
     * - 负载均衡确保多个API密钥的合理使用
     * - 错误重试机制提高成功率
     *
     * <h3>安全限制：</h3>
     * - 严格的prompt内容过滤，防止生成不当内容
     * - 图片尺寸和数量限制，防止资源滥用
     * - API密钥权限验证，确保用户有图片生成权限
     *
     * @param openAiImageRequest 图片生成请求对象，包含prompt、模型、尺寸、数量等参数
     * @param httpRequest HTTP请求对象，用于提取认证信息
     * @return ResponseEntity<OpenAiImageResponse> 包含生成的图片URL列表和元数据
     * @throws IllegalArgumentException 当模型不支持图片生成或参数无效时
     * @throws RuntimeException 当API密钥无效、提供商服务异常或生成失败时
     */
    @PostMapping("/images/generations")
    @Operation(
            summary = "图片生成",
            description = "完全兼容OpenAI API的图片生成接口，支持DALL-E模型"
    )
    @ApiResponse(responseCode = "200", description = "成功")
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @ApiResponse(responseCode = "401", description = "认证失败")
    @ApiResponse(responseCode = "429", description = "请求频率限制")
    @ApiResponse(responseCode = "500", description = "服务器内部错误")
    public ResponseEntity<?> generateImages(
            @Valid @RequestBody OpenAiImageRequest openAiImageRequest,
            HttpServletRequest httpRequest) {

        // 1. 认证
        String apiKey = extractAndValidateApiKey(httpRequest);
        if (apiKey == null) {
            return createUnauthorizedResponse();
        }

        // 2. 调用服务层处理业务逻辑
        try {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(openAiRequestService.processImageGeneration(apiKey, openAiImageRequest));
        } catch (IllegalArgumentException e) {
            log.warn("请求参数验证失败: {}", e.getMessage());
            return ResponseEntity.status(400).body(createErrorResponse(e.getMessage()));
        } catch (Exception e) {
            log.error("处理图片生成请求失败", e);
            return ResponseEntity.status(500).body(createErrorResponse("Internal server error: " + e.getMessage()));
        }
    }

    /**
     * 文本嵌入接口（计划支持）
     * <p>
     * 完全兼容OpenAI的/v1/embeddings端点
     * </p>
     *
     * @param httpRequest HTTP请求对象
     * @return 嵌入响应
     */
    @PostMapping("/embeddings")
    @Operation(
            summary = "文本嵌入",
            description = "完全兼容OpenAI API的文本嵌入接口（计划支持）"
    )
    @ApiResponse(responseCode = "501", description = "功能暂未实现")
    public ResponseEntity<?> createEmbeddings(HttpServletRequest httpRequest) {
        log.warn("文本嵌入接口暂未实现");
        return ResponseEntity.status(501).body(createErrorResponse("Embeddings endpoint not implemented yet"));
    }

    /**
     * 健康检查接口
     * <p>
     * 检查OpenAI兼容API的可用性
     * </p>
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    @Operation(
            summary = "健康检查",
            description = "检查OpenAI兼容API的可用性"
    )
    @ApiResponse(responseCode = "200", description = "服务正常")
    public ResponseEntity<?> healthCheck() {
        return ResponseEntity.ok().body(createSuccessResponse("OpenAI Compatible API is healthy"));
    }

    // ========================
    // 私有辅助方法
    // ========================

    /**
     * 通用认证方法
     * 从Authorization头中提取并验证API密钥
     *
     * @param httpRequest HTTP请求对象
     * @return 提取的API密钥，如果认证失败则返回null
     */
    private String extractAndValidateApiKey(HttpServletRequest httpRequest) {
        String authHeader = httpRequest.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return null;
        }

        // 使用JwtUtil的TokenCheck方法提取token
        return jwtUtil.TokenCheck(authHeader);
    }

    /**
     * 创建认证失败的响应
     */
    private ResponseEntity<?> createUnauthorizedResponse() {
        return ResponseEntity.status(401).body(createErrorResponse("Missing or invalid authorization header"));
    }

    /**
     * 创建流式错误响应
     */
    private SseEmitter createStreamErrorResponse(String message) {
        SseEmitter emitter = new SseEmitter(30000L);
        try {
            emitter.send(SseEmitter.event()
                    .name("error")
                    .data(createErrorResponse(message)));
            emitter.complete();
        } catch (Exception e) {
            emitter.completeWithError(e);
        }
        return emitter;
    }

    /**
     * 创建OpenAI格式的错误响应
     */
    private Object createErrorResponse(String message) {
        return new ErrorResponse("error", message, "invalid_request_error", null, null);
    }

    /**
     * 创建成功响应
     */
    private Object createSuccessResponse(String message) {
        return new SuccessResponse("success", message);
    }



    /**
     * OpenAI格式错误响应
     */
    private static class ErrorResponse {
        private final String type;
        private final String message;
        private final String code;
        private final String param;
        private final String details;

        public ErrorResponse(String type, String message, String code, String param, String details) {
            this.type = type;
            this.message = message;
            this.code = code;
            this.param = param;
            this.details = details;
        }

        // Getters for JSON serialization
        public String getType() { return type; }
        public String getMessage() { return message; }
        public String getCode() { return code; }
        public String getParam() { return param; }
        public String getDetails() { return details; }
    }

    /**
     * 成功响应格式
     */
    private static class SuccessResponse {
        public final String status;
        public final String message;

        public SuccessResponse(String status, String message) {
            this.status = status;
            this.message = message;
        }
    }



}
