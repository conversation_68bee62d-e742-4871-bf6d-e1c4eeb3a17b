package com.example.pure.controller.file.user;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.file.user.FileMetadata;
import com.example.pure.service.file.user.FilePureService;
import com.example.pure.util.FileValidationUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * 图片控制器
 * <p>
 * 提供图片查看功能的API接口
 * </p>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "图片服务", description = "图片流式查看相关接口")
public class ImageController {

    private final FilePureService filePureService;

    /**
     * 处理图片查看请求的端点（普通实现）
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_image.jpg）
     * @param headers  请求头，用于处理范围请求（Range Requests）
     * @return 包含图片数据的 ResponseEntity
     * @deprecated 推荐使用 {@link #viewImageAsync(String, HttpHeaders)} 代替
     */
    @GetMapping("/image-general/{fileName:.+}")
    @Operation(summary = "图片查看（普通实现）", description = "使用普通方式支持图片加载和范围请求")
    public ResponseEntity<Resource> viewImage(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("请求图片查看（普通方式）: {}", fileName);
            return filePureService.streamVideo(fileName, headers);
        } catch (IOException e) {
            log.error("处理图片查看时发生错误: {}", fileName, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 处理图片查看请求的端点（异步实现）
     * <p>
     * 使用ResponseBodyEmitter异步发送图片数据，提高性能和资源利用率
     * </p>
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_image.jpg）
     * @param headers  请求头，用于处理范围请求（Range Requests）
     * @return ResponseEntity包含ResponseBodyEmitter，用于异步发送图片数据
     */
    @GetMapping("/image/{fileName:.+}")
    @Operation(summary = "图片查看（异步）", description = "使用异步方式支持图片加载和范围请求，提高性能")
    public ResponseEntity<ResponseBodyEmitter> viewImageAsync(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("请求图片查看（异步）: {}", fileName);

            // 1. 获取文件元数据（只调用一次）
            FileMetadata metadata;
            try {
                metadata = filePureService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw BusinessException.of(ResponseCode.DATA_NOT_FOUND, "无法获取图片信息: " + e.getMessage());
            }

            // 使用工具类验证文件元数据
            FileValidationUtil.validateImageMetadata(metadata, fileName);

            // 2. 使用基于元数据的方法构建响应头（避免重复调用getFileMetadata）
            HttpHeaders responseHeaders = filePureService.buildResponseHeaders(metadata, headers, "image/jpeg");

            // 3. 检查Range请求状态（具体Range处理由异步方法处理）
            String range = headers.getFirst(HttpHeaders.RANGE);
            HttpStatus status = (range != null && range.startsWith("bytes=")) ?
                HttpStatus.PARTIAL_CONTENT : HttpStatus.OK;

            // 4. 获取异步发送器
            CompletableFuture<ResponseBodyEmitter> emitterFuture = filePureService.streamFileAsync(fileName, headers);

            // 处理异步返回结果
            ResponseBodyEmitter emitter = emitterFuture.getNow(null);
            if (emitter != null) {
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter);
            } else {
                // 如果emitter还没准备好，等待它完成，阻塞并获取已完成状态的值
                ResponseBodyEmitter emitter2 = emitterFuture.join();
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter2);
            }

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理图片查看时发生错误: {}", fileName, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 根据图片ID获取图片流的API接口（异步实现）
     * <p>
     * 此接口允许通过图片标识符而不是直接文件名获取图片
     * </p>
     *
     * @param imageId 图片标识符
     * @param headers 请求头，用于处理范围请求（Range Requests）
     * @return ResponseEntity包含ResponseBodyEmitter，用于异步发送图片数据
     */
    @GetMapping("/api/images/view/{imageId}")
    @Operation(summary = "根据ID查看图片", description = "通过图片ID访问图片，支持分段加载和范围请求")
    public ResponseEntity<ResponseBodyEmitter> viewImageByIdAsync(
            @PathVariable String imageId,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("根据ID请求图片: {}", imageId);

            // 1. 根据图片ID映射到实际文件名（在实际应用中，应从数据库中获取）
            String fileName = mapImageIdToFileName(imageId);

            // 2. 获取文件元数据（只调用一次）
            FileMetadata metadata;
            try {
                metadata = filePureService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw BusinessException.of(ResponseCode.DATA_NOT_FOUND, "无法获取图片信息: " + e.getMessage());
            }

            // 使用工具类验证文件元数据
            FileValidationUtil.validateImageMetadata(metadata, imageId, fileName);

            // 3. 使用基于元数据的方法构建响应头（避免重复调用getFileMetadata）
            HttpHeaders responseHeaders = filePureService.buildResponseHeaders(metadata, headers, "image/jpeg");

            // 4. 检查Range请求状态
            String range = headers.getFirst(HttpHeaders.RANGE);
            HttpStatus status = (range != null && range.startsWith("bytes=")) ?
                HttpStatus.PARTIAL_CONTENT : HttpStatus.OK;

            // 5. 获取异步发送器
            CompletableFuture<ResponseBodyEmitter> emitterFuture = filePureService.streamFileAsync(fileName, headers);

            // 处理异步返回结果
            ResponseBodyEmitter emitter = emitterFuture.getNow(null);
            if (emitter != null) {
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter);
            } else {
                // 如果emitter还没准备好，等待它完成
                ResponseBodyEmitter emitter2 = emitterFuture.join();
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter2);
            }

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理图片查看时发生错误: ID={}", imageId, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 获取图片信息的API接口
     *
     * @param imageId 图片标识符
     * @return 图片信息响应
     */
    @GetMapping("/api/images/info/{imageId}")
    @Operation(summary = "获取图片信息", description = "获取图片文件的元数据信息，如大小、类型等")
    public Result<ImageInfo> getImageInfo(@PathVariable String imageId) {
        try {
            log.debug("请求图片信息: ID={}", imageId);

            // 根据图片ID映射到实际文件名
            String fileName = mapImageIdToFileName(imageId);

            // 获取文件元数据
            FileMetadata metadata;
            try {
                metadata = filePureService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw new BusinessException(ResponseCode.DATA_NOT_FOUND, "无法获取图片信息: " + e.getMessage());
            }

            // 检查文件是否存在和可读
            if (!metadata.isExists() || !metadata.isReadable()) {
                log.error("请求的图片不存在或不可读: ID={}, 文件={}", imageId, fileName);
                throw new BusinessException(ResponseCode.NOT_FOUND, "图片不存在或不可读");
            }

            // 构建返回信息
            ImageInfo info = new ImageInfo();
            info.setImageId(imageId);
            info.setFileName(fileName);
            info.setFileSize(metadata.getFileSize());
            info.setContentType(metadata.getContentType());
            info.setViewUrl("/api/images/view/" + imageId);

            return Result.success("获取图片信息成功", info);

        } catch (Exception e) {
            log.error("获取图片信息失败: ID={}", imageId, e);
            throw new BusinessException(ResponseCode.OPERATION_FAILED, "获取图片信息失败: " + e.getMessage());
        }
    }

    /**
     * 将图片ID映射到文件名（示例实现）
     * <p>
     * 在实际应用中，应该从数据库中查询图片记录以获取正确的文件名
     * </p>
     *
     * @param imageId 图片ID
     * @return 对应的文件名
     */
    private String mapImageIdToFileName(String imageId) {
        // 示例实现，根据图片ID前缀确定图片类型
        // 实际应用中应该查询数据库获取正确的文件名
        if (imageId.startsWith("logo")) {
            return "logo.png";
        } else if (imageId.startsWith("banner")) {
            return "banner.jpg";
        } else if (imageId.startsWith("avatar")) {
            return "avatar.jpg";
        } else if (imageId.startsWith("product")) {
            return "product.jpg";
        } else {
            // 默认返回测试图片
            return "sample.jpg";
        }
    }

    /**
     * 图片信息数据传输对象
     */
    @Data
    public static class ImageInfo {
        private String imageId;      // 图片ID
        private String fileName;     // 文件名
        private long fileSize;       // 文件大小（字节）
        private String contentType;  // 内容类型
        private String viewUrl;      // 查看URL
    }
}
