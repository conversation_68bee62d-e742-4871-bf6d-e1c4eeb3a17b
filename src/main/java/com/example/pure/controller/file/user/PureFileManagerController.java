package com.example.pure.controller.file.user;


import com.example.pure.common.Result;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.file.user.FileUploadService;
import com.example.pure.service.userprofile.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/api/file")
public class PureFileManagerController {
    private final FileUploadService fileUploadService;
    private final UserProfileService userProfileService;

    @Value("${file.storage.location.upload}")
    private String fileStorageLocation;

    @Value("${file.storage.max-size}") // 默认1GB (1024 * 1024 * 1024)
    private long maxFileSize;

    @Autowired
    public PureFileManagerController(
            FileUploadService fileUploadService,
            UserProfileService userProfileService
    ) {
        this.fileUploadService = fileUploadService;
        this.userProfileService = userProfileService;
    }

    /**
     * 上传文件（同步实现）
     *
     * @param file 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @param authentication 当前认证信息
     * @return 上传结果
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(
            //查询请求体的key为file的值反序列化到MultipartFile的实例file里
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type,
            Authentication authentication) {

        // 获取当前用户信息
        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        String username = customUserDetails.getUsername();

        // 验证上传文件
        fileUploadService.validateUploadFile(file, maxFileSize);

        // 调用图片上传服务处理文件上传和头像更新
        Map<String, Object> fileInfo = fileUploadService.uploadImageAndUpdateAvatar(file, type, username);

        return Result.success("文件上传成功", fileInfo);
    }

}
