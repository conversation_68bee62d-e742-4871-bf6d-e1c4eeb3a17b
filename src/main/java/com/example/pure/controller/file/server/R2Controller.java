package com.example.pure.controller.file.server;

import com.example.pure.common.Result;
import com.example.pure.model.dto.request.file.server.R2PresignRequest;
import com.example.pure.model.dto.request.file.server.R2GetUrlRequest;
import com.example.pure.service.file.server.R2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api")
public class R2Controller {

    @Autowired
    private R2Service r2Service;



    /**
     * 获取预签名上传URL
     * <p>
     * 此接口用于生成预签名的上传URL，允许客户端直接向R2存储上传文件，
     * 无需通过服务器中转，提高上传效率并减少服务器负载。
     *
     * @param request 包含文件名和内容类型的请求对象
     * @return 包含预签名上传URL的响应
     */
    @PostMapping("/presigned-url")
    public Result<Map<String, String>> getPresignedUrl(@RequestBody R2PresignRequest request) {
        String url = r2Service.generatePresignedUrl(request.getFileName(), request.getContentType());
        return Result.success("获取预签名URL成功", Map.of("uploadUrl", url));
    }

    /**
     * 获取临时访问URL
     * <p>
     * 此接口用于为存储在R2中的私有文件生成临时访问URL。
     * 适用于需要控制访问权限的场景，如：
     * - 付费内容的临时访问
     * - 私人文件的安全分享
     * - 需要时效性控制的文件下载
     * <p>
     * 生成的URL具有时效性，过期后将无法访问，确保文件访问的安全性。
     *
     * @param request 包含对象键（文件路径）的请求对象
     * @return 包含临时访问URL的响应
     */
    @PostMapping("/temporary-access-url")
    public Result<Map<String, String>> getTemporaryAccessUrl(@RequestBody R2GetUrlRequest request) {
        String url = r2Service.generatePresignedGetUrl(request.getObjectKey());
        return Result.success("获取临时访问URL成功", Map.of("accessUrl", url));
    }

}
