package com.example.pure.controller.file.user;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.file.user.FileMetadata;
import com.example.pure.service.file.user.FilePureService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 文件下载控制器
 * <p>
 * 提供文件下载功能的API接口
 * </p>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "文件下载", description = "文件下载相关接口")
public class DownloadController {

    private final FilePureService filePureService;

    /**
     * 处理文件下载请求的端点（传统实现）
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_document.pdf）
     * @param headers  请求头
     * @return 包含文件资源的 ResponseEntity
     * @deprecated 推荐使用 {@link #downloadFileAsync(String, HttpHeaders)} 代替
     */
    @GetMapping("/download-general/{fileName:.+}")
    @Operation(summary = "文件下载（传统实现）", description = "使用传统方式下载文件")
    public ResponseEntity<Resource> downloadFile(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("请求文件下载（传统方式）: {}", fileName);
            return filePureService.downloadFile(fileName, headers);
        } catch (IOException e) {
            log.error("处理文件下载时发生错误: {}", fileName, e);
            // 创建包含错误信息的业务异常
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "文件下载失败: " + e.getMessage());
        }
    }

    /**   最佳实践说明
     * 1、多线程异步的需要获取数据时候，主线程调用线程池单个子线程@async调用线程池令子线程们（@async子线程
     *    运行除了runAsync多线程方法外的代码）运行异步任务，主线程生成承诺未来完成类（未完成状态）等待兑现，
     *    主线程阻塞等待子线程，完成任务填充数据，然后主线程再从承诺未来完成类（已完成）获取数据
     *
     * 2、多线程异步处理分块传输任务时，主线程调用线程池线程们运行异步任务，主线程生成承诺未来完成类（未完成状态），这时候
     *    async调用的子线程返回一个承诺未来完成类（已完成状态，存放着未处理的存放二进制字节数组数据的类），主线程的未来完成状态类
     *    就会变成已完成状态，然后调用getNow方法获取暂时未处理的二进制数据类，然后由于类不是null,所以执行if语句主线程返回
     *    数据给客户端，这时候和客户端形成握手连接，线程池同时控制（除了@async调用的子线程）多个子线程持续返回数据给客户端
     *
     * 3. 多线程异步不需要返回数据给主线程时候，不需要承诺未来完成类，不需要返回数据，让多线程异步子线程自动处理数据
     *
     * 处理文件下载请求的端点（异步实现）
     * <p>
     * 使用ResponseBodyEmitter异步发送文件数据，提高性能和资源利用率
     * </p>
     *
     * @param fileName 路径参数，包含文件名和扩展名（例如：my_document.pdf）
     * @param headers  请求头
     * @return ResponseEntity包含ResponseBodyEmitter，用于异步发送文件数据
     */
    @GetMapping("/download/{fileName:.+}")
    @Operation(summary = "文件下载（异步）", description = "使用异步方式下载文件，提高性能")
    public ResponseEntity<ResponseBodyEmitter> downloadFileAsync(
            @PathVariable String fileName,
            @RequestHeader HttpHeaders headers) {

        try {
            log.debug("请求文件下载（异步）: {}", fileName);

            // 1. 获取文件元数据
            FileMetadata metadata;
            try {
                metadata = filePureService.getFileMetadata(fileName);
            } catch (IOException e) {
                log.error("获取文件元数据失败: {}", fileName, e);
                throw BusinessException.of(ResponseCode.DATA_NOT_FOUND, "无法获取文件信息: " + e.getMessage());
            }

            if (!metadata.isExists() || !metadata.isReadable()) {
                log.error("请求的文件不存在或不可读: {}", fileName);
                throw BusinessException.of(ResponseCode.NOT_FOUND, "文件不存在或不可读: " + fileName);
            }

            // 2. 使用Service构建下载专用响应头
            HttpHeaders responseHeaders = filePureService.buildDownloadResponseHeaders(fileName, headers);

            // 3. 检查Range请求状态
            String range = headers.getFirst(HttpHeaders.RANGE);
            HttpStatus status = (range != null && range.startsWith("bytes=")) ?
                HttpStatus.PARTIAL_CONTENT : HttpStatus.OK;

            // 3. 获取异步发送器，传递HTTP头信息
            // 配置流传输参数，包括16KB(16384字节)的缓冲区大小，并标记为下载模式
            Map<String, Object> streamOptions = new HashMap<>();
            streamOptions.put("bufferSize", 8192*2); // 16KB是一个良好的平衡点
            streamOptions.put("isDownload", true); // 标记为下载模式，启用文件类型白名单检查

            CompletableFuture<ResponseBodyEmitter> emitterFuture = filePureService.streamFileAsync(fileName, headers, streamOptions);

            /*
                主线程调用子线程去异步处理然后回来接着运行下面的代码
                处理异步返回结果，由于返回的已完成状态的对象所以包含ResponseBodyEmitter对象，所以不是null，
                直接返回http响应给Spring Mvc然后和客户端建立长连接，并将响应体的空的ResponseBodyEmitter对象与
                改连接关联，子线程通过emitter.send(dataChunk);持续发送数据给客户端
             */

            // 多线程异步分块传输的逻辑
            ResponseBodyEmitter emitter = emitterFuture.getNow(null); // 如果是完成状态返回结果值，否则返回()里的值
            if (emitter != null) {
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter);
            } else {
                // 如果emitter还没准备好，等待它完成，并获得值。
                ResponseBodyEmitter emitter2 = emitterFuture.join(); // 正常的多线程异步用法，等待类变成完成状态并获取值
                return ResponseEntity
                        .status(status)
                        .headers(responseHeaders)
                        .body(emitter2);
            }

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            // 处理所有异常，包括ClientAbortException
            if (e instanceof org.apache.catalina.connector.ClientAbortException) {
                // 客户端中断连接的特殊处理
                log.warn("客户端终止了下载连接: {}, 原因: {}", fileName, e.getMessage());
                // 这是客户端行为，不需要向客户端发送错误响应
                throw BusinessException.of(ResponseCode.CLIENT_CLOSED_REQUEST, "客户端中断了下载");
            } else {
                log.error("处理文件下载时发生错误: {}", fileName, e);
                throw BusinessException.of(ResponseCode.OPERATION_FAILED, "文件下载失败: " + e.getMessage());
            }
        }
    }

    /**
     * 检查文件可用性的API接口
     *
     * @param fileName 文件名
     * @return 文件可用性响应
     */
    @GetMapping("/api/files/check/{fileName:.+}")
    @Operation(summary = "检查文件可用性", description = "检查指定文件是否存在并可下载")
    public Result<Object> checkFileAvailability(@PathVariable String fileName) {
        try {
            log.debug("检查文件可用性: {}", fileName);

            // 获取文件元数据
            FileMetadata metadata = filePureService.getFileMetadata(fileName);

            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("fileName", metadata.getFileName());
            data.put("fileSize", metadata.getFileSize());
            data.put("exists", metadata.isExists());
            data.put("readable", metadata.isReadable());
            data.put("contentType", metadata.getContentType());

            if (metadata.isExists() && metadata.isReadable()) {
                data.put("downloadUrl", "/download/" + fileName);
                return Result.success("文件可用", data);
            } else {
                return Result.error(ResponseCode.DATA_NOT_FOUND, "文件不存在或不可读", data);
            }

        } catch (BusinessException e) {
            // 直接使用业务异常中的错误码和消息
            throw new BusinessException(e.getCode(), e.getMessage());
        } catch (IOException e) {
            log.error("获取文件元数据失败: {}", fileName, e);
            throw new BusinessException(ResponseCode.DATA_NOT_FOUND, "无法获取文件信息: " + e.getMessage());
        } catch (Exception e) {
            log.error("检查文件可用性时发生错误: {}", fileName, e);
            throw new BusinessException(ResponseCode.STATUS_ERROR, "检查文件失败: " + e.getMessage());
        }
    }
}
