package com.example.pure.controller.file.user;

import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.file.user.FileMetadata;
import com.example.pure.service.file.user.FilePureService;
import com.example.pure.service.file.user.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 文件管理控制器
 * <p>
 * 提供文件上传、列表查询和管理等功能
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "文件上传和管理相关接口")
public class FileManagerController {

    private final FilePureService filePureService;
    private final FileUploadService fileUploadService;

    @Value("${file.storage.location.upload}")
    private String fileStorageLocation;

    @Value("${file.storage.max-size}") // 默认1GB (1024 * 1024 * 1024)
    private long maxFileSize;



    /**
     * 上传文件（同步实现）
     *
     * @param currentFile 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @return 上传结果
     * @deprecated 推荐使用 {@link #uploadFileAsync(MultipartFile, String)} 代替
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件（传统实现）", description = "同步方式上传文件到服务器")
    public Result<Map<String, Object>> uploadFile(
            //查询请求体的key为file的值反序列化到MultipartFile的实例currentFile里
            @RequestParam("file") MultipartFile currentFile,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        // 获取当前用户名
        String username = getCurrentUsername();

        // 验证上传文件
        fileUploadService.validateUploadFile(currentFile, maxFileSize);

        // 调用上传服务处理文件上传
        Map<String, Object> fileInfo = fileUploadService.uploadFile(currentFile, type, username);

        return Result.success("文件上传成功", fileInfo);
    }

    /**
     * 传输文件和元数据: multipart/form-data 的核心优势在于它可以在同一个 HTTP 请求中同时传输二进制文件数据和其他文本形
     * 式的参数（如你的 type 参数，或者文件描述、用户 ID 等）。每一部分（文件或文本字段）都有自己的Content-Disposition头来标识名称和（如果是文件）原始文件名。
     *
     * 异步上传文件
     * <p>
     * 使用异步方式处理文件上传，提高服务器性能
     * </p>
     *
     * @param file 上传的文件
     * @param type 文件类型（可选，用于分类存储）
     * @return 上传结果，包含文件上传进度和状态
     */
    @PostMapping("/upload/async")
    @Operation(summary = "上传文件（异步）", description = "异步方式上传文件到服务器，提高性能")
    public Result<Map<String, Object>> uploadFileAsync(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        // 获取当前用户名
        String username = getCurrentUsername();

        // 验证上传文件
        fileUploadService.validateUploadFile(file, maxFileSize);

        try {
            // 调用异步上传服务处理文件上传
            CompletableFuture<Map<String, Object>> uploadResult = fileUploadService.uploadFileAsync(file, type, username);
            Map<String, Object> fileInfo = uploadResult.get();

            return Result.success("文件上传请求已接收", fileInfo);
        } catch (Exception e) {
            log.error("处理文件上传请求失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理文件上传请求失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前认证用户的用户名，从认证过的用户上下文获取用户名
     *
     * @return 当前用户名，如未认证则返回"anonymous"
     */
    private String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() &&
            !"anonymousUser".equals(authentication.getPrincipal())) {
            return authentication.getName();
        }
        return "anonymous";
    }



    /**
     * 获取文件列表
     *
     * @param type 文件类型（可选，用于过滤）
     * @return 文件列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "获取指定类型的文件列表")
    public Result<List<Map<String, Object>>> getFileList(
            @RequestParam(value = "type", required = false) String type) {

        try {
            // 获取当前用户名
            String username = getCurrentUsername();

            // 构建文件存储路径
            Path rootPath;
            if (type != null && !type.isEmpty()) {
                rootPath = Paths.get(fileStorageLocation, username, type).toAbsolutePath().normalize();
            } else {
                rootPath = Paths.get(fileStorageLocation, username).toAbsolutePath().normalize();
            }

            // 确保目录存在
            if (!Files.exists(rootPath)) {
                Files.createDirectories(rootPath);
            }

            // 获取文件列表
            List<Map<String, Object>> fileList = new ArrayList<>();

            Files.list(rootPath).forEach(path -> {
                if (Files.isRegularFile(path)) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    String fileName = path.getFileName().toString();
                    try {
                        fileInfo.put("name", fileName);
                        fileInfo.put("size", Files.size(path));
                        fileInfo.put("lastModified", Files.getLastModifiedTime(path).toMillis());
                        fileInfo.put("contentType", filePureService.determineContentType(path));
                        fileInfo.put("path", path.toString());
                        fileInfo.put("downloadUrl", "/download/" + fileName);
                        fileList.add(fileInfo);
                    } catch (IOException e) {
                        log.error("读取文件信息失败: {}", fileName, e);
                    }
                }
            });

            return Result.success("获取文件列表成功", fileList);
        } catch (IOException e) {
            log.error("获取文件列表失败", e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 异步获取文件详细信息
     *
     * @param filename 文件名
     * @param type 文件类型（用于定位文件位置）
     * @return 文件详细信息
     */
    @GetMapping("/info/{filename:.+}")
    @Operation(summary = "获取文件详细信息", description = "获取指定文件的详细元数据信息")
    public Result<Map<String, Object>> getFileInfo(
            @PathVariable String filename,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        try {
            // 安全检查
            if (!filePureService.isValidFileName(filename)) {
                throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
            }

            // 获取当前用户名
            String username = getCurrentUsername();

            // 先检查文件是否存在
            Path filePath = Paths.get(fileStorageLocation, username, type, filename).toAbsolutePath().normalize();
            if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
                throw BusinessException.of(ResponseCode.NOT_FOUND, "文件不存在");
            }

            // 异步获取文件元数据
            CompletableFuture<FileMetadata> metadataFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return filePureService.getFileMetadata(filename);
                } catch (IOException e) {
                    throw new RuntimeException("获取文件元数据失败", e);
                }
            });

            // 处理异步结果
            FileMetadata metadata = metadataFuture.join();

            // 构建响应数据
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("name", metadata.getFileName());
            fileInfo.put("size", metadata.getFileSize());
            fileInfo.put("contentType", metadata.getContentType());
            fileInfo.put("exists", metadata.isExists());
            fileInfo.put("readable", metadata.isReadable());
            fileInfo.put("downloadUrl", "/download/" + filename);
            fileInfo.put("type", type);

            return Result.success("获取文件信息成功", fileInfo);

        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", filename, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param filename 文件名
     * @param type 文件类型（用于定位文件位置）
     * @return 删除结果
     */
    @DeleteMapping("/{filename:.+}")
    @Operation(summary = "删除文件", description = "删除指定的文件")
    public Result<Void> deleteFile(
            @PathVariable String filename,
            @RequestParam(value = "type", required = false, defaultValue = "default") String type) {

        // 安全检查
        if (!filePureService.isValidFileName(filename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }

        try {
            // 获取当前用户名
            String username = getCurrentUsername();

            // 构建文件路径
            Path filePath = Paths.get(fileStorageLocation, username, type, filename).toAbsolutePath().normalize();

            // 验证文件是否存在
            if (!Files.exists(filePath) || !Files.isRegularFile(filePath)) {
                throw BusinessException.of(ResponseCode.NOT_FOUND, "文件不存在");
            }

            // 异步删除文件
            CompletableFuture.runAsync(() -> {
                try {
                    Files.delete(filePath);
                    log.info("文件删除成功: {}", filename);
                } catch (IOException e) {
                    log.error("异步文件删除失败: {}", filename, e);
                }
            });

            return Result.success("文件删除请求已接收");
        } catch (BusinessException e) {
            throw e; // 直接重新抛出业务异常
        } catch (Exception e) {
            log.error("处理文件删除请求失败: {}", filename, e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "处理文件删除请求失败: " + e.getMessage());
        }
    }

}
