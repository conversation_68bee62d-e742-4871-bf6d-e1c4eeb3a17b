package com.example.pure.controller.videocomment;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.Result;
import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.request.videocomment.CommentLikeRequest;
import com.example.pure.model.dto.request.videocomment.VideoComentsPageRequest;
import com.example.pure.model.dto.request.videocomment.VideoCommentRequest;
import com.example.pure.model.dto.response.videocomment.CommentLikeInfoDTO;
import com.example.pure.model.dto.response.videocomment.VideoCommentsDTO;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.videocomment.VideoCommentInteractionService;
import com.example.pure.util.TextVerifyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 视频评论互动控制器
 * <p>
 * 提供与视频评论互动相关的功能，例如发表评论、删除评论、点赞/踩评论等。
 * </p>
 */
@RestController
@RequestMapping("/api/video/comments")
public class VideoCommentInteractionController {

    private final VideoCommentInteractionService videoCommentInteractionService;

    @Autowired
    public VideoCommentInteractionController(VideoCommentInteractionService videoCommentInteractionService) {
        this.videoCommentInteractionService = videoCommentInteractionService;
    }

    // 用户提交评论,创建评论接口
    @PostMapping
    public Result<Void> createComment(@RequestBody VideoCommentRequest videoCommentRequest, @AuthenticationPrincipal CustomUserDetails userDetails){
        List<String> verifyResult = TextVerifyUtil.verifyText(videoCommentRequest.getContent());
        if (verifyResult!=null){
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "评论内容不文明: " + verifyResult);
        }

        Long userId=userDetails.getUserId();
        videoCommentRequest.setUserId(userId);
        videoCommentInteractionService.createComment(videoCommentRequest);
        return Result.success("用户在视频评论成功");
    }

    // 获取视频评论，分页获取
    @GetMapping
    public Result<PageFinalResult<VideoCommentsDTO>>
    getCommentsByVideoEpisodesId(VideoComentsPageRequest videoComentsPageRequest,
                                 @AuthenticationPrincipal CustomUserDetails userDetails){
        PageFinalResult<VideoCommentsDTO> videoCommentsList=videoCommentInteractionService.getComments(videoComentsPageRequest,
        userDetails.getUserId());
        return Result.success("获取视频评论成功",videoCommentsList);
    }

    @DeleteMapping("/{commentId}")
    public Result<Void> deleteComment(@PathVariable Long commentId){
        videoCommentInteractionService.deleteComment(commentId);
        return Result.success("删除视频评论成功");
    }

    /**
     * 对评论进行点赞或踩
     * <p>
     * 一个用户对一条评论只能点赞或踩，不能同时进行。
     * 如果用户首次操作，则创建新记录。
     * 如果用户重复操作（如重复点赞），则切换状态（如取消点赞）。
     * 如果用户从赞切换为踩（或反之），则更新类型和状态。
     * 请求体中需要同时包含 commentId 和 type。
     * </p>
     *
     * @param commentLikeRequest 包含评论ID和操作类型 ('like' 或 'dislike') 的请求体
     * @param userDetails        当前登录的用户信息
     * @return 操作结果
     */
    @PostMapping("/like")
    public Result<Void> likeComment(
            @Valid @RequestBody CommentLikeRequest commentLikeRequest,
            @AuthenticationPrincipal CustomUserDetails userDetails) {
        videoCommentInteractionService.handleCommentLike(userDetails.getUserId(), userDetails.getUsername(), commentLikeRequest);
        return Result.success("操作成功");
    }

    /**
     * 获取单个评论的点赞/踩信息,用于评论点赞后更新点赞数量
     * <p>
     * 返回指定评论的总点赞数、总踩数，以及当前登录用户的点赞状态。
     * 如果用户未登录，则只返回总数，用户状态为 "none"。
     * </p>
     *
     * @param commentId   被查询的评论ID
     * @param userDetails 当前登录的用户信息 (可以为null)
     * @return 包含点赞信息的DTO
     */
    @GetMapping("/{commentId}/likes")
    public Result<CommentLikeInfoDTO> getCommentLikeInfo(
            @PathVariable Long commentId,
            @AuthenticationPrincipal CustomUserDetails userDetails) {
        // 如果用户未登录，userDetails可能为null，此时传入的userId也为null
        Long userId = (userDetails != null) ? userDetails.getUserId() : null;
        CommentLikeInfoDTO likeInfo = videoCommentInteractionService.getCommentLikeInfo(commentId, userId);
        return Result.success("获取点赞信息成功", likeInfo);
    }
}
