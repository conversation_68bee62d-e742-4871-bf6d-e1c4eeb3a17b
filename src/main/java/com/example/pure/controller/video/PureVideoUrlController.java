package com.example.pure.controller.video;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.Result;
import com.example.pure.model.dto.response.video.VideoInfoWithEpisodesDto;
import com.example.pure.model.entity.VideoInfo;
import com.example.pure.model.entity.VideoType;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.video.PureVideoUrlService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.example.pure.model.dto.request.page.PageRequestDTO;

import javax.validation.Valid;
import java.util.List;

/**
 * 视频爬取使用的控制器
 */
@RestController
@RequestMapping("/api/videoUrl")
public class PureVideoUrlController {

    private final PureVideoUrlService pureUploadVideoUrlService;

    @Autowired
    public PureVideoUrlController(PureVideoUrlService pureUploadVideoUrlService) {
        this.pureUploadVideoUrlService = pureUploadVideoUrlService;
    }

    /**
     * 上传视频和视频分类及其分集信息
     * @param videoUploadRequest 视频上传请求对象
     * @return 上传结果
     */
    @PostMapping("/videoinfo-with-episodes")
    @Operation(summary = "上传视频",description = "上传视频信息集数加分类的Api")
    public Result<Void> uploadVideoWithEpisodes(@Validated @RequestBody VideoInfoWithEpisodesDto videoUploadRequest) {
        pureUploadVideoUrlService.uploadVideoWithEpisodes(videoUploadRequest);
        return Result.success("视频上传成功");
    }

    // 上传视频集数
    @PostMapping("/video-episodes")
    @Operation(summary = "上传视频集数",description = "上传视频集数的Api")
    public Result<Void> uploadVideo(@Validated @RequestBody VideoInfoWithEpisodesDto videoUploadRequest) {
        pureUploadVideoUrlService.uploadVideoEpisodes(videoUploadRequest);
        return Result.success("视频上传成功");
    }

    // 获取单个视频（包含多集数）信息和分类和集数根据标题
    @GetMapping("/videoinfo-with-episodes")
    @Operation(summary = "获取视频信息",description = "获取视频信息和分类和集数根据标题")
    public Result<VideoInfoWithEpisodesDto> getVideoInfoWithEpisodes(
            @RequestParam("title") String title,
            @AuthenticationPrincipal CustomUserDetails userDetails){
        // 安全地获取用户ID，如果用户未登录，则userId为null
        Long userId = (userDetails != null) ? userDetails.getUserId() : null;

        // 调用服务层，并传入正确的userId (Long类型)
        VideoInfoWithEpisodesDto videoInfoWithEpisodes = pureUploadVideoUrlService.getVideoInfoWithEpisodes(title, userId);
        return Result.success("视频查询成功", videoInfoWithEpisodes);
    }

    // 获取分页视频信息，有keyword就是通过title来搜索APi，没有就是按默认排序获取第一页视频
    @GetMapping("/videoinfo/pagination")
    @Operation(summary = "获取分页视频信息",description = "获取分页视频信息，有keyword就是搜索APi，没有就是按默认排序获取第一页视频")
    public Result<PageFinalResult<VideoInfo>>  getVideoInfoWithPagination(@Valid  PageRequestDTO pageRequest){
        PageFinalResult<VideoInfo> pageFinalResult=pureUploadVideoUrlService.getVideoInfoWithPagination(pageRequest);
        return Result.success("视频查询成功",pageFinalResult);

    }

    // 获取分页视频信息通过类型
    @GetMapping("/videoinfo/by-type/pagination")
    @Operation(summary = "获取分页视频信息通过类型",description = "获取分页视频信息通过类型")
    public Result<PageFinalResult<VideoInfo>> getVideoInfoByTypeWithPagination(@Valid  PageRequestDTO pageRequest){
        PageFinalResult<VideoInfo> pageFinalResult=pureUploadVideoUrlService.getVideoInfoByTypeWithPagination(pageRequest);
        return Result.success("视频查询成功",pageFinalResult);
    }

    // 获取全部视频类型
    @GetMapping("/type")
    public Result<PageFinalResult<VideoType>>getVideoAllType(){
        List<VideoType> allVideoType=pureUploadVideoUrlService.getAllVideoType();
        PageFinalResult<VideoType> pageFinalResult=new PageFinalResult<>(allVideoType,null);
        return Result.success("视频类型查询成功",pageFinalResult);
    }
}

