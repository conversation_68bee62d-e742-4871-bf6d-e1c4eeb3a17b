package com.example.pure.controller.video;

import com.example.pure.common.Result;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.video.EpisodeLikeInfoDTO;
import com.example.pure.model.dto.request.video.VideoEpisodesLikesRequest;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.video.PureVideoInteractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 视频互动控制器
 * <p>
 * 提供与视频内容互动相关的功能，例如发表评论、删除评论、点赞/踩评论等。
 * </p>
 */
@RestController
@RequestMapping("/api/video/interaction")
public class PureVideoInteractionController {

    private final PureVideoInteractionService pureVideoInteractionService;

    @Autowired
    public PureVideoInteractionController(PureVideoInteractionService pureVideoInteractionService) {
        this.pureVideoInteractionService = pureVideoInteractionService;
    }


    /* 点赞集数视频接口,如果用户为从未点赞过就运行CREATE的SQL操作，
    点赞过就使用UPDATE的SQL操作改变status来实现取消点赞，再次点赞就再次改变status的值来进行点赞
     */
    @PostMapping("/episode/{episodeId}/likes")
    public Result<Void> createVideoEpisodesLikes(
            @PathVariable Long episodeId,
            @AuthenticationPrincipal CustomUserDetails userDetails) {
        if (ObjectUtils.isEmpty(userDetails)) {
            throw new BusinessException("用户未登录");
        }
        // Manually construct the request DTO for the service layer
        VideoEpisodesLikesRequest videoLikesRequest = new VideoEpisodesLikesRequest();
        videoLikesRequest.setVideoEpisodeId(episodeId);
        videoLikesRequest.setUserId(userDetails.getUserId());

        pureVideoInteractionService.createEpisodesVideoLikes(videoLikesRequest);
        return Result.success("视频点赞成功");
    }


    /**
     * 获取单个视频分集的点赞信息
     * <p>
     * 用于在用户点赞/取消点赞后，客户端调用此接口以获取最新的点赞总数和当前用户的点赞状态，
     * 以便实时更新UI。
     * </p>
     *
     * @param episodeId   要查询的视频分集ID
     * @param userDetails 当前登录的用户信息 (可以为null)
     * @return 包含该分集最新点赞信息的DTO
     */
    @GetMapping("/episode/{episodeId}/like-info")
    public Result<EpisodeLikeInfoDTO> getEpisodeLikeInfo(
            @PathVariable Long episodeId,
            @AuthenticationPrincipal CustomUserDetails userDetails) {
        Long userId = (userDetails != null) ? userDetails.getUserId() : null;
        EpisodeLikeInfoDTO likeInfo = pureVideoInteractionService.getEpisodeLikeInfo(episodeId, userId);
        return Result.success("获取分集点赞信息成功", likeInfo);
    }
}
