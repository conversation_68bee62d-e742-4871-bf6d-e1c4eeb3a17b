package com.example.pure.util;

import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 文件传输通用工具类
 * <p>
 * 提供文件传输过程中的通用工具方法，避免在多个类中重复相同的逻辑。
 * 这是一个静态工具类，所有方法都是线程安全的。
 * </p>
 * 
 * <h3>功能分类：</h3>
 * <ul>
 *   <li><b>格式化工具</b>：字节数格式化、时间格式化等</li>
 *   <li><b>验证工具</b>：参数验证、状态检查等</li>
 *   <li><b>计算工具</b>：速度计算、进度计算等</li>
 * </ul>
 * 
 * <h3>设计原则：</h3>
 * <ul>
 *   <li><b>无状态</b>：所有方法都是静态的，不维护任何状态</li>
 *   <li><b>线程安全</b>：可以在多线程环境中安全使用</li>
 *   <li><b>高性能</b>：避免不必要的对象创建和计算</li>
 *   <li><b>易用性</b>：提供简洁明了的API</li>
 * </ul>
 */
public final class TransferUtils {

    // 私有构造函数，防止实例化
    private TransferUtils() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }

    // ==================== 格式化工具 ====================

    /**
     * 格式化字节数为可读的大小字符串
     * <p>
     * 将字节数转换为人类可读的格式，自动选择合适的单位（B、KB、MB、GB、TB）。
     * 使用1024作为进制，符合计算机存储的标准。
     * </p>
     * 
     * <h3>格式化规则：</h3>
     * <ul>
     *   <li>小于1KB：显示为 "xxx B"</li>
     *   <li>小于1MB：显示为 "xxx.xx KB"</li>
     *   <li>小于1GB：显示为 "xxx.xx MB"</li>
     *   <li>小于1TB：显示为 "xxx.xx GB"</li>
     *   <li>大于等于1TB：显示为 "xxx.xx TB"</li>
     * </ul>
     * 
     * @param bytes 字节数
     * @return 格式化的大小字符串，如 "1.50 MB"
     */
    public static String formatBytes(long bytes) {
        if (bytes < 0) {
            return "0 B";
        }
        
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else if (bytes < 1024L * 1024 * 1024 * 1024) {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        } else {
            return String.format("%.2f TB", bytes / (1024.0 * 1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 格式化时间为可读的时间字符串
     * <p>
     * 将毫秒数转换为人类可读的时间格式，自动选择合适的单位。
     * </p>
     * 
     * @param timeMs 时间（毫秒）
     * @return 格式化的时间字符串，如 "1分30秒"
     */
    public static String formatTime(long timeMs) {
        if (timeMs < 0) {
            return "0秒";
        }
        
        long seconds = timeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天%d小时", days, hours % 24);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分%d秒", minutes, seconds % 60);
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 格式化传输速度为可读的速度字符串
     * <p>
     * 将字节/秒转换为人类可读的速度格式。
     * </p>
     * 
     * @param bytesPerSecond 传输速度（字节/秒）
     * @return 格式化的速度字符串，如 "1.50 MB/s"
     */
    public static String formatSpeed(double bytesPerSecond) {
        if (bytesPerSecond < 0) {
            return "0 B/s";
        }
        
        return formatBytes((long) bytesPerSecond) + "/s";
    }

    /**
     * 格式化进度百分比
     * <p>
     * 将进度百分比格式化为字符串，保留一位小数。
     * </p>
     * 
     * @param percent 进度百分比（0.0-1.0）
     * @return 格式化的百分比字符串，如 "65.5%"
     */
    public static String formatPercent(double percent) {
        if (percent < 0) {
            return "0.0%";
        } else if (percent > 1) {
            return "100.0%";
        } else {
            return String.format("%.1f%%", percent * 100);
        }
    }

    // ==================== 验证工具 ====================

    /**
     * 验证传输参数的基本有效性
     * <p>
     * 提供通用的参数验证逻辑，使用Spring工具类进行null检查。
     * </p>
     * 
     * @param fileName 文件名
     * @param totalBytes 文件总大小
     * @throws IllegalArgumentException 如果参数无效
     */
    public static void validateBasicTransferParameters(String fileName, long totalBytes) {
        if (!StringUtils.hasText(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (totalBytes < 0) {
            throw new IllegalArgumentException("文件大小不能为负数");
        }
    }

    /**
     * 验证数据写入参数
     * <p>
     * 验证write方法的参数有效性。
     * </p>
     * 
     * @param data 数据数组
     * @param offset 偏移量
     * @param length 长度
     * @throws IllegalArgumentException 如果参数无效
     */
    public static void validateWriteParameters(byte[] data, int offset, int length) {
        if (ObjectUtils.isEmpty(data)) {
            throw new IllegalArgumentException("写入数据不能为null");
        }
        if (offset < 0 || length < 0 || offset + length > data.length) {
            throw new IllegalArgumentException("无效的偏移量或长度参数");
        }
    }

    // ==================== 计算工具 ====================

    /**
     * 计算传输进度百分比
     * <p>
     * 根据已传输字节数和总字节数计算进度百分比。
     * </p>
     * 
     * @param transferredBytes 已传输字节数
     * @param totalBytes 总字节数
     * @return 进度百分比（0.0-1.0）
     */
    public static double calculateProgress(long transferredBytes, long totalBytes) {
        if (totalBytes <= 0) {
            return 0.0;
        }
        double progress = (double) transferredBytes / totalBytes;
        return Math.min(Math.max(progress, 0.0), 1.0); // 确保在0-1范围内
    }

    /**
     * 计算传输速度
     * <p>
     * 根据传输的字节数和时间计算传输速度。
     * </p>
     * 
     * @param bytes 传输的字节数
     * @param timeMs 传输时间（毫秒）
     * @return 传输速度（字节/秒）
     */
    public static double calculateSpeed(long bytes, long timeMs) {
        if (timeMs <= 0) {
            return 0.0;
        }
        return (double) bytes / (timeMs / 1000.0);
    }

    /**
     * 估算剩余传输时间
     * <p>
     * 根据当前传输速度和剩余字节数估算剩余时间。
     * </p>
     * 
     * @param remainingBytes 剩余字节数
     * @param currentSpeedBytesPerSecond 当前传输速度（字节/秒）
     * @return 估算的剩余时间（毫秒）
     */
    public static long estimateRemainingTime(long remainingBytes, double currentSpeedBytesPerSecond) {
        if (currentSpeedBytesPerSecond <= 0 || remainingBytes <= 0) {
            return 0L;
        }
        return (long) (remainingBytes / currentSpeedBytesPerSecond * 1000);
    }

    // ==================== 状态检查工具 ====================

    /**
     * 检查对象是否为null或空
     * <p>
     * 使用Spring的ObjectUtils进行检查，支持多种类型的空值判断。
     * </p>
     * 
     * @param obj 要检查的对象
     * @return 如果对象为null或空返回true，否则返回false
     */
    public static boolean isEmpty(Object obj) {
        return ObjectUtils.isEmpty(obj);
    }

    /**
     * 检查字符串是否有文本内容
     * <p>
     * 使用Spring的StringUtils进行检查，会检查null、空字符串和空白字符串。
     * </p>
     * 
     * @param str 要检查的字符串
     * @return 如果字符串有文本内容返回true，否则返回false
     */
    public static boolean hasText(String str) {
        return StringUtils.hasText(str);
    }
}
