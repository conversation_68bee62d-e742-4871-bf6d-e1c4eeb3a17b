package com.example.pure.util;

import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 多模态内容处理工具类
 * <p>
 * 提供多模态内容的解析、验证和转换功能
 * </p>
 */
@Slf4j
public class MultimodalContentUtils {

    // Base64图片数据的正则表达式
    private static final Pattern BASE64_IMAGE_PATTERN = Pattern.compile(
        "^data:image/(jpeg|jpg|png|gif|webp);base64,[A-Za-z0-9+/]+=*$"
    );

    // HTTP/HTTPS URL的正则表达式
    private static final Pattern HTTP_URL_PATTERN = Pattern.compile(
        "^https?://.*\\.(jpeg|jpg|png|gif|webp)$", Pattern.CASE_INSENSITIVE
    );

    /**
     * 检查内容是否为多模态格式
     *
     * @param content 消息内容
     * @return true如果是多模态格式
     */
    public static boolean isMultimodalContent(List<OpenAiChatRequest.ContentPart> content) {
        return content != null && !content.isEmpty();
    }

    /**
     * 检查内容是否为纯文本格式
     *
     * @param content 消息内容
     * @return true如果只包含文本内容
     */
    public static boolean isTextContent(List<OpenAiChatRequest.ContentPart> content) {
        if (content == null || content.isEmpty()) {
            return false;
        }
        return content.stream().allMatch(part -> "text".equals(part.getType()));
    }

    /**
     * 将纯文本转换为多模态格式
     *
     * @param text 纯文本内容
     * @return 多模态内容列表
     */
    public static List<OpenAiChatRequest.ContentPart> textToMultimodal(String text) {
        List<OpenAiChatRequest.ContentPart> parts = new ArrayList<>();
        
        OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
        textPart.setType("text");
        textPart.setText(text);
        parts.add(textPart);
        
        return parts;
    }

    /**
     * 从多模态内容中提取纯文本
     *
     * @param content 多模态内容
     * @return 提取的文本内容
     */
    public static String extractTextFromMultimodal(List<OpenAiChatRequest.ContentPart> content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        StringBuilder textBuilder = new StringBuilder();

        for (OpenAiChatRequest.ContentPart part : content) {
            if ("text".equals(part.getType()) && part.getText() != null) {
                if (textBuilder.length() > 0) {
                    textBuilder.append(" ");
                }
                textBuilder.append(part.getText());
            }
        }

        return textBuilder.toString();
    }

    /**
     * 检查多模态内容是否包含图片
     *
     * @param content 消息内容
     * @return true如果包含图片
     */
    public static boolean containsImages(List<OpenAiChatRequest.ContentPart> content) {
        if (content == null || content.isEmpty()) {
            return false;
        }

        return content.stream()
                .anyMatch(part -> "image_url".equals(part.getType()));
    }

    /**
     * 获取多模态内容中的图片数量
     *
     * @param content 消息内容
     * @return 图片数量
     */
    public static int getImageCount(List<OpenAiChatRequest.ContentPart> content) {
        if (content == null || content.isEmpty()) {
            return 0;
        }

        return (int) content.stream()
                .filter(part -> "image_url".equals(part.getType()))
                .count();
    }

    /**
     * 验证图片URL格式
     *
     * @param imageUrl 图片URL
     * @return true如果格式有效
     */
    public static boolean isValidImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否为base64格式
        if (BASE64_IMAGE_PATTERN.matcher(imageUrl).matches()) {
            return true;
        }
        
        // 检查是否为HTTP/HTTPS URL
        if (HTTP_URL_PATTERN.matcher(imageUrl).matches()) {
            return true;
        }
        
        return false;
    }

    /**
     * 验证多模态内容格式
     *
     * @param content 消息内容
     * @return 验证结果
     */
    public static ValidationResult validateMultimodalContent(List<OpenAiChatRequest.ContentPart> content) {
        if (content == null || content.isEmpty()) {
            return ValidationResult.error("多模态内容不能为空");
        }

        // 验证每个部分
        for (int i = 0; i < content.size(); i++) {
            OpenAiChatRequest.ContentPart part = content.get(i);
            
            if (part.getType() == null) {
                return ValidationResult.error("第" + (i + 1) + "个内容部分缺少type字段");
            }
            
            switch (part.getType()) {
                case "text":
                    if (part.getText() == null || part.getText().trim().isEmpty()) {
                        return ValidationResult.error("第" + (i + 1) + "个文本部分内容为空");
                    }
                    break;
                    
                case "image_url":
                    if (part.getImageUrl() == null) {
                        return ValidationResult.error("第" + (i + 1) + "个图片部分缺少image_url字段");
                    }
                    if (!isValidImageUrl(part.getImageUrl().getUrl())) {
                        return ValidationResult.error("第" + (i + 1) + "个图片URL格式无效");
                    }
                    break;
                    
                default:
                    return ValidationResult.error("第" + (i + 1) + "个内容部分类型不支持: " + part.getType());
            }
        }
        
        // 检查图片数量限制（OpenAI限制为10张）
        int imageCount = getImageCount(content);
        if (imageCount > 10) {
            return ValidationResult.error("图片数量超过限制，最多支持10张图片");
        }
        
        return ValidationResult.success();
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;

        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }

        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 记录多模态内容的统计信息
     *
     * @param content 消息内容
     */
    public static void logContentStats(List<OpenAiChatRequest.ContentPart> content) {
        if (content == null || content.isEmpty()) {
            log.debug("空消息内容");
            return;
        }

        int imageCount = getImageCount(content);
        String text = extractTextFromMultimodal(content);

        if (imageCount > 0) {
            log.debug("多模态消息 - 文本长度: {} 字符, 图片数量: {}", text.length(), imageCount);
        } else {
            log.debug("纯文本消息 - 长度: {} 字符", text.length());
        }
    }
}
