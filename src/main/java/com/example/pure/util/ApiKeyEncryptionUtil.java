package com.example.pure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * API密钥加密工具类
 * <p>
 * 提供API密钥的AES-256加密和解密功能
 * </p>
 */
@Slf4j
@Component
public class ApiKeyEncryptionUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String HASH_ALGORITHM = "SHA-256";

    @Value("${ai.encryption.secret:default-secret-key-for-ai-api-encryption}")
    private String encryptionSecret;

    /**
     * 加密API密钥
     *
     * @param apiKey 原始API密钥
     * @return 加密后的API密钥（Base64编码）
     */
    public String encrypt(String apiKey) {
        try {
            SecretKey secretKey = generateSecretKey();
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = cipher.doFinal(apiKey.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("加密API密钥失败", e);
            throw new RuntimeException("加密API密钥失败", e);
        }
    }

    /**
     * 解密API密钥
     *
     * @param encryptedApiKey 加密后的API密钥（Base64编码）
     * @return 原始API密钥
     */
    public String decrypt(String encryptedApiKey) {
        try {
            SecretKey secretKey = generateSecretKey();
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedApiKey);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密API密钥失败", e);
            throw new RuntimeException("解密API密钥失败", e);
        }
    }

    /**
     * 脱敏显示API密钥
     * <p>
     * 只显示前缀和后缀，中间用星号替代
     * </p>
     *
     * @param apiKey 原始API密钥
     * @return 脱敏后的API密钥
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        
        String prefix = apiKey.substring(0, Math.min(4, apiKey.length()));
        String suffix = apiKey.substring(Math.max(apiKey.length() - 4, 4));
        return prefix + "****...****" + suffix;
    }

    /**
     * 生成OpenAI兼容的API密钥格式
     * <p>
     * 格式：sk-{userId}_{keyId}_{hash}
     * </p>
     *
     * @param userId 用户ID
     * @param keyId  密钥ID
     * @return 兼容格式的API密钥
     */
    public String generateCompatibleApiKey(Long userId, Long keyId) {
        try {
            String data = userId + "_" + keyId + "_" + System.currentTimeMillis();
            String hash = generateHash(data);
            return String.format("sk-%d_%d_%s", userId, keyId, hash.substring(0, 12));
        } catch (Exception e) {
            log.error("生成兼容API密钥失败", e);
            throw new RuntimeException("生成兼容API密钥失败", e);
        }
    }

    /**
     * 解析OpenAI兼容的API密钥格式
     * <p>
     * 从格式 sk-{userId}_{keyId}_{hash} 中提取信息
     * </p>
     *
     * @param compatibleKey 兼容格式的API密钥
     * @return 解析结果
     */
    public ParseResult parseCompatibleApiKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return new ParseResult(false, null, null, null);
            }
            
            String[] parts = compatibleKey.substring(3).split("_");
            if (parts.length != 3) {
                return new ParseResult(false, null, null, null);
            }
            
            Long userId = Long.parseLong(parts[0]);
            Long keyId = Long.parseLong(parts[1]);
            String hash = parts[2];
            
            return new ParseResult(true, userId, keyId, hash);
        } catch (Exception e) {
            log.warn("解析兼容API密钥失败: {}", compatibleKey, e);
            return new ParseResult(false, null, null, null);
        }
    }

    /**
     * 验证兼容API密钥的有效性
     *
     * @param compatibleKey 兼容格式的API密钥
     * @param userId        用户ID
     * @param keyId         密钥ID
     * @return 是否有效
     */
    public boolean validateCompatibleApiKey(String compatibleKey, Long userId, Long keyId) {
        ParseResult result = parseCompatibleApiKey(compatibleKey);
        return result.isValid() && 
               userId.equals(result.getUserId()) && 
               keyId.equals(result.getKeyId());
    }

    /**
     * 生成密钥
     */
    private SecretKey generateSecretKey() throws Exception {
        MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
        byte[] keyBytes = digest.digest(encryptionSecret.getBytes(StandardCharsets.UTF_8));
        // 取前32字节作为AES-256密钥
        byte[] key = new byte[32];
        System.arraycopy(keyBytes, 0, key, 0, Math.min(keyBytes.length, 32));
        return new SecretKeySpec(key, ALGORITHM);
    }

    /**
     * 生成哈希值
     */
    private String generateHash(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
        byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes);
    }

    /**
     * 解析结果
     */
    public static class ParseResult {
        private final boolean valid;
        private final Long userId;
        private final Long keyId;
        private final String hash;

        public ParseResult(boolean valid, Long userId, Long keyId, String hash) {
            this.valid = valid;
            this.userId = userId;
            this.keyId = keyId;
            this.hash = hash;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getKeyId() { return keyId; }
        public String getHash() { return hash; }
    }
}
