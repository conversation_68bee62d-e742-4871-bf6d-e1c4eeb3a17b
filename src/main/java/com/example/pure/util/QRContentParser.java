package com.example.pure.util;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.model.dto.response.auth.QRLoginDTO.CommunicationType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码内容解析工具类
 * <p>
 * 用于解析二维码内容，提取二维码ID和通信类型信息
 * </p>
 */
@Slf4j
public class QRContentParser {

    /**
     * 二维码解析结果
     */
    @Data
    public static class QRParseResult {
        private String qrId;
        private CommunicationType communicationType;
        private boolean valid;
        private String errorMessage;

        public static QRParseResult success(String qrId, CommunicationType communicationType) {
            QRParseResult result = new QRParseResult();
            result.qrId = qrId;
            result.communicationType = communicationType;
            result.valid = true;
            return result;
        }

        public static QRParseResult error(String errorMessage) {
            QRParseResult result = new QRParseResult();
            result.valid = false;
            result.errorMessage = errorMessage;
            return result;
        }
    }

    /**
     * 解析二维码内容
     * <p>
     * 支持的格式：
     * - qrlogin://qr-id
     * - qrlogin://qr-id?type=websocket
     * - qrlogin://qr-id?type=sse
     * </p>
     *
     * @param qrContent 二维码内容
     * @return 解析结果
     */
    public static QRParseResult parseQRContent(String qrContent) {
        try {
            // 检查基本格式
            if (!StringUtils.hasText(qrContent)) {
                return QRParseResult.error("二维码内容为空");
            }

            if (!qrContent.startsWith(SecurityConstants.QR_LOGIN_SCHEME)) {
                return QRParseResult.error("不是有效的登录二维码");
            }

            // 移除协议前缀
            String content = qrContent.substring(SecurityConstants.QR_LOGIN_SCHEME.length());

            // 解析 qrId 和参数
            String qrId;
            Map<String, String> params = new HashMap<>();

            if (content.contains("?")) {
                String[] parts = content.split("\\?", 2);
                qrId = parts[0];

                // 解析查询参数
                if (parts.length > 1) {
                    params = parseQueryParams(parts[1]);
                }
            } else {
                qrId = content;
            }

            // 验证 qrId
            if (!StringUtils.hasText(qrId)) {
                return QRParseResult.error("二维码ID为空");
            }

            // 解析通信类型
            CommunicationType communicationType = CommunicationType.WEBSOCKET; // 默认值
            String typeParam = params.get("type");
            if (StringUtils.hasText(typeParam)) {
                try {
                    communicationType = parseCommunicationType(typeParam);
                } catch (IllegalArgumentException e) {
                    log.warn("未知的通信类型: {}, 使用默认值 WEBSOCKET", typeParam);
                }
            }

            log.debug("解析二维码成功 - QR ID: {}, 通信类型: {}", qrId, communicationType);
            return QRParseResult.success(qrId, communicationType);

        } catch (Exception e) {
            log.error("解析二维码内容失败: {}", qrContent, e);
            return QRParseResult.error("解析二维码失败: " + e.getMessage());
        }
    }

    /**
     * 解析查询参数
     *
     * @param queryString 查询字符串
     * @return 参数映射
     */
    private static Map<String, String> parseQueryParams(String queryString) {
        Map<String, String> params = new HashMap<>();

        if (!StringUtils.hasText(queryString)) {
            return params;
        }

        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                try {
                    String key = URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8);
                    String value = URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8);
                    params.put(key, value);
                } catch (Exception e) {
                    log.warn("解析查询参数失败: {}", pair, e);
                }
            }
        }

        return params;
    }

    /**
     * 解析通信类型
     *
     * @param typeCode 类型代码
     * @return 通信类型
     */
    private static CommunicationType parseCommunicationType(String typeCode) {
        if (!StringUtils.hasText(typeCode)) {
            return CommunicationType.WEBSOCKET;
        }

        for (CommunicationType type : CommunicationType.values()) {
            if (type.getCode().equalsIgnoreCase(typeCode)) {
                return type;
            }
        }

        throw new IllegalArgumentException("未知的通信类型: " + typeCode);
    }

    /**
     * 生成二维码内容
     *
     * @param qrId 二维码ID
     * @param communicationType 通信类型
     * @return 二维码内容
     */
    public static String generateQRContent(String qrId, CommunicationType communicationType) {
        if (!StringUtils.hasText(qrId)) {
            throw new IllegalArgumentException("二维码ID不能为空");
        }

        if (communicationType == null) {
            communicationType = CommunicationType.WEBSOCKET;
        }

        return SecurityConstants.QR_LOGIN_SCHEME + qrId + "?type=" + communicationType.getCode();
    }
}
