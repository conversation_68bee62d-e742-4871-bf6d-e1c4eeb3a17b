package com.example.pure.util;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * 时间工具类
 * <p>
 * 统一处理UTC时间，所有时间存储和传输都使用UTC时区
 * 前端负责根据用户系统时间进行时区转换
 * </p>
 */
public class TimeUtil {

    /**
     * UTC时区偏移
     */
    public static final ZoneOffset UTC = ZoneOffset.UTC;

    /**
     * ISO 8601格式化器（UTC时区）
     */
    public static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ISO_INSTANT;

    /**
     * 自定义格式化器（UTC时区）
     */
    public static final DateTimeFormatter CUSTOM_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS").withZone(UTC);

    /**
     * 获取当前UTC时间
     *
     * @return 当前UTC时间的Instant对象
     */
    public static Instant nowUtc() {
        return Instant.now();
    }

    /**
     * 将Instant转换为ISO 8601格式字符串
     *
     * @param instant 时间对象
     * @return ISO 8601格式字符串，如果输入为null则返回null
     */
    public static String toIsoString(Instant instant) {
        return instant != null ? ISO_FORMATTER.format(instant) : null;
    }

    /**
     * 将Instant转换为自定义格式字符串
     *
     * @param instant 时间对象
     * @return 自定义格式字符串，如果输入为null则返回null
     */
    public static String toCustomString(Instant instant) {
        return instant != null ? CUSTOM_FORMATTER.format(instant) : null;
    }

    /**
     * 从ISO 8601格式字符串解析Instant
     *
     * @param isoString ISO 8601格式字符串
     * @return Instant对象，如果输入为null或空则返回null
     */
    public static Instant fromIsoString(String isoString) {
        if (isoString == null || isoString.trim().isEmpty()) {
            return null;
        }
        return Instant.parse(isoString);
    }

    /**
     * 从时间戳（毫秒）创建Instant
     *
     * @param epochMilli 时间戳（毫秒）
     * @return Instant对象
     */
    public static Instant fromEpochMilli(long epochMilli) {
        return Instant.ofEpochMilli(epochMilli);
    }

    /**
     * 从时间戳（秒）创建Instant
     *
     * @param epochSecond 时间戳（秒）
     * @return Instant对象
     */
    public static Instant fromEpochSecond(long epochSecond) {
        return Instant.ofEpochSecond(epochSecond);
    }

    /**
     * 将Instant转换为时间戳（毫秒）
     *
     * @param instant 时间对象
     * @return 时间戳（毫秒），如果输入为null则返回0
     */
    public static long toEpochMilli(Instant instant) {
        return instant != null ? instant.toEpochMilli() : 0L;
    }

    /**
     * 将Instant转换为时间戳（秒）
     *
     * @param instant 时间对象
     * @return 时间戳（秒），如果输入为null则返回0
     */
    public static long toEpochSecond(Instant instant) {
        return instant != null ? instant.getEpochSecond() : 0L;
    }

    /**
     * 检查时间是否已过期
     *
     * @param expiresAt 过期时间
     * @return 是否已过期，如果过期时间为null则返回false
     */
    public static boolean isExpired(Instant expiresAt) {
        return expiresAt != null && expiresAt.isBefore(nowUtc());
    }

    /**
     * 检查时间是否未过期
     *
     * @param expiresAt 过期时间
     * @return 是否未过期，如果过期时间为null则返回true
     */
    public static boolean isNotExpired(Instant expiresAt) {
        return !isExpired(expiresAt);
    }

    /**
     * 创建过期时间（从现在开始的指定秒数后）
     *
     * @param secondsFromNow 从现在开始的秒数
     * @return 过期时间
     */
    public static Instant expiresAfterSeconds(long secondsFromNow) {
        return nowUtc().plusSeconds(secondsFromNow);
    }

    /**
     * 创建过期时间（从现在开始的指定分钟数后）
     *
     * @param minutesFromNow 从现在开始的分钟数
     * @return 过期时间
     */
    public static Instant expiresAfterMinutes(long minutesFromNow) {
        return nowUtc().plusSeconds(minutesFromNow * 60);
    }

    /**
     * 创建过期时间（从现在开始的指定小时数后）
     *
     * @param hoursFromNow 从现在开始的小时数
     * @return 过期时间
     */
    public static Instant expiresAfterHours(long hoursFromNow) {
        return nowUtc().plusSeconds(hoursFromNow * 3600);
    }

    /**
     * 创建过期时间（从现在开始的指定天数后）
     *
     * @param daysFromNow 从现在开始的天数
     * @return 过期时间
     */
    public static Instant expiresAfterDays(long daysFromNow) {
        return nowUtc().plusSeconds(daysFromNow * 86400);
    }

    /**
     * 计算两个时间之间的秒数差
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 秒数差，如果任一参数为null则返回0
     */
    public static long secondsBetween(Instant start, Instant end) {
        if (start == null || end == null) {
            return 0L;
        }
        return end.getEpochSecond() - start.getEpochSecond();
    }

    /**
     * 计算两个时间之间的毫秒数差
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 毫秒数差，如果任一参数为null则返回0
     */
    public static long millisBetween(Instant start, Instant end) {
        if (start == null || end == null) {
            return 0L;
        }
        return end.toEpochMilli() - start.toEpochMilli();
    }
}
