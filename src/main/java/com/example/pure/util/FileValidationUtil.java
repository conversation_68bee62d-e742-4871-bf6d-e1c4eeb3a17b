package com.example.pure.util;

import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.file.user.FileMetadata;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件验证工具类
 * <p>
 * 提供文件相关的验证方法，减少重复代码
 * </p>
 */
@Slf4j
public final class FileValidationUtil {

    private FileValidationUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 验证文件元数据并抛出相应的业务异常
     * <p>
     * 统一的文件存在性和可读性验证逻辑
     * </p>
     *
     * @param metadata 文件元数据
     * @param identifier 文件标识符（文件名或ID）
     * @param identifierType 标识符类型（用于错误消息）
     * @throws BusinessException 如果文件不存在或不可读
     */
    public static void validateFileMetadata(FileMetadata metadata, String identifier, String identifierType) {
        if (!metadata.isExists()) {
            log.error("请求的{}不存在: {}", identifierType, identifier);
            throw BusinessException.of(ResponseCode.NOT_FOUND,
                String.format("%s不存在: %s", identifierType, identifier));
        }

        if (!metadata.isReadable()) {
            log.error("请求的{}不可读: {}", identifierType, identifier);
            throw BusinessException.of(ResponseCode.NOT_FOUND,
                String.format("%s不可读: %s", identifierType, identifier));
        }
    }

    /**
     * 验证文件元数据（使用默认的错误消息）
     * <p>
     * 简化版本的文件验证方法
     * </p>
     *
     * @param metadata 文件元数据
     * @param fileName 文件名
     * @throws BusinessException 如果文件不存在或不可读
     */
    public static void validateFileMetadata(FileMetadata metadata, String fileName) {
        validateFileMetadata(metadata, fileName, "文件");
    }

    /**
     * 验证图片文件元数据
     * <p>
     * 专门用于图片文件的验证
     * </p>
     *
     * @param metadata 文件元数据
     * @param identifier 图片标识符
     * @throws BusinessException 如果图片文件不存在或不可读
     */
    public static void validateImageMetadata(FileMetadata metadata, String identifier) {
        if (!metadata.isExists() || !metadata.isReadable()) {
            log.error("请求的图片不存在或不可读: {}", identifier);
            throw BusinessException.of(ResponseCode.NOT_FOUND,
                "图片文件不存在或不可读: " + identifier);
        }
    }

    /**
     * 验证图片文件元数据（带ID映射）
     * <p>
     * 用于通过ID访问图片的场景
     * </p>
     *
     * @param metadata 文件元数据
     * @param imageId 图片ID
     * @param fileName 实际文件名
     * @throws BusinessException 如果图片文件不存在或不可读
     */
    public static void validateImageMetadata(FileMetadata metadata, String imageId, String fileName) {
        if (!metadata.isExists() || !metadata.isReadable()) {
            log.error("请求的图片不存在或不可读: ID={}, 文件={}", imageId, fileName);
            throw BusinessException.of(ResponseCode.NOT_FOUND,
                "图片不存在或不可读: ID=" + imageId);
        }
    }
}
