package com.example.pure.util;

import com.example.pure.monitor.FileTransferProgressMonitor;
import com.example.pure.transfer.EmitterOutputTarget;
import com.example.pure.transfer.FileOutputTarget;
import com.example.pure.transfer.OutputTarget;
import com.example.pure.transfer.TransferConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;

/**
 * 统一分块文件传输工具类
 * <p>
 * 提供统一的带进度监控的分块文件传输功能，支持：
 * - Chunked Transfer：分块读写，内存占用可控
 * - 统一输出目标：支持文件写入和HTTP响应发送
 * - 进度监控：实时进度、传输速度、时间估算
 * - Range请求：支持HTTP Range请求的部分内容传输
 * - 性能优化：可配置缓冲区大小，平衡性能和内存使用
 * - 异常安全：确保资源正确释放
 * </p>
 *
 * <h3>设计原则：</h3>
 * <ul>
 *   <li><b>统一接口</b>：上传和下载使用相同的传输逻辑</li>
 *   <li><b>内存效率</b>：使用固定大小缓冲区，避免大文件导致内存溢出</li>
 *   <li><b>进度可见</b>：提供详细的传输进度信息</li>
 *   <li><b>性能平衡</b>：在传输速度和进度更新频率之间找到平衡</li>
 *   <li><b>资源安全</b>：使用try-finally确保资源正确释放</li>
 * </ul>
 *
 * <h3>核心方法说明：</h3>
 * <ul>
 *   <li><b>transferWithProgress</b>：统一的传输方法，支持所有场景</li>
 *   <li><b>transferFromStreamToFile</b>：专用于文件上传场景</li>
 *   <li><b>transferFromFileToStream</b>：专用于文件下载场景</li>
 * </ul>
 *
 * <h3>进度监视器触发条件详解：</h3>
 * <ul>
 *   <li><b>启用条件</b>：enableProgress=true 且 progressMonitor!=null</li>
 *   <li><b>报告触发</b>：首次报告总是触发 或 时间间隔≥2秒 或 字节间隔≥1MB</li>
 *   <li><b>日志输出</b>：每10%进度增量 或 超过30秒时间阈值</li>
 *   <li><b>生命周期</b>：传输开始、进行中、完成、错误四个节点自动触发</li>
 *   <li><b>性能优化</b>：智能频率控制，避免过于频繁的回调影响传输性能</li>
 * </ul>
 */
@Slf4j
public class FileTransferUtil {



    // ==================== 统一传输方法 ====================

    /**
     * 统一的带进度监控的文件传输方法
     * <p>
     * 这是核心的传输方法，支持所有传输场景：
     * - 文件上传：InputStream → FileOutputTarget
     * - 文件下载：InputStream → EmitterOutputTarget
     * - Range请求：支持部分内容传输
     * - 进度监控：实时进度、速度计算、时间估算
     * </p>
     *
     * <h3>从FilePureServiceImpl转移的核心逻辑：</h3>
     * <ul>
     *   <li><b>分块读写</b>：使用缓冲区分块处理，内存占用恒定</li>
     *   <li><b>Range支持</b>：支持HTTP Range请求的跳过和限制逻辑</li>
     *   <li><b>发送控制</b>：支持发送间隔控制，避免占用过多带宽</li>
     *   <li><b>异常处理</b>：完善的错误处理和资源清理</li>
     * </ul>
     *
     * @param inputStream 输入流
     * @param outputTarget 输出目标（文件或HTTP响应）
     * @param fileName 文件名（用于进度显示和日志）
     * @param totalBytes 总字节数（用于进度计算，如果未知可传入-1）
     * @param progressMonitor 进度监控器，可以为null
     * @param config 传输配置
     * @return 实际传输的字节数
     * @throws IOException 如果传输过程中发生IO错误
     */
    public static long transferWithProgress(InputStream inputStream,
                                          OutputTarget outputTarget,
                                          String fileName,
                                          long totalBytes,
                                          FileTransferProgressMonitor progressMonitor,
                                          TransferConfig config) throws IOException {

        // 参数验证：确保所有必需参数都不为null且有效
        validateTransferParameters(inputStream, outputTarget, fileName, config);

        // 获取传输配置参数
        int bufferSize = config.getBufferSize();           // 缓冲区大小，影响内存使用和传输效率
        long rangeStart = config.getRangeStart();          // Range请求起始位置
        long rangeEnd = config.getRangeEnd();              // Range请求结束位置
        long sendIntervalMs = config.getSendIntervalMs();  // 发送间隔，用于速率控制
        boolean enableProgress = config.isEnableProgressMonitoring(); // 是否启用进度监控

        // 计算实际传输的内容长度
        // Range请求时：传输指定范围的字节数
        // 普通请求时：传输整个文件的字节数
        long contentLength = config.isRangeRequest() ?
                config.calculateRangeContentLength(totalBytes) : totalBytes;

        // 初始化传输状态变量
        long transferredBytes = 0;                    // 已传输字节数
        long startTime = System.currentTimeMillis(); // 传输开始时间
        long lastProgressTime = startTime;           // 上次进度报告时间
        long lastProgressBytes = 0;                  // 上次进度报告时的字节数
        
        // 初始化进度监控器
        // 触发条件：enableProgress=true 且 progressMonitor!=null
        if (enableProgress && !ObjectUtils.isEmpty(progressMonitor)) {
            progressMonitor.onTransferStart(fileName, contentLength, config.getTransferType());
        }

        try {
            // ========== Range请求处理 ==========
            // 支持HTTP Range请求，用于断点续传和部分内容下载
            // 决策理由：从FilePureServiceImpl转移的Range处理逻辑，支持断点续传和部分内容请求
            if (config.isRangeRequest() && rangeStart > 0) {
                long bytesSkipped = inputStream.skip(rangeStart);
                if (bytesSkipped < rangeStart) {
                    throw new IOException(String.format(
                            "无法跳过足够的字节: 需要 %d, 实际 %d, 文件: %s",
                            rangeStart, bytesSkipped, fileName));
                }
                log.debug("Range请求跳过字节: {} bytes, 文件: {}", bytesSkipped, fileName);
            }

            // ========== 分块传输核心循环 ==========
            // 决策理由：从FilePureServiceImpl转移的核心传输逻辑，使用缓冲区分块处理
            // 优势：内存占用恒定，支持大文件传输，提供进度监控
            byte[] buffer = new byte[bufferSize];  // 创建固定大小的缓冲区
            int bytesRead;                         // 每次实际读取的字节数
            long bytesRemaining = contentLength;   // 剩余需要传输的字节数

            // 分块读取和写入循环
            // 条件1：还有字节需要传输 (bytesRemaining > 0)
            // 条件2：输入流还有数据可读 (bytesRead != -1)
            while (bytesRemaining > 0 &&
                   (bytesRead = inputStream.read(buffer, 0,
                           (int) Math.min(buffer.length, bytesRemaining))) != -1) {

                // 将读取的数据写入输出目标（文件或HTTP响应）
                outputTarget.write(buffer, 0, bytesRead);
                transferredBytes += bytesRead;  // 累计已传输字节数
                bytesRemaining -= bytesRead;    // 减少剩余字节数

                // ========== 进度监控处理 ==========
                // 触发条件：enableProgress=true 且 progressMonitor!=null
                if (enableProgress && !ObjectUtils.isEmpty(progressMonitor)) {
                    long currentTime = System.currentTimeMillis();

                    // 检查是否应该报告进度
                    // 触发条件：首次报告 或 时间间隔≥2秒 或 字节间隔≥1MB
                    if (progressMonitor.shouldReportProgress(transferredBytes, currentTime)) {

                        // 计算当前传输速度（基于最近的传输数据）
                        double currentSpeed = calculateCurrentSpeed(
                                transferredBytes, lastProgressBytes,
                                currentTime, lastProgressTime);

                        // 调用进度监控器更新进度
                        // 监控器内部会根据配置决定是否输出日志（每10%或30秒）
                        progressMonitor.onProgress(transferredBytes, contentLength, currentSpeed);

                        // 更新进度报告状态，用于下次计算
                        lastProgressTime = currentTime;
                        lastProgressBytes = transferredBytes;
                    }
                }

                // ========== 传输速率控制 ==========
                // 决策理由：从FilePureServiceImpl转移的速率控制逻辑，避免占用过多带宽
                // 用途：在每个数据块传输后暂停指定时间，控制传输速度
                if (sendIntervalMs > 0) {
                    try {
                        Thread.sleep(sendIntervalMs);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new IOException("传输线程被中断: " + fileName, e);
                    }
                }
            }

            // ========== 传输完整性检查 ==========
            // 检查是否传输了所有请求的字节数
            if (bytesRemaining > 0) {
                log.warn("未传输完所有请求的字节. 剩余: {} bytes, 文件: {}", bytesRemaining, fileName);
            }

            // ========== 确保数据写入 ==========
            // 强制将缓冲区中的数据写入目标
            outputTarget.flush();

            // ========== 传输完成处理 ==========
            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            // 计算平均传输速度（字节/秒）
            double averageSpeed = elapsedTime > 0 ? (double) transferredBytes / (elapsedTime / 1000.0) : 0.0;

            // 触发进度监控器的传输完成回调
            // 生命周期触发：传输成功完成时自动触发
            if (enableProgress && !ObjectUtils.isEmpty(progressMonitor)) {
                progressMonitor.onTransferComplete(transferredBytes, elapsedTime, averageSpeed);
            }

            // 记录传输完成日志
            log.debug("文件传输完成: {} -> {}, 传输: {} bytes, 耗时: {} ms, 平均速度: {} MB/s",
                    fileName, outputTarget.getTargetDescription(), transferredBytes, elapsedTime,
                    String.format("%.2f", averageSpeed / (1024.0 * 1024.0)));

            return transferredBytes;

        } catch (IOException e) {
            // ========== 传输异常处理 ==========
            // 触发进度监控器的传输错误回调
            // 生命周期触发：传输过程中出现异常时自动触发
            if (enableProgress && !ObjectUtils.isEmpty(progressMonitor)) {
                progressMonitor.onTransferError(transferredBytes, contentLength, e);
            }

            // 记录传输失败日志
            log.error("文件传输失败: {} -> {}, 已传输: {} bytes, 错误: {}",
                    fileName, outputTarget.getTargetDescription(), transferredBytes, e.getMessage());

            throw e;
        } finally {
            // ========== 资源清理 ==========
            // 确保输出目标被正确关闭，释放系统资源
            // 无论传输成功还是失败，都必须执行资源清理
            try {
                outputTarget.close();
            } catch (IOException e) {
                log.warn("关闭输出目标失败: {}", outputTarget.getTargetDescription(), e);
            }
        }
    }

    // ==================== 兼容性方法（向后兼容） ====================

    /**
     * 从输入流传输到文件，带进度监控（兼容性方法）- 专用于文件上传
     * <p>
     * 使用分块传输方式，将输入流的数据写入到指定文件，同时提供进度监控。
     * 此方法保持向后兼容性，内部委托给统一的传输方法。
     * </p>
     *
     * <h3>使用场景：</h3>
     * <ul>
     *   <li><b>文件上传</b>：MultipartFile → 本地文件</li>
     *   <li><b>流式上传</b>：任意InputStream → 文件系统</li>
     *   <li><b>进度监控</b>：实时显示上传进度和速度</li>
     * </ul>
     *
     * <h3>进度监控触发：</h3>
     * <ul>
     *   <li>传输开始时自动触发onTransferStart</li>
     *   <li>每2秒或每1MB触发onProgress</li>
     *   <li>传输完成时自动触发onTransferComplete</li>
     *   <li>传输出错时自动触发onTransferError</li>
     * </ul>
     *
     * @param inputStream 输入流（如MultipartFile的InputStream）
     * @param targetPath 目标文件路径
     * @param fileName 文件名（用于进度显示和日志）
     * @param totalBytes 总字节数（用于进度计算，如果未知可传入-1）
     * @param progressMonitor 进度监控器，可以为null（为null时不进行进度监控）
     * @return 实际传输的字节数
     * @throws IOException 如果传输过程中发生IO错误
     */
    public static long transferFromStreamToFile(InputStream inputStream,
                                              Path targetPath,
                                              String fileName,
                                              long totalBytes,
                                              FileTransferProgressMonitor progressMonitor) throws IOException {

        // 确保目标目录存在（复用逻辑）
        ensureParentDirectoryExists(targetPath);

        // 创建上传专用的传输配置
        // 自动根据文件大小选择合适的缓冲区大小和进度监控设置
        TransferConfig config = TransferConfig.forUpload(totalBytes);

        // 创建文件输出目标
        // CREATE: 如果文件不存在则创建
        // WRITE: 允许写入操作
        // TRUNCATE_EXISTING: 如果文件已存在则清空内容
        OutputStream outputStream = Files.newOutputStream(targetPath,
                StandardOpenOption.CREATE,
                StandardOpenOption.WRITE,
                StandardOpenOption.TRUNCATE_EXISTING);

        // 包装为统一的输出目标接口
        FileOutputTarget outputTarget =
                new FileOutputTarget(outputStream, targetPath);

        // 委托给统一的传输方法，复用核心传输逻辑
        return transferWithProgress(inputStream, outputTarget, fileName, totalBytes, progressMonitor, config);
    }

    /**
     * 从输入流传输到文件，带进度监控和自定义缓冲区大小（兼容性方法）
     * <p>
     * 此方法提供自定义缓冲区大小的功能，适用于特殊性能需求的场景。
     * 已重构为复用核心传输逻辑和目录创建逻辑。
     * </p>
     *
     * @param inputStream 输入流
     * @param targetPath 目标文件路径
     * @param fileName 文件名（用于进度显示）
     * @param totalBytes 总字节数（用于进度计算，如果未知可传入-1）
     * @param progressMonitor 进度监控器，可以为null
     * @param bufferSize 缓冲区大小
     * @return 实际传输的字节数
     * @throws IOException 如果传输过程中发生IO错误
     */
    public static long transferFromStreamToFile(InputStream inputStream,
                                              Path targetPath,
                                              String fileName,
                                              long totalBytes,
                                              FileTransferProgressMonitor progressMonitor,
                                              int bufferSize) throws IOException {

        // 确保目标目录存在（复用逻辑）
        ensureParentDirectoryExists(targetPath);

        // 创建自定义缓冲区大小的传输配置
        TransferConfig config = TransferConfig.builder()
                .transferType("upload")
                .bufferSize(bufferSize)
                .enableProgressMonitoring(!ObjectUtils.isEmpty(progressMonitor))
                .build();

        // 创建文件输出目标
        OutputStream outputStream = Files.newOutputStream(targetPath,
                StandardOpenOption.CREATE,
                StandardOpenOption.WRITE,
                StandardOpenOption.TRUNCATE_EXISTING);

        FileOutputTarget outputTarget = new FileOutputTarget(outputStream, targetPath);

        // 委托给统一的传输方法
        return transferWithProgress(inputStream, outputTarget, fileName, totalBytes, progressMonitor, config);
    }

    /**
     * 从文件传输到HTTP响应，带进度监控（专用于文件下载）
     * <p>
     * 用于文件下载场景，将文件内容分块读取并发送到HTTP响应。
     * 支持Range请求和进度监控，适用于大文件下载和断点续传。
     * </p>
     *
     * <h3>使用场景：</h3>
     * <ul>
     *   <li><b>文件下载</b>：本地文件 → HTTP响应流</li>
     *   <li><b>Range请求</b>：支持HTTP Range头，实现断点续传</li>
     *   <li><b>流式下载</b>：分块发送，避免大文件内存溢出</li>
     *   <li><b>进度监控</b>：实时显示下载进度和速度</li>
     * </ul>
     *
     * <h3>进度监控特点：</h3>
     * <ul>
     *   <li>支持Range请求的进度计算（基于实际传输字节数）</li>
     *   <li>实时速度计算和剩余时间估算</li>
     *   <li>智能日志输出频率控制</li>
     * </ul>
     *
     * @param sourcePath 源文件路径（必须存在且可读）
     * @param emitter HTTP响应发送器（Spring的ResponseBodyEmitter）
     * @param fileName 文件名（用于进度显示和日志）
     * @param progressMonitor 进度监控器，可以为null（为null时不进行进度监控）
     * @param config 传输配置（包含Range信息、缓冲区大小等）
     * @return 实际传输的字节数
     * @throws IOException 如果传输过程中发生IO错误
     */
    public static long transferFromFileToEmitter(Path sourcePath,
                                               org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter emitter,
                                               String fileName,
                                               FileTransferProgressMonitor progressMonitor,
                                               TransferConfig config) throws IOException {

        // 参数验证：确保源文件存在且可读
        // 使用ObjectUtils.isEmpty()判断Path对象是否为null
        if (ObjectUtils.isEmpty(sourcePath) || !Files.exists(sourcePath)) {
            throw new IllegalArgumentException("源文件不存在: " + sourcePath);
        }

        // 获取文件总大小，用于进度计算
        long totalBytes = Files.size(sourcePath);

        // 创建HTTP响应输出目标
        // 包装ResponseBodyEmitter为统一的输出接口
        EmitterOutputTarget outputTarget =
                new EmitterOutputTarget(emitter, "文件下载: " + fileName);

        // 创建文件输入流
        InputStream inputStream = Files.newInputStream(sourcePath);

        try {
            // 委托给统一的传输方法
            // 支持Range请求、进度监控、分块传输等所有功能
            return transferWithProgress(inputStream, outputTarget, fileName, totalBytes, progressMonitor, config);
        } finally {
            // 确保输入流被正确关闭，释放文件句柄
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("关闭输入流失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 从文件传输到输出流，带进度监控（重构版本 - 复用核心传输逻辑）
     * <p>
     * 用于文件下载场景，将文件内容分块读取并写入到输出流。
     * 此方法已重构为使用统一的传输核心，消除代码重复。
     * </p>
     *
     * <h3>重构优势：</h3>
     * <ul>
     *   <li><b>代码复用</b>：使用统一的transferWithProgress方法</li>
     *   <li><b>一致性</b>：与其他传输方法保持相同的行为</li>
     *   <li><b>维护性</b>：减少重复代码，降低维护成本</li>
     *   <li><b>功能完整</b>：自动支持Range请求等高级功能</li>
     * </ul>
     *
     * @param sourcePath 源文件路径
     * @param outputStream 输出流
     * @param fileName 文件名（用于进度显示）
     * @param progressMonitor 进度监控器，可以为null
     * @return 实际传输的字节数
     * @throws IOException 如果传输过程中发生IO错误
     */
    public static long transferFromFileToStream(Path sourcePath,
                                              OutputStream outputStream,
                                              String fileName,
                                              FileTransferProgressMonitor progressMonitor) throws IOException {

        // 参数验证
        if (ObjectUtils.isEmpty(sourcePath) || !Files.exists(sourcePath)) {
            throw new IllegalArgumentException("源文件不存在: " + sourcePath);
        }
        if (ObjectUtils.isEmpty(outputStream)) {
            throw new IllegalArgumentException("输出流不能为null");
        }

        // 获取文件大小，用于进度计算和缓冲区优化
        long totalBytes = Files.size(sourcePath);

        // 创建下载专用的传输配置
        TransferConfig config = TransferConfig.forDownload(totalBytes);

        // 创建输入流
        InputStream inputStream = Files.newInputStream(sourcePath);

        // 创建输出目标包装器（使用工厂方法简化代码）
        OutputTarget outputTarget = createOutputStreamTarget(outputStream,
                "输出流: " + outputStream.getClass().getSimpleName());

        try {
            // 委托给统一的传输方法，复用所有核心逻辑
            return transferWithProgress(inputStream, outputTarget, fileName, totalBytes, progressMonitor, config);
        } finally {
            // 确保输入流被正确关闭
            if (!ObjectUtils.isEmpty(inputStream)) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流失败: {}", e.getMessage());
                }
            }
        }
    }

    // ==================== 工厂方法 ====================

    /**
     * 创建输出流包装器
     * <p>
     * 将普通的OutputStream包装为OutputTarget接口，用于统一的传输方法。
     * 这是一个工厂方法，简化了OutputTarget的创建过程。
     * </p>
     *
     * @param outputStream 要包装的输出流
     * @param description 输出目标的描述信息
     * @return OutputTarget接口实现
     */
    private static OutputTarget createOutputStreamTarget(OutputStream outputStream, String description) {
        return new OutputTarget() {
            private long bytesWritten = 0;

            @Override
            public void write(byte[] data, int offset, int length) throws IOException {
                outputStream.write(data, offset, length);
                bytesWritten += length;
            }

            @Override
            public void flush() throws IOException {
                outputStream.flush();
            }

            @Override
            public void close() throws IOException {
                // 不关闭外部传入的输出流，由调用者负责
            }

            @Override
            public String getTargetType() {
                return "stream";
            }

            @Override
            public String getTargetDescription() {
                return description;
            }

            @Override
            public boolean isAvailable() {
                return !ObjectUtils.isEmpty(outputStream);
            }

            @Override
            public long getBytesWritten() {
                return bytesWritten;
            }
        };
    }

    // ==================== 辅助方法 ====================

    /**
     * 确保目标文件的父目录存在
     * <p>
     * 检查目标文件路径的父目录是否存在，如果不存在则创建。
     * 这是一个通用的辅助方法，避免在多个地方重复相同的逻辑。
     * </p>
     *
     * @param targetPath 目标文件路径
     * @throws IOException 如果创建目录失败
     */
    private static void ensureParentDirectoryExists(Path targetPath) throws IOException {
        Path parentDir = targetPath.getParent();
        if (!ObjectUtils.isEmpty(parentDir) && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
            log.debug("创建目录: {}", parentDir);
        }
    }

    /**
     * 验证传输参数的有效性
     * <p>
     * 在传输开始前验证所有必需参数，确保传输过程的安全性和可靠性。
     * 这是防御性编程的重要组成部分。
     * </p>
     *
     * @param inputStream 输入流（数据源）
     * @param outputTarget 输出目标（数据目的地）
     * @param fileName 文件名（用于日志和错误信息）
     * @param config 传输配置（包含缓冲区大小等参数）
     * @throws IllegalArgumentException 如果任何参数无效
     */
    private static void validateTransferParameters(InputStream inputStream,
                                                 OutputTarget outputTarget,
                                                 String fileName,
                                                 TransferConfig config) {
        // 使用ObjectUtils.isEmpty()判断对象是否为null
        if (ObjectUtils.isEmpty(inputStream)) {
            throw new IllegalArgumentException("输入流不能为null");
        }
        if (ObjectUtils.isEmpty(outputTarget)) {
            throw new IllegalArgumentException("输出目标不能为null");
        }
        // 使用StringUtils.hasText()判断字符串是否有效（非null、非空、非空白）
        if (!StringUtils.hasText(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (ObjectUtils.isEmpty(config)) {
            throw new IllegalArgumentException("传输配置不能为null");
        }
        if (config.getBufferSize() <= 0) {
            throw new IllegalArgumentException("缓冲区大小必须大于0");
        }
        if (!outputTarget.isAvailable()) {
            throw new IllegalArgumentException("输出目标不可用: " + outputTarget.getTargetDescription());
        }
    }

    /**
     * 计算当前传输速度（实时速度计算）
     * <p>
     * 基于最近的传输数据计算实时传输速度，用于进度监控器的速度显示。
     * 采用滑动窗口的方式计算，提供更准确的实时速度信息。
     * </p>
     *
     * <h3>计算公式：</h3>
     * <pre>
     * 速度 = (当前字节数 - 上次字节数) / (当前时间 - 上次时间) * 1000
     * 单位：字节/秒
     * </pre>
     *
     * @param currentBytes 当前已传输字节数
     * @param lastBytes 上次记录的已传输字节数
     * @param currentTime 当前时间（毫秒）
     * @param lastTime 上次记录的时间（毫秒）
     * @return 当前传输速度（字节/秒），如果时间差≤0则返回0.0
     */
    private static double calculateCurrentSpeed(long currentBytes, long lastBytes,
                                              long currentTime, long lastTime) {
        long bytesDiff = currentBytes - lastBytes;  // 字节增量
        long timeDiff = currentTime - lastTime;     // 时间增量（毫秒）

        // 防止除零错误，时间差必须大于0
        if (timeDiff <= 0) {
            return 0.0;
        }

        // 转换为字节/秒：(字节增量 / 毫秒增量) * 1000
        return (double) bytesDiff / (timeDiff / 1000.0);
    }
}
