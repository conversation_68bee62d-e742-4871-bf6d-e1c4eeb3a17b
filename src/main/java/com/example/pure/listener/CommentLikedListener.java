package com.example.pure.listener;

import com.example.pure.event.CommentLikedEvent;
import com.example.pure.model.dto.request.messages.SystemMessageRequestDTO;
import com.example.pure.service.messages.MessagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 评论点赞事件监听器
 * <p>
 * 负责监听 {@link CommentLikedEvent} 事件，并在事件触发时执行相应的业务逻辑，
 * 例如向被点赞的用户发送系统通知。
 * </p>
 */
@Slf4j
@Component
public class CommentLikedListener {

    private final MessagesService messagesService;

    @Autowired
    public CommentLikedListener(MessagesService messagesService) {
        this.messagesService = messagesService;
    }

    /**
     * 处理评论被点赞事件。
     * <p>
     * 此方法被 {@link Async} 注解标记，将在独立的线程中异步执行，以避免阻塞主业务流程。
     * 它会构建一个系统消息并调用 {@link MessagesService} 发送通知给评论的作者。
     * 如果用户给自己点赞，则不会发送通知。
     * </p>
     *
     * @param event 包含点赞相关信息的事件对象
     */
    @Async
    @EventListener
    public void handleCommentLikedEvent(CommentLikedEvent event) {
        try {
            // 防止用户给自己点赞时发送通知
            if (event.getLikingUserId().equals(event.getCommentAuthorId())) {
                log.info("用户 {} 给自己的评论点赞，不发送通知。", event.getLikingUsername());
                return;
            }

            log.info("接收到评论点赞事件：用户 {} (ID: {}) 点赞了用户 {} (ID: {}) 的评论 (ID: {})",
                    event.getLikingUsername(), event.getLikingUserId(), event.getCommentAuthorId(), event.getCommentAuthorId(), event.getCommentId());

            String title = "你收到了一个新的赞";
            String content = "用户 " + event.getLikingUsername() + " 赞了你的评论。";

            SystemMessageRequestDTO messageRequest = SystemMessageRequestDTO.builder()
                    .recipientUserId(event.getCommentAuthorId())
                    .title(title)
                    .content(content)
                    .messageType("like") // 消息类型定义为 'like'
                    .build();

            messagesService.sendSystemMessage(messageRequest);

            log.info("已成功为用户 {} 发送点赞通知。", event.getCommentAuthorId());

        } catch (Exception e) {
            log.error("处理评论点赞事件并发送通知时发生错误: {}", e.getMessage(), e);
        }
    }
}
