package com.example.pure.listener;

import com.example.pure.event.CommentReplyEvent;
import com.example.pure.mapper.primary.VideoCommentInteractionMapper;
import com.example.pure.service.messages.MessagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CommentReplyListener {

    private final MessagesService messagesService;
    VideoCommentInteractionMapper videoCommentInteractionMapper;

    @Autowired
    public CommentReplyListener(MessagesService messagesService, VideoCommentInteractionMapper videoCommentInteractionMapper) {
        this.messagesService = messagesService;
        this.videoCommentInteractionMapper = videoCommentInteractionMapper;
    }

    @Async // 异步处理，避免阻塞主线程
    @EventListener
    public void handleCommentReplyEvent(CommentReplyEvent event) {
        log.info("接收到评论回复事件：父评论ID {}，回复者ID {}", event.getParentCommentId(), event.getReplierUserId());
        try {
            // 获取父评论的作者ID
            Long parentCommentAuthorId = videoCommentInteractionMapper.getUserIdByCommentId(event.getParentCommentId());

            if (parentCommentAuthorId != null) {
                // 此处的自我回复检查已被移至 PureVideoInteractionServiceImpl
                // 现在事件发布前已确保回复者与被回复者不是同一人
                messagesService.sendCommentReplyNotification(
                        event.getReplierUserId(),
                        parentCommentAuthorId,
                        event.getCommentContent(),
                        event.getVideoEpisodesId()
                );
                log.info("已发送通知给用户 {} (父评论作者)，针对用户 {} (回复者) 的回复", parentCommentAuthorId, event.getReplierUserId());
            } else {
                // 此日志理论上不应该再触发，因为如果 parentCommentAuthorId 为 null，事件就不会在 PureVideoInteractionServiceImpl 中发布
                log.warn("评论回复事件处理：未找到父评论ID {} 的作者。通知未发送。(此情况应在事件发布前被捕获)", event.getParentCommentId());
            }
        } catch (Exception e) {
            log.error("处理评论回复事件时发生错误：父评论ID {}，错误信息：{}", event.getParentCommentId(), e.getMessage(), e);
            // 根据需要处理异常，例如记录到特定日志或发送警报
        }
    }
}
