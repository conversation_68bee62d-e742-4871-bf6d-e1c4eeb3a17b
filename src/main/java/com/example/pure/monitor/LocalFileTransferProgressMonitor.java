package com.example.pure.monitor;

import com.example.pure.model.dto.response.file.user.TransferProgress;
import com.example.pure.util.TransferUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 本地文件传输进度监控器实现类
 * <p>
 * 提供本地文件传输的进度监控功能，包括：
 * - 实时进度跟踪和显示
 * - 传输速度计算（当前速度和平均速度）
 * - 时间估算（已用时间和预计剩余时间）
 * - 智能日志输出（避免过于频繁的日志）
 * </p>
 *
 * <p>
 * 性能优化：
 * - 智能回调频率控制：避免过于频繁的进度回调影响传输性能
 * - 滑动窗口速度计算：提供更准确的实时速度计算
 * - 自适应日志间隔：根据文件大小调整日志输出频率
 * </p>
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 * @see FileTransferProgressMonitor
 * @see TransferProgress
 */
@Slf4j
public class LocalFileTransferProgressMonitor implements FileTransferProgressMonitor {

    /**
     * 当前传输进度信息
     */
    private TransferProgress currentProgress;

    /**
     * 上次进度报告的时间戳（毫秒）
     */
    private long lastReportTime;

    /**
     * 上次进度报告时的已传输字节数
     */
    private long lastReportedBytes;

    /**
     * 速度计算开始时间（毫秒）
     */
    private long speedCalculationStartTime;

    /**
     * 速度计算开始时的字节数
     */
    private long speedCalculationStartBytes;

    /**
     * 进度报告时间间隔（毫秒）
     */
    private final long progressReportIntervalMs;

    /**
     * 进度报告字节间隔
     */
    private final long progressReportIntervalBytes;

    /**
     * 日志输出进度间隔（0.1表示每10%输出一次）
     */
    private final double progressLogInterval;

    /**
     * 默认构造函数
     * <p>
     * 使用默认的进度报告参数：
     * - 时间间隔：2秒
     * - 字节间隔：1MB
     * - 日志间隔：10%
     * </p>
     */
    public LocalFileTransferProgressMonitor() {
        this(2000L, 1024 * 1024L, 0.1);
    }

    /**
     * 自定义构造函数
     * <p>
     * 允许自定义进度报告和日志输出的频率控制参数，
     * 以适应不同场景下的性能需求。
     * </p>
     *
     * @param progressReportIntervalMs 进度报告时间间隔（毫秒），建议值：1000-5000
     * @param progressReportIntervalBytes 进度报告字节间隔，建议值：1MB-10MB
     * @param progressLogInterval 日志输出进度间隔（0.1表示每10%输出一次），建议值：0.05-0.2
     */
    public LocalFileTransferProgressMonitor(long progressReportIntervalMs,
                                          long progressReportIntervalBytes,
                                          double progressLogInterval) {
        this.progressReportIntervalMs = progressReportIntervalMs;
        this.progressReportIntervalBytes = progressReportIntervalBytes;
        this.progressLogInterval = progressLogInterval;
    }

    /**
     * 传输开始时的回调方法
     * <p>
     * 初始化传输进度监控器的状态，包括：
     * - 创建初始的传输进度对象
     * - 重置所有计时器和计数器
     * - 验证传输参数的有效性
     * - 输出传输开始的日志信息
     * </p>
     *
     * @param fileName 要传输的文件名，不能为空或null
     * @param totalBytes 文件总字节数，必须大于0
     * @param transferType 传输类型（如"上传"、"下载"等），用于日志显示
     *
     * @throws IllegalArgumentException 当fileName为空或totalBytes小于等于0时抛出
     *
     * @see TransferUtils#validateBasicTransferParameters(String, long)
     * @see TransferProgress.TransferStatus#IN_PROGRESS
     */
    @Override
    public void onTransferStart(String fileName, long totalBytes, String transferType) {
        // 使用工具类验证基本参数
        TransferUtils.validateBasicTransferParameters(fileName, totalBytes);

        long currentTime = System.currentTimeMillis();

        this.currentProgress = TransferProgress.builder()
                .fileName(fileName)
                .transferType(transferType)
                .totalBytes(totalBytes)
                .transferredBytes(0L)
                .progressPercent(0.0)
                .currentSpeedBytesPerSecond(0.0)
                .averageSpeedBytesPerSecond(0.0)
                .startTimeMs(currentTime)
                .currentTimeMs(currentTime)
                .elapsedTimeMs(0L)
                .estimatedRemainingTimeMs(0L)
                .status(TransferProgress.TransferStatus.IN_PROGRESS)
                .build();

        // 初始化速度计算参数
        this.lastReportTime = currentTime;
        this.lastReportedBytes = 0L;
        this.speedCalculationStartTime = currentTime;
        this.speedCalculationStartBytes = 0L;

        log.info("开始{}文件: {} ({})", transferType, fileName,
                TransferUtils.formatBytes(totalBytes));
    }


    /**
     * 传输进度更新回调方法
     * <p>
     * 实时更新传输进度信息，包括：
     * - 更新已传输字节数和进度百分比
     * - 计算当前传输速度和平均传输速度
     * - 估算剩余传输时间
     * - 根据配置的间隔智能输出进度日志
     * </p>
     * <p>
     * 性能优化特性：
     * - 智能速度计算：优先使用传入的当前速度，避免重复计算
     * - 滑动窗口计算：基于最近的传输数据计算实时速度
     * - 自适应日志：根据进度百分比和时间间隔智能控制日志输出频率
     * </p>
     *
     * @param transferredBytes 已传输的字节数，必须 >= 0 且 <= totalBytes
     * @param totalBytes 文件总字节数，必须 > 0
     * @param currentSpeedBytesPerSecond 当前传输速度（字节/秒），如果 <= 0 则自动重新计算
     *
     * @see TransferUtils#calculateProgress(long, long)
     * @see TransferUtils#calculateSpeed(long, long)
     * @see TransferUtils#estimateRemainingTime(long, double)
     * @see #shouldLogProgress(long, long)
     */
    @Override
    public void onProgress(long transferredBytes, long totalBytes, double currentSpeedBytesPerSecond) {
        if (currentProgress == null) {
            log.warn("进度更新时发现未初始化的监控器状态");
            return;
        }

        long currentTime = System.currentTimeMillis();
        long elapsedTime = currentTime - currentProgress.getStartTimeMs();

        // 更新进度信息
        currentProgress.setTransferredBytes(transferredBytes);
        currentProgress.setCurrentTimeMs(currentTime);
        currentProgress.setElapsedTimeMs(elapsedTime);
        currentProgress.setProgressPercent(TransferUtils.calculateProgress(transferredBytes, totalBytes));

        // 计算平均速度
        double averageSpeed = TransferUtils.calculateSpeed(transferredBytes, elapsedTime);
        currentProgress.setAverageSpeedBytesPerSecond(averageSpeed);

        // 使用传入的当前速度或重新计算
        if (currentSpeedBytesPerSecond > 0) {
            currentProgress.setCurrentSpeedBytesPerSecond(currentSpeedBytesPerSecond);
        } else {
            // 重新计算当前速度（基于最近的传输数据）
            long timeDiff = currentTime - lastReportTime;
            long bytesDiff = transferredBytes - lastReportedBytes;
            double realtimeSpeed = TransferUtils.calculateSpeed(bytesDiff, timeDiff);
            currentProgress.setCurrentSpeedBytesPerSecond(realtimeSpeed);
        }

        // 计算预计剩余时间
        long remainingBytes = totalBytes - transferredBytes;
        long estimatedRemainingTime = TransferUtils.estimateRemainingTime(
                remainingBytes, currentProgress.getCurrentSpeedBytesPerSecond());
        currentProgress.setEstimatedRemainingTimeMs(estimatedRemainingTime);

        // 检查是否需要输出日志
        if (shouldLogProgress(transferredBytes, currentTime)) {
            logProgress();
            // 更新最后报告的状态
            lastReportTime = currentTime;
            lastReportedBytes = transferredBytes;
        }
    }

    /**
     * 传输完成时的回调方法
     * <p>
     * 更新传输状态为完成，并输出最终的传输统计信息，包括：
     * - 设置传输状态为 COMPLETED
     * - 更新最终的传输统计数据
     * - 输出包含总大小、耗时、平均速度的完成日志
     * </p>
     *
     * @param totalBytes 文件总字节数，应与传输开始时的值一致
     * @param elapsedTimeMs 总传输耗时（毫秒）
     * @param averageSpeedBytesPerSecond 整个传输过程的平均速度（字节/秒）
     *
     * @see TransferProgress.TransferStatus#COMPLETED
     * @see #formatBytes(long)
     * @see #formatDuration(long)
     * @see #formatSpeed(double)
     */
    @Override
    public void onTransferComplete(long totalBytes, long elapsedTimeMs, double averageSpeedBytesPerSecond) {
        if (currentProgress != null) {
            currentProgress.setStatus(TransferProgress.TransferStatus.COMPLETED);
            currentProgress.setTransferredBytes(totalBytes);
            currentProgress.setProgressPercent(1.0);
            currentProgress.setElapsedTimeMs(elapsedTimeMs);
            currentProgress.setAverageSpeedBytesPerSecond(averageSpeedBytesPerSecond);
            currentProgress.setEstimatedRemainingTimeMs(0L);
        }

        log.info("{}完成: {} | 总大小: {} | 耗时: {} | 平均速度: {}",
                currentProgress != null ? currentProgress.getTransferType() : "传输",
                currentProgress != null ? currentProgress.getFileName() : "未知文件",
                formatBytes(totalBytes),
                formatDuration(elapsedTimeMs),
                formatSpeed(averageSpeedBytesPerSecond));
    }

    /**
     * 传输错误时的回调方法
     * <p>
     * 处理传输过程中发生的错误，包括：
     * - 设置传输状态为 ERROR
     * - 记录当前已传输的字节数
     * - 输出详细的错误日志信息
     * </p>
     *
     * @param transferredBytes 错误发生时已传输的字节数
     * @param totalBytes 文件总字节数
     * @param error 发生的异常对象，用于获取错误信息
     *
     * @see TransferProgress.TransferStatus#ERROR
     */
    @Override
    public void onTransferError(long transferredBytes, long totalBytes, Exception error) {
        if (currentProgress != null) {
            currentProgress.setStatus(TransferProgress.TransferStatus.ERROR);
            currentProgress.setTransferredBytes(transferredBytes);
        }

        log.error("{}失败: {} | 已传输: {} / {} | 错误: {}",
                currentProgress != null ? currentProgress.getTransferType() : "传输",
                currentProgress != null ? currentProgress.getFileName() : "未知文件",
                formatBytes(transferredBytes),
                formatBytes(totalBytes),
                error.getMessage());
    }

    /**
     * 获取当前传输进度信息
     * <p>
     * 返回当前的传输进度对象，包含所有实时的传输统计信息。
     * 如果传输尚未开始，则返回 null。
     * </p>
     *
     * @return 当前的传输进度对象，如果传输未开始则返回 null
     *
     * @see TransferProgress
     */
    @Override
    public TransferProgress getCurrentProgress() {
        return currentProgress;
    }

    /**
     * 判断是否应该报告传输进度
     * <p>
     * 基于时间间隔和字节间隔的双重条件来判断是否需要报告进度，
     * 避免过于频繁的进度回调影响传输性能。
     * </p>
     *
     * <p>
     * 判断逻辑：
     * - 首次调用：总是返回 true
     * - 时间间隔：距离上次报告超过配置的时间间隔
     * - 字节间隔：距离上次报告超过配置的字节间隔
     * - 满足任一条件：即返回 true
     * </p>
     *
     * @param transferredBytes 当前已传输的字节数
     * @param currentTimeMs 当前时间戳（毫秒）
     * @return true 如果应该报告进度，false 否则
     *
     * @see #progressReportIntervalMs
     * @see #progressReportIntervalBytes
     */
    @Override
    public boolean shouldReportProgress(long transferredBytes, long currentTimeMs) {
        if (lastReportTime == 0) {
            return true; // 第一次总是报告
        }

        long timeDiff = currentTimeMs - lastReportTime;
        long bytesDiff = transferredBytes - lastReportedBytes;

        // 基于时间间隔或字节间隔判断
        return timeDiff >= progressReportIntervalMs || bytesDiff >= progressReportIntervalBytes;
    }

    /**
     * 检查是否应该输出进度日志
     * <p>
     * 基于进度百分比和时间间隔的双重条件来决定是否输出日志，
     * 实现智能的日志输出频率控制，避免过于频繁的日志输出影响性能。
     * </p>
     *
     * <p>
     * 判断策略：
     * - 百分比阈值：每达到配置的百分比间隔（如10%）输出一次日志
     * - 时间阈值：至少每30秒输出一次日志，防止长时间无日志输出
     * - 满足任一条件：即输出日志
     * </p>
     *
     * @param transferredBytes 当前已传输的字节数
     * @param currentTimeMs 当前时间戳（毫秒）
     * @return true 如果应该输出日志，false 否则
     *
     * @see #progressLogInterval
     */
    private boolean shouldLogProgress(long transferredBytes, long currentTimeMs) {
        if (currentProgress == null || currentProgress.getTotalBytes() <= 0) {
            return false;
        }

        // 基于进度百分比的日志输出
        double currentPercent = (double) transferredBytes / currentProgress.getTotalBytes();
        double lastPercent = (double) lastReportedBytes / currentProgress.getTotalBytes();

        // 每达到指定百分比间隔就输出日志
        boolean percentageThreshold = Math.floor(currentPercent / progressLogInterval) > Math.floor(lastPercent / progressLogInterval);

        // 或者基于时间间隔（至少每30秒输出一次）
        boolean timeThreshold = (currentTimeMs - lastReportTime) >= 30000;

        return percentageThreshold || timeThreshold;
    }

    /**
     * 输出详细的传输进度日志
     * <p>
     * 输出包含完整传输统计信息的格式化日志，包括：
     * - 传输类型和进度百分比
     * - 已传输大小和文件总大小
     * - 当前传输速度和平均传输速度
     * - 已用时间和预计剩余时间
     * </p>
     *
     * <p>
     * 日志格式示例：
     * <pre>
     * 上传进度: 45.2% | 452.3 MB / 1.0 GB | 当前速度: 15.6 MB/s | 平均速度: 12.3 MB/s | 已用时间: 2分钟15秒 | 预计剩余: 3分钟42秒
     * </pre>
     * </p>
     *
     * @see TransferProgress#getFormattedProgress()
     * @see TransferProgress#getFormattedTransferredSize()
     * @see TransferProgress#getFormattedCurrentSpeed()
     */
    private void logProgress() {
        if (currentProgress == null) {
            return;
        }

        log.info("{}进度: {} | {} / {} | 当前速度: {} | 平均速度: {} | 已用时间: {} | 预计剩余: {} | [注意：这是服务器文件读取速度，客户端网络速度会更低]",
                currentProgress.getTransferType(),
                currentProgress.getFormattedProgress(),
                currentProgress.getFormattedTransferredSize(),
                currentProgress.getFormattedFileSize(),
                currentProgress.getFormattedCurrentSpeed(),
                currentProgress.getFormattedAverageSpeed(),
                currentProgress.getFormattedElapsedTime(),
                currentProgress.getFormattedEstimatedRemainingTime());
    }

    /**
     * 格式化字节数为人类可读的字符串
     * <p>
     * 将字节数转换为合适的单位（B、KB、MB、GB），
     * 自动选择最合适的单位以提高可读性。
     * </p>
     *
     * @param bytes 要格式化的字节数，必须 >= 0
     * @return 格式化后的字符串，如 "1.23 MB"、"456.78 KB" 等
     *
     * <p>
     * 示例：
     * <pre>
     * formatBytes(1024)       → "1.00 KB"
     * formatBytes(1536)       → "1.50 KB"
     * formatBytes(1048576)    → "1.00 MB"
     * formatBytes(1073741824) → "1.00 GB"
     * </pre>
     * </p>
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 格式化传输速度为人类可读的字符串
     * <p>
     * 将字节/秒的速度转换为 MB/s 单位，保留两位小数。
     * 统一使用 MB/s 单位便于用户理解和比较。
     * </p>
     *
     * @param bytesPerSecond 传输速度（字节/秒），必须 >= 0
     * @return 格式化后的速度字符串，如 "15.67 MB/s"
     *
     * <p>
     * 示例：
     * <pre>
     * formatSpeed(1048576)   → "1.00 MB/s"
     * formatSpeed(16777216)  → "16.00 MB/s"
     * formatSpeed(1572864)   → "1.50 MB/s"
     * </pre>
     * </p>
     */
    private String formatSpeed(double bytesPerSecond) {
        return String.format("%.2f MB/s", bytesPerSecond / (1024.0 * 1024.0));
    }

    /**
     * 格式化时间长度为人类可读的字符串
     * <p>
     * 将毫秒时间转换为小时、分钟、秒的组合格式，
     * 根据时间长度自动选择合适的显示格式。
     * </p>
     *
     * @param durationMs 时间长度（毫秒），必须 >= 0
     * @return 格式化后的时间字符串
     *
     * <p>
     * 示例：
     * <pre>
     * formatDuration(5000)    → "5秒"
     * formatDuration(65000)   → "1分钟5秒"
     * formatDuration(3665000) → "1小时1分钟5秒"
     * </pre>
     * </p>
     */
    private String formatDuration(long durationMs) {
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
