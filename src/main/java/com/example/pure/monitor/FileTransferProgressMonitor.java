package com.example.pure.monitor;

import com.example.pure.model.dto.response.file.user.TransferProgress;

/**
 * 文件传输进度监控器接口
 * <p>
 * 提供统一的文件传输进度监控功能，支持上传、下载等各种文件传输场景。
 * 实现类可以根据具体需求提供不同的进度展示方式（日志、WebSocket、数据库等）。
 * </p>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li><b>文件上传</b>：监控本地文件上传到服务器的进度</li>
 *   <li><b>文件下载</b>：监控从服务器下载文件的进度</li>
 *   <li><b>文件复制</b>：监控本地文件复制操作的进度</li>
 *   <li><b>远程传输</b>：监控与远程服务器之间的文件传输</li>
 * </ul>
 *
 * <h3>设计原则：</h3>
 * <ul>
 *   <li><b>通用性</b>：接口设计通用，适用于各种文件传输场景</li>
 *   <li><b>解耦性</b>：业务逻辑与进度监控逻辑分离</li>
 *   <li><b>扩展性</b>：可以轻松添加新的监控实现</li>
 *   <li><b>性能友好</b>：避免频繁回调影响传输性能</li>
 * </ul>
 */
public interface FileTransferProgressMonitor {

    /**
     * 传输开始时的回调
     * <p>
     * 在文件传输开始时调用，用于初始化进度监控状态
     * </p>
     *
     * @param fileName 文件名
     * @param totalBytes 文件总大小（字节）
     * @param transferType 传输类型（如：upload、download、copy）
     */
    void onTransferStart(String fileName, long totalBytes, String transferType);

    /**
     * 传输进度更新时的回调
     * <p>
     * 在文件传输过程中定期调用，报告当前进度。
     * 实现类应该控制回调频率，避免过于频繁的调用影响性能。
     * </p>
     *
     * @param transferredBytes 已传输的字节数
     * @param totalBytes 文件总大小（字节）
     * @param currentSpeedBytesPerSecond 当前传输速度（字节/秒）
     */
    void onProgress(long transferredBytes, long totalBytes, double currentSpeedBytesPerSecond);

    /**
     * 传输完成时的回调
     * <p>
     * 在文件传输成功完成时调用，提供最终的统计信息
     * </p>
     *
     * @param totalBytes 文件总大小（字节）
     * @param elapsedTimeMs 总耗时（毫秒）
     * @param averageSpeedBytesPerSecond 平均传输速度（字节/秒）
     */
    void onTransferComplete(long totalBytes, long elapsedTimeMs, double averageSpeedBytesPerSecond);

    /**
     * 传输出错时的回调
     * <p>
     * 在文件传输过程中发生错误时调用
     * </p>
     *
     * @param transferredBytes 已传输的字节数
     * @param totalBytes 文件总大小（字节）
     * @param error 错误信息
     */
    void onTransferError(long transferredBytes, long totalBytes, Exception error);

    /**
     * 获取当前传输进度信息
     * <p>
     * 返回当前的传输进度快照，可用于外部查询
     * </p>
     *
     * @return 当前的传输进度信息，如果传输未开始则返回null
     */
    TransferProgress getCurrentProgress();

    /**
     * 检查是否应该报告进度
     * <p>
     * 用于控制进度报告的频率，避免过于频繁的回调
     * </p>
     *
     * @param transferredBytes 当前已传输字节数
     * @param currentTimeMs 当前时间戳（毫秒）
     * @return 如果应该报告进度返回true，否则返回false
     */
    boolean shouldReportProgress(long transferredBytes, long currentTimeMs);
}
