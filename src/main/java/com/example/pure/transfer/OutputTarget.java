package com.example.pure.transfer;

import java.io.IOException;

/**
 * 输出目标抽象接口
 * <p>
 * 定义统一的输出目标接口，支持不同类型的数据输出方式：
 * - 文件输出（用于上传场景）
 * - HTTP响应输出（用于下载场景）
 * - 网络流输出（用于远程传输场景）
 * </p>
 * 
 * <h3>设计目标：</h3>
 * <ul>
 *   <li><b>统一接口</b>：为不同的输出方式提供统一的操作接口</li>
 *   <li><b>解耦设计</b>：传输逻辑与具体输出方式解耦</li>
 *   <li><b>扩展性强</b>：可以轻松添加新的输出目标类型</li>
 *   <li><b>性能优化</b>：每种输出方式可以有针对性的优化</li>
 * </ul>
 */
public interface OutputTarget {

    /**
     * 写入数据到目标
     * <p>
     * 将指定的数据写入到输出目标，具体的写入方式由实现类决定
     * </p>
     * 
     * @param data 要写入的数据
     * @param offset 数据的起始偏移量
     * @param length 要写入的数据长度
     * @throws IOException 如果写入过程中发生IO错误
     */
    void write(byte[] data, int offset, int length) throws IOException;

    /**
     * 刷新输出缓冲区
     * <p>
     * 确保所有缓冲的数据都被写入到目标，具体行为由实现类决定
     * </p>
     * 
     * @throws IOException 如果刷新过程中发生IO错误
     */
    void flush() throws IOException;

    /**
     * 关闭输出目标
     * <p>
     * 关闭输出目标并释放相关资源，具体行为由实现类决定
     * </p>
     * 
     * @throws IOException 如果关闭过程中发生IO错误
     */
    void close() throws IOException;

    /**
     * 获取输出目标类型
     * <p>
     * 返回输出目标的类型标识，用于日志记录和调试
     * </p>
     * 
     * @return 输出目标类型（如："file"、"http"、"network"等）
     */
    String getTargetType();

    /**
     * 获取目标描述信息
     * <p>
     * 返回输出目标的描述信息，用于日志记录和调试
     * </p>
     * 
     * @return 目标描述信息（如：文件路径、HTTP连接信息等）
     */
    String getTargetDescription();

    /**
     * 检查输出目标是否可用
     * <p>
     * 检查输出目标是否处于可写入状态
     * </p>
     * 
     * @return 如果目标可用返回true，否则返回false
     */
    boolean isAvailable();

    /**
     * 获取已写入的字节数
     * <p>
     * 返回已经写入到目标的字节数，用于进度监控
     * </p>
     * 
     * @return 已写入的字节数
     */
    long getBytesWritten();
}
