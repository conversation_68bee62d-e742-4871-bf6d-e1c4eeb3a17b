package com.example.pure.transfer;

import com.example.pure.util.TransferUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件传输配置类
 * <p>
 * 封装文件传输过程中的各种配置参数，包括缓冲区大小、进度报告间隔、
 * 传输类型等。提供灵活的配置选项以适应不同的传输场景。
 * </p>
 * 
 * <h3>配置分类：</h3>
 * <ul>
 *   <li><b>性能配置</b>：缓冲区大小、发送间隔等</li>
 *   <li><b>监控配置</b>：进度报告间隔、日志级别等</li>
 *   <li><b>业务配置</b>：传输类型、Range请求支持等</li>
 * </ul>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferConfig {

    /**
     * 默认缓冲区大小：64KB
     * <p>
     * 64KB是一个经过优化的缓冲区大小，在大多数情况下能提供良好的性能
     * </p>
     */
    public static final int DEFAULT_BUFFER_SIZE = 64 * 1024;

    /**
     * 大文件缓冲区大小：256KB
     * <p>
     * 对于大文件（>100MB），使用更大的缓冲区可以提高传输效率
     * </p>
     */
    public static final int LARGE_FILE_BUFFER_SIZE = 256 * 1024;

    /**
     * 大文件阈值：100MB
     */
    public static final long LARGE_FILE_THRESHOLD = 100 * 1024 * 1024;

    /**
     * 默认进度报告时间间隔：2秒
     */
    public static final long DEFAULT_PROGRESS_INTERVAL_MS = 2000;

    /**
     * 默认进度报告字节间隔：1MB
     */
    public static final long DEFAULT_PROGRESS_INTERVAL_BYTES = 1024 * 1024;

    // ==================== 性能配置 ====================

    /**
     * 缓冲区大小（字节）
     * <p>
     * 控制每次读写操作的数据块大小，影响传输性能和内存使用
     * </p>
     */
    @Builder.Default
    private int bufferSize = DEFAULT_BUFFER_SIZE;

    /**
     * 发送间隔时间（毫秒）
     * <p>
     * 用于控制数据发送速率，0表示无间隔（最大速度）
     * 主要用于下载场景，避免占用过多带宽
     * </p>
     */
    @Builder.Default
    private long sendIntervalMs = 0;

    // ==================== 监控配置 ====================

    /**
     * 进度报告时间间隔（毫秒）
     * <p>
     * 控制进度监控器报告进度的时间间隔
     * </p>
     */
    @Builder.Default
    private long progressReportIntervalMs = DEFAULT_PROGRESS_INTERVAL_MS;

    /**
     * 进度报告字节间隔
     * <p>
     * 控制进度监控器报告进度的字节间隔
     * </p>
     */
    @Builder.Default
    private long progressReportIntervalBytes = DEFAULT_PROGRESS_INTERVAL_BYTES;

    /**
     * 进度日志间隔（百分比）
     * <p>
     * 控制进度日志输出的百分比间隔，0.1表示每10%输出一次日志
     * </p>
     */
    @Builder.Default
    private double progressLogInterval = 0.1;

    // ==================== 业务配置 ====================

    /**
     * 传输类型
     * <p>
     * 标识传输的类型，如："upload"、"download"、"copy"等
     * 用于日志记录和监控统计
     * </p>
     */
    private String transferType;

    /**
     * Range请求起始位置
     * <p>
     * 用于支持Range请求，指定传输的起始字节位置
     * 主要用于下载场景的断点续传
     * </p>
     */
    @Builder.Default
    private long rangeStart = 0;

    /**
     * Range请求结束位置
     * <p>
     * 用于支持Range请求，指定传输的结束字节位置
     * -1表示传输到文件末尾
     * </p>
     */
    @Builder.Default
    private long rangeEnd = -1;

    /**
     * 是否启用进度监控
     * <p>
     * 控制是否启用详细的进度监控功能
     * 禁用可以提高性能，但失去进度可见性
     * </p>
     */
    @Builder.Default
    private boolean enableProgressMonitoring = true;

    /**
     * 是否启用详细日志
     * <p>
     * 控制是否输出详细的传输日志
     * </p>
     */
    @Builder.Default
    private boolean enableVerboseLogging = false;

    // ==================== 便利方法 ====================

    /**
     * 检查是否为Range请求
     * 
     * @return 如果是Range请求返回true，否则返回false
     */
    public boolean isRangeRequest() {
        return rangeStart > 0 || rangeEnd >= 0;
    }

    /**
     * 计算Range请求的内容长度
     * 
     * @param totalFileSize 文件总大小
     * @return Range请求的内容长度
     */
    public long calculateRangeContentLength(long totalFileSize) {
        if (!isRangeRequest()) {
            return totalFileSize;
        }
        
        long effectiveEnd = rangeEnd >= 0 ? rangeEnd : totalFileSize - 1;
        return effectiveEnd - rangeStart + 1;
    }

    /**
     * 根据文件大小自动选择缓冲区大小
     * 
     * @param fileSize 文件大小
     * @return 推荐的缓冲区大小
     */
    public static int getRecommendedBufferSize(long fileSize) {
        return fileSize > LARGE_FILE_THRESHOLD ? LARGE_FILE_BUFFER_SIZE : DEFAULT_BUFFER_SIZE;
    }

    /**
     * 创建上传配置
     * 
     * @param fileSize 文件大小
     * @return 上传配置
     */
    public static TransferConfig forUpload(long fileSize) {
        return TransferConfig.builder()
                .transferType("upload")
                .bufferSize(getRecommendedBufferSize(fileSize))
                .enableProgressMonitoring(true)
                .build();
    }

    /**
     * 创建下载配置
     * 
     * @param fileSize 文件大小
     * @return 下载配置
     */
    public static TransferConfig forDownload(long fileSize) {
        return TransferConfig.builder()
                .transferType("download")
                .bufferSize(getRecommendedBufferSize(fileSize))
                .enableProgressMonitoring(true)
                .build();
    }

    /**
     * 创建Range下载配置
     * 
     * @param fileSize 文件大小
     * @param rangeStart Range起始位置
     * @param rangeEnd Range结束位置
     * @return Range下载配置
     */
    public static TransferConfig forRangeDownload(long fileSize, long rangeStart, long rangeEnd) {
        return TransferConfig.builder()
                .transferType("download")
                .bufferSize(getRecommendedBufferSize(fileSize))
                .rangeStart(rangeStart)
                .rangeEnd(rangeEnd)
                .enableProgressMonitoring(true)
                .build();
    }

    /**
     * 获取格式化的配置描述
     * 
     * @return 配置描述字符串
     */
    public String getConfigDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("TransferConfig{");
        sb.append("type=").append(transferType);
        sb.append(", bufferSize=").append(formatBytes(bufferSize));
        if (isRangeRequest()) {
            sb.append(", range=").append(rangeStart).append("-").append(rangeEnd);
        }
        sb.append(", progressMonitoring=").append(enableProgressMonitoring);
        sb.append("}");
        return sb.toString();
    }

    /**
     * 格式化字节数（使用公共工具类）
     */
    private String formatBytes(long bytes) {
        return TransferUtils.formatBytes(bytes);
    }
}
