package com.example.pure.transfer;

import com.example.pure.util.TransferUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Path;

/**
 * 文件输出目标实现类
 * <p>
 * 用于文件上传场景，将数据写入到本地文件系统。
 * 提供高效的文件写入操作和详细的状态监控。
 * </p>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li><b>文件上传</b>：将客户端上传的文件保存到服务器磁盘</li>
 *   <li><b>文件复制</b>：在服务器内部进行文件复制操作</li>
 *   <li><b>数据备份</b>：将数据流备份到文件</li>
 * </ul>
 *
 * <h3>性能特性：</h3>
 * <ul>
 *   <li><b>缓冲写入</b>：利用OutputStream的内部缓冲提高写入效率</li>
 *   <li><b>状态监控</b>：实时跟踪写入字节数和状态</li>
 *   <li><b>异常安全</b>：确保在异常情况下正确关闭资源</li>
 * </ul>
 */
@Slf4j
public class FileOutputTarget implements OutputTarget {

    private final OutputStream outputStream;
    private final Path targetPath;
    private long bytesWritten;
    private boolean closed;

    /**
     * 构造函数
     *
     * @param outputStream 目标文件的输出流
     * @param targetPath 目标文件路径（用于日志和描述）
     */
    public FileOutputTarget(OutputStream outputStream, Path targetPath) {
        this.outputStream = outputStream;
        this.targetPath = targetPath;
        this.bytesWritten = 0;
        this.closed = false;
    }

    /**
     * 将数据写入到目标文件
     * <p>
     * 这是文件上传的核心方法，将接收到的数据块写入到本地文件系统。
     * 方法会进行严格的参数验证，确保数据完整性和操作安全性。
     * </p>
     *
     * <h3>执行流程：</h3>
     * <ol>
     *   <li>检查文件输出目标是否已关闭</li>
     *   <li>验证输入参数的有效性</li>
     *   <li>调用底层OutputStream写入数据</li>
     *   <li>更新已写入字节数统计</li>
     *   <li>记录写入操作日志</li>
     * </ol>
     *
     * <h3>异常处理：</h3>
     * <ul>
     *   <li>如果目标已关闭，抛出IOException</li>
     *   <li>如果参数无效，抛出IllegalArgumentException</li>
     *   <li>如果写入失败，记录错误日志并重新抛出IOException</li>
     * </ul>
     *
     * @param data 要写入的数据数组
     * @param offset 数据数组中的起始偏移量
     * @param length 要写入的数据长度
     * @throws IOException 如果文件已关闭或写入过程中发生IO错误
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    public void write(byte[] data, int offset, int length) throws IOException {
        // 检查文件输出目标状态
        if (closed) {
            throw new IOException("输出目标已关闭，无法写入数据");
        }

        // 参数有效性验证
        TransferUtils.validateWriteParameters(data, offset, length);

        try {
            // 执行实际的文件写入操作
            outputStream.write(data, offset, length);
            // 更新写入字节数统计
            bytesWritten += length;

            // 记录详细的写入日志（TRACE级别，用于调试）
            log.trace("文件写入数据块: {} bytes, 总计: {} bytes, 目标: {}",
                    length, bytesWritten, targetPath.getFileName());
        } catch (IOException e) {
            // 记录写入失败的错误日志
            log.error("文件写入失败: {}, 已写入: {} bytes", targetPath.getFileName(), bytesWritten, e);
            throw e;
        }
    }

    /**
     * 刷新文件输出缓冲区
     * <p>
     * 强制将缓冲区中的所有数据写入到磁盘文件，确保数据持久化。
     * 这对于确保数据完整性和及时写入非常重要，特别是在传输大文件时。
     * </p>
     *
     * <h3>使用场景：</h3>
     * <ul>
     *   <li><b>传输完成时</b>：确保所有数据都写入磁盘</li>
     *   <li><b>关键节点</b>：在重要的传输节点强制刷新</li>
     *   <li><b>错误恢复</b>：在异常情况下尽可能保存已传输的数据</li>
     * </ul>
     *
     * <h3>实现细节：</h3>
     * <ul>
     *   <li>如果文件已关闭，记录警告日志但不抛出异常</li>
     *   <li>调用底层OutputStream的flush()方法</li>
     *   <li>记录刷新操作的日志</li>
     * </ul>
     *
     * @throws IOException 如果刷新过程中发生IO错误
     */
    @Override
    public void flush() throws IOException {
        // 检查文件状态，如果已关闭则记录警告但不抛出异常
        if (closed) {
            log.warn("尝试刷新已关闭的文件输出目标: {}", targetPath.getFileName());
            return;
        }

        try {
            // 强制刷新底层输出流的缓冲区
            outputStream.flush();
            // 记录刷新成功的日志
            log.trace("文件输出缓冲区已刷新: {}", targetPath.getFileName());
        } catch (IOException e) {
            // 记录刷新失败的错误日志
            log.error("文件输出缓冲区刷新失败: {}", targetPath.getFileName(), e);
            throw e;
        }
    }

    /**
     * 关闭文件输出目标并释放资源
     * <p>
     * 这是文件上传的最后一步，负责正确关闭文件流并释放系统资源。
     * 方法会确保所有缓冲的数据都被写入磁盘，然后安全地关闭文件句柄。
     * </p>
     *
     * <h3>执行顺序：</h3>
     * <ol>
     *   <li>检查是否已经关闭，避免重复操作</li>
     *   <li>刷新缓冲区，确保数据完整写入</li>
     *   <li>关闭底层输出流</li>
     *   <li>标记状态为已关闭</li>
     *   <li>记录关闭操作和统计信息</li>
     * </ol>
     *
     * <h3>异常处理策略：</h3>
     * <ul>
     *   <li><b>防御性编程</b>：即使关闭失败也标记为已关闭，防止资源泄露</li>
     *   <li><b>详细日志</b>：记录关闭失败的详细信息，包括已写入字节数</li>
     *   <li><b>异常传播</b>：将IOException向上传播，让调用者知道关闭失败</li>
     * </ul>
     *
     * <h3>资源管理：</h3>
     * <p>
     * 此方法是资源管理的关键部分，确保文件句柄被正确释放，
     * 避免文件句柄泄露导致的系统资源耗尽。
     * </p>
     *
     * @throws IOException 如果关闭过程中发生IO错误
     */
    @Override
    public void close() throws IOException {
        // 防止重复关闭操作
        if (closed) {
            log.debug("文件输出目标已经关闭: {}", targetPath.getFileName());
            return;
        }

        try {
            // 第一步：先刷新缓冲区，确保所有数据都写入磁盘
            outputStream.flush();
            // 第二步：关闭底层输出流，释放文件句柄
            outputStream.close();
            // 第三步：标记为已关闭状态
            closed = true;

            // 记录成功关闭的日志，包含统计信息
            log.debug("文件输出目标已关闭: {}, 总写入: {} bytes",
                    targetPath.getFileName(), bytesWritten);
        } catch (IOException e) {
            // 防御性编程：即使关闭失败也标记为已关闭，防止资源泄露
            closed = true;
            // 记录详细的错误信息，包含已写入的字节数
            log.error("关闭文件输出目标失败: {}, 总写入: {} bytes",
                    targetPath.getFileName(), bytesWritten, e);
            throw e;
        }
    }

    /**
     * 获取输出目标类型标识
     * <p>
     * 返回此输出目标的类型标识，用于日志记录、调试和类型判断。
     * 这是OutputTarget接口的标准方法，帮助区分不同类型的输出目标。
     * </p>
     *
     * @return 固定返回"file"，表示这是文件类型的输出目标
     */
    @Override
    public String getTargetType() {
        return "file";
    }

    /**
     * 获取输出目标的详细描述信息
     * <p>
     * 返回包含完整文件路径的描述信息，用于日志记录和调试。
     * 描述信息包含绝对路径，便于定位具体的文件位置。
     * </p>
     *
     * @return 格式为"文件: /absolute/path/to/file"的描述字符串
     */
    @Override
    public String getTargetDescription() {
        return "文件: " + targetPath.toAbsolutePath().toString();
    }

    /**
     * 检查文件输出目标是否可用
     * <p>
     * 判断当前文件输出目标是否处于可写入状态。
     * 只有在文件未关闭且输出流有效的情况下才认为是可用的。
     * </p>
     *
     * <h3>可用性条件：</h3>
     * <ul>
     *   <li>文件输出目标未被关闭（!closed）</li>
     *   <li>底层输出流不为null（outputStream != null）</li>
     * </ul>
     *
     * @return 如果可用返回true，否则返回false
     */
    @Override
    public boolean isAvailable() {
        return !closed && !ObjectUtils.isEmpty(outputStream);
    }

    /**
     * 获取已写入的字节数
     * <p>
     * 返回从开始写入到当前时刻已经成功写入到文件的总字节数。
     * 这个值用于进度监控、统计信息和调试。
     * </p>
     *
     * <h3>注意事项：</h3>
     * <ul>
     *   <li>只统计成功写入的字节数</li>
     *   <li>如果写入过程中发生异常，不会计入失败的字节数</li>
     *   <li>此值在文件关闭后仍然有效，可用于最终统计</li>
     * </ul>
     *
     * @return 已写入的字节数
     */
    @Override
    public long getBytesWritten() {
        return bytesWritten;
    }

    /**
     * 获取目标文件路径
     *
     * @return 目标文件路径
     */
    public Path getTargetPath() {
        return targetPath;
    }

    /**
     * 检查是否已关闭
     *
     * @return 如果已关闭返回true，否则返回false
     */
    public boolean isClosed() {
        return closed;
    }

    /**
     * 获取格式化的写入大小
     * <p>
     * 使用工具类格式化字节数，确保格式一致性。
     * </p>
     *
     * @return 格式化的大小字符串
     */
    public String getFormattedBytesWritten() {
        return TransferUtils.formatBytes(bytesWritten);
    }

    @Override
    public String toString() {
        return String.format("FileOutputTarget{path=%s, bytesWritten=%s, closed=%s}",
                targetPath.getFileName(), getFormattedBytesWritten(), closed);
    }
}
