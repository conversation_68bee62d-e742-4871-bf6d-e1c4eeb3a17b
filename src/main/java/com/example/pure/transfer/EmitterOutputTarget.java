package com.example.pure.transfer;

import com.example.pure.util.TransferUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;
import java.util.Arrays;

/**
 * HTTP响应输出目标实现类
 * <p>
 * 用于文件下载场景，将数据发送到HTTP响应流。
 * 通过ResponseBodyEmitter实现流式HTTP响应，支持大文件下载。
 * </p>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li><b>文件下载</b>：将服务器文件流式发送给客户端</li>
 *   <li><b>视频流播放</b>：支持Range请求的视频流传输</li>
 *   <li><b>大文件传输</b>：避免内存溢出的大文件传输</li>
 * </ul>
 *
 * <h3>性能特性：</h3>
 * <ul>
 *   <li><b>流式传输</b>：数据直接发送到HTTP响应，无需缓存</li>
 *   <li><b>背压控制</b>：利用ResponseBodyEmitter的背压机制</li>
 *   <li><b>异常处理</b>：完善的错误处理和连接状态监控</li>
 * </ul>
 */
@Slf4j
public class EmitterOutputTarget implements OutputTarget {

    private final ResponseBodyEmitter emitter;
    private final String connectionInfo;
    private long bytesSent;
    private boolean completed;
    private boolean errorOccurred;

    /**
     * 构造函数
     *
     * @param emitter HTTP响应发送器
     * @param connectionInfo 连接信息（用于日志和描述）
     */
    public EmitterOutputTarget(ResponseBodyEmitter emitter, String connectionInfo) {
        this.emitter = emitter;
        this.connectionInfo = TransferUtils.hasText(connectionInfo) ? connectionInfo : "未知连接";
        this.bytesSent = 0;
        this.completed = false;
        this.errorOccurred = false;
    }

    /**
     * 简化构造函数
     *
     * @param emitter HTTP响应发送器
     */
    public EmitterOutputTarget(ResponseBodyEmitter emitter) {
        this(emitter, null);
    }

    /**
     * 将数据发送到HTTP响应流
     * <p>
     * 这是文件下载的核心方法，将文件数据分块发送给HTTP客户端。
     * 使用ResponseBodyEmitter实现流式传输，支持大文件下载而不会导致内存溢出。
     * </p>
     *
     * <h3>执行流程：</h3>
     * <ol>
     *   <li>检查HTTP响应状态（是否已完成或发生错误）</li>
     *   <li>验证输入参数的有效性</li>
     *   <li>创建精确大小的数据块副本</li>
     *   <li>通过ResponseBodyEmitter发送数据</li>
     *   <li>更新已发送字节数统计</li>
     *   <li>记录发送操作日志</li>
     * </ol>
     *
     * <h3>数据处理策略：</h3>
     * <ul>
     *   <li><b>精确复制</b>：使用Arrays.copyOfRange创建精确大小的数据块</li>
     *   <li><b>避免多余数据</b>：确保只发送指定范围的数据，保证数据完整性</li>
     *   <li><b>流式传输</b>：数据直接发送到客户端，不在服务器端缓存</li>
     * </ul>
     *
     * <h3>异常处理：</h3>
     * <ul>
     *   <li>如果响应已完成，抛出IOException</li>
     *   <li>如果之前发生错误，抛出IOException</li>
     *   <li>如果参数无效，抛出IllegalArgumentException</li>
     *   <li>如果发送失败，标记错误状态并重新抛出异常</li>
     * </ul>
     *
     * @param data 要发送的数据数组
     * @param offset 数据数组中的起始偏移量
     * @param length 要发送的数据长度
     * @throws IOException 如果响应已完成、发生错误或发送过程中出现IO错误
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    public void write(byte[] data, int offset, int length) throws IOException {
        // 检查HTTP响应状态
        if (completed) {
            throw new IOException("HTTP响应已完成，无法发送更多数据");
        }

        if (errorOccurred) {
            throw new IOException("HTTP响应发生错误，无法发送数据");
        }

        // 参数有效性验证
        TransferUtils.validateWriteParameters(data, offset, length);

        try {
            // 创建精确大小的数据块发送
            // 决策理由：避免发送多余的数据，确保数据完整性
            byte[] chunkToSend = Arrays.copyOfRange(data, offset, offset + length);
            // 通过ResponseBodyEmitter发送数据到HTTP客户端
            emitter.send(chunkToSend);
            // 更新已发送字节数统计
            bytesSent += length;

            // 记录详细的发送日志（TRACE级别，用于调试）
            log.trace("HTTP响应发送数据块: {} bytes, 总计: {} bytes, 连接: {}",
                    length, bytesSent, connectionInfo);
        } catch (IOException e) {
            // 标记错误状态，防止后续操作
            errorOccurred = true;
            // 记录IO错误日志
            log.error("HTTP响应发送失败: {}, 已发送: {} bytes", connectionInfo, bytesSent, e);
            throw e;
        } catch (Exception e) {
            // 处理意外的非IO异常
            errorOccurred = true;
            log.error("HTTP响应发送过程中发生意外错误: {}, 已发送: {} bytes",
                    connectionInfo, bytesSent, e);
            // 包装为IOException向上传播
            throw new IOException("发送数据时发生意外错误", e);
        }
    }

    /**
     * 刷新HTTP响应缓冲区
     * <p>
     * 对于HTTP响应输出目标，刷新操作由ResponseBodyEmitter自动处理。
     * 此方法主要用于保持OutputTarget接口的一致性，并提供状态检查和日志记录。
     * </p>
     *
     * <h3>ResponseBodyEmitter的自动刷新机制：</h3>
     * <ul>
     *   <li><b>即时发送</b>：调用emitter.send()时数据立即发送给客户端</li>
     *   <li><b>无需缓冲</b>：不像文件输出那样需要显式刷新缓冲区</li>
     *   <li><b>背压控制</b>：Spring框架自动处理网络背压和流量控制</li>
     * </ul>
     *
     * <h3>状态检查：</h3>
     * <ul>
     *   <li>如果响应已完成或发生错误，记录调试信息但不执行操作</li>
     *   <li>如果响应正常，记录刷新日志用于调试</li>
     * </ul>
     *
     * <h3>设计考虑：</h3>
     * <p>
     * 虽然HTTP响应不需要显式刷新，但保留此方法确保了OutputTarget接口的
     * 统一性，使得文件输出和HTTP输出可以使用相同的传输逻辑。
     * </p>
     *
     * @throws IOException 此实现中不会抛出IOException，但保留以符合接口契约
     */
    @Override
    public void flush() throws IOException {
        // 检查响应状态，如果已完成或出错则无需操作
        if (completed || errorOccurred) {
            log.debug("HTTP响应已完成或发生错误，无需刷新: {}", connectionInfo);
            return;
        }

        // ResponseBodyEmitter 会自动处理数据发送，无需显式刷新
        // 但我们可以在这里记录日志用于调试和监控
        log.trace("HTTP响应缓冲区刷新（自动处理）: {}", connectionInfo);
    }

    /**
     * 关闭HTTP响应并完成传输
     * <p>
     * 这是文件下载的最后一步，负责正确完成HTTP响应并释放相关资源。
     * 根据当前状态（正常或错误），采用不同的完成策略。
     * </p>
     *
     * <h3>执行策略：</h3>
     * <ol>
     *   <li>检查是否已经完成，避免重复操作</li>
     *   <li>根据错误状态选择完成方式：</li>
     *   <ul>
     *     <li><b>正常情况</b>：调用emitter.complete()正常完成响应</li>
     *     <li><b>错误情况</b>：直接标记为完成，避免进一步操作</li>
     *   </ul>
     *   <li>更新完成状态标记</li>
     *   <li>记录完成操作和统计信息</li>
     * </ol>
     *
     * <h3>HTTP响应完成机制：</h3>
     * <ul>
     *   <li><b>emitter.complete()</b>：通知客户端响应正常结束</li>
     *   <li><b>连接关闭</b>：释放HTTP连接资源</li>
     *   <li><b>客户端通知</b>：客户端接收到响应结束信号</li>
     * </ul>
     *
     * <h3>异常处理策略：</h3>
     * <ul>
     *   <li><b>防御性编程</b>：即使完成失败也标记为已完成，防止资源泄露</li>
     *   <li><b>详细日志</b>：记录完成失败的详细信息，包括已发送字节数</li>
     *   <li><b>异常包装</b>：将各种异常包装为IOException向上传播</li>
     * </ul>
     *
     * <h3>资源管理：</h3>
     * <p>
     * 此方法确保HTTP连接被正确关闭，避免连接泄露导致的服务器资源耗尽。
     * 对于长时间运行的下载服务，正确的资源管理至关重要。
     * </p>
     *
     * @throws IOException 如果完成HTTP响应过程中发生错误
     */
    @Override
    public void close() throws IOException {
        // 防止重复完成操作
        if (completed) {
            log.debug("HTTP响应已经完成: {}", connectionInfo);
            return;
        }

        try {
            if (!errorOccurred) {
                // 正常情况：调用complete()正常完成HTTP响应
                emitter.complete();
                completed = true;
                // 记录正常完成的日志，包含统计信息
                log.debug("HTTP响应正常完成: {}, 总发送: {} bytes",
                        connectionInfo, bytesSent);
            } else {
                // 错误情况：直接标记为完成，避免调用complete()可能引发的异常
                completed = true;
                // 记录错误状态下的关闭警告
                log.warn("HTTP响应在错误状态下关闭: {}, 总发送: {} bytes",
                        connectionInfo, bytesSent);
            }
        } catch (Exception e) {
            // 防御性编程：即使完成失败也标记为已完成，防止资源泄露
            completed = true;
            // 记录详细的错误信息，包含已发送的字节数
            log.error("关闭HTTP响应失败: {}, 总发送: {} bytes",
                    connectionInfo, bytesSent, e);
            // 包装异常并向上传播
            throw new IOException("关闭HTTP响应失败", e);
        }
    }

    /**
     * 获取输出目标类型标识
     * <p>
     * 返回此输出目标的类型标识，用于日志记录、调试和类型判断。
     * 这是OutputTarget接口的标准方法，帮助区分不同类型的输出目标。
     * </p>
     *
     * @return 固定返回"http"，表示这是HTTP响应类型的输出目标
     */
    @Override
    public String getTargetType() {
        return "http";
    }

    /**
     * 获取输出目标的详细描述信息
     * <p>
     * 返回包含连接信息的描述，用于日志记录和调试。
     * 描述信息通常包含客户端信息、请求路径等，便于问题追踪。
     * </p>
     *
     * @return 格式为"HTTP响应: {连接信息}"的描述字符串
     */
    @Override
    public String getTargetDescription() {
        return "HTTP响应: " + connectionInfo;
    }

    /**
     * 检查HTTP响应输出目标是否可用
     * <p>
     * 判断当前HTTP响应是否处于可发送数据的状态。
     * 需要同时满足多个条件才认为是可用的。
     * </p>
     *
     * <h3>可用性条件：</h3>
     * <ul>
     *   <li><b>未完成</b>：HTTP响应尚未完成（!completed）</li>
     *   <li><b>无错误</b>：没有发生传输错误（!errorOccurred）</li>
     *   <li><b>发送器有效</b>：ResponseBodyEmitter不为null（emitter != null）</li>
     * </ul>
     *
     * <h3>状态说明：</h3>
     * <ul>
     *   <li>如果任一条件不满足，都认为不可用</li>
     *   <li>不可用状态下尝试发送数据会抛出异常</li>
     *   <li>此方法常用于传输前的状态检查</li>
     * </ul>
     *
     * @return 如果可用返回true，否则返回false
     */
    @Override
    public boolean isAvailable() {
        return !completed && !errorOccurred && !ObjectUtils.isEmpty(emitter);
    }

    /**
     * 获取已发送的字节数
     * <p>
     * 返回从开始发送到当前时刻已经成功发送到HTTP客户端的总字节数。
     * 这个值用于进度监控、统计信息和调试。
     * </p>
     *
     * <h3>注意事项：</h3>
     * <ul>
     *   <li><b>成功发送</b>：只统计成功发送的字节数</li>
     *   <li><b>网络传输</b>：表示已发送到网络，但不保证客户端已接收</li>
     *   <li><b>异常处理</b>：如果发送过程中发生异常，不会计入失败的字节数</li>
     *   <li><b>持久统计</b>：此值在响应完成后仍然有效，可用于最终统计</li>
     * </ul>
     *
     * @return 已发送的字节数
     */
    @Override
    public long getBytesWritten() {
        return bytesSent;
    }

    /**
     * 获取连接信息
     *
     * @return 连接信息
     */
    public String getConnectionInfo() {
        return connectionInfo;
    }

    /**
     * 检查是否已完成
     *
     * @return 如果已完成返回true，否则返回false
     */
    public boolean isCompleted() {
        return completed;
    }

    /**
     * 检查是否发生错误
     *
     * @return 如果发生错误返回true，否则返回false
     */
    public boolean hasError() {
        return errorOccurred;
    }

    /**
     * 标记发生错误
     * <p>
     * 当外部检测到错误时，可以调用此方法标记错误状态
     * </p>
     *
     * @param error 错误信息
     */
    public void markError(Throwable error) {
        if (!completed) {
            errorOccurred = true;
            try {
                emitter.completeWithError(error);
                completed = true;
                log.error("HTTP响应标记为错误状态: {}, 已发送: {} bytes",
                        connectionInfo, bytesSent, error);
            } catch (Exception e) {
                log.error("标记HTTP响应错误状态失败: {}", connectionInfo, e);
            }
        }
    }

    /**
     * 获取格式化的发送大小
     * <p>
     * 使用公共工具类格式化字节数，确保格式一致性。
     * </p>
     *
     * @return 格式化的大小字符串
     */
    public String getFormattedBytesSent() {
        return TransferUtils.formatBytes(bytesSent);
    }

    @Override
    public String toString() {
        return String.format("EmitterOutputTarget{connection=%s, bytesSent=%s, completed=%s, error=%s}",
                connectionInfo, getFormattedBytesSent(), completed, errorOccurred);
    }
}
