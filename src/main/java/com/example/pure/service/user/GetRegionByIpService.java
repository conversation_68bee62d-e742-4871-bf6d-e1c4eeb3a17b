package com.example.pure.service.user;

import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.InputStream;

@Service
public class GetRegionByIpService {



    private Searcher searcher;
    private final String dbPath = "/ip2region/IpDataBase.xdb";  // 假设文件在 resources/ip2region/

    // spring容器完成所有依赖注入后自动调用这个注解的方法
    @PostConstruct
    public void init() throws IOException {
        try {
            InputStream inputStream = getClass().getResourceAsStream(dbPath);
            if (inputStream == null) {
                throw new IOException("ip2region.xdb not found in classpath at " + dbPath);
            }
            byte[] cBuff = inputStream.readAllBytes();
            searcher = Searcher.newWithBuffer(cBuff);
        }catch (IOException e) {
            throw new RuntimeException("Failed to load ip2region database", e);
        }
    }

    public String getRegion(String ip) {
        try {
            // 国家|区域|省份|城市|ISP
            return searcher.search(ip);
        } catch (Exception e) {
            // log.error("IP地址解析失败: {}", ip, e);
            return "未知";
        }
    }
    // spring容器准备销毁前自动调用这个方法释放资源
    @PreDestroy
    public void destroy() throws IOException {
        if (searcher != null) {
            searcher.close();
        }
    }

}
