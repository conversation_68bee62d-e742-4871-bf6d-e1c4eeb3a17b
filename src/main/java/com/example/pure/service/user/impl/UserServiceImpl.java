package com.example.pure.service.user.impl;

import com.example.pure.common.PageFinalResult;
import com.example.pure.constant.ResponseCode;
import com.example.pure.event.UserRegisteredEvent;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.mapper.primary.UserProfileMapper;
import com.example.pure.mapper.primary.UserRoleMapper;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.dto.request.user.PasswordUpdateDTO;
import com.example.pure.model.dto.request.user.RegisterRequest;
import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.model.dto.response.user.UserWithUserProfileDTO;
import com.example.pure.model.entity.Role;
import com.example.pure.model.entity.User;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.service.auth.DeviceService;
import com.example.pure.service.userprofile.UserProfileService;
import com.example.pure.service.user.UserService;
import com.example.pure.service.auth.VerificationService;
import com.example.pure.util.CryptoUtil;
import com.example.pure.util.JwtUtil;
import com.example.pure.util.HashingUtil;
import com.example.pure.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.validation.Valid;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;
    private final UserRoleMapper userRoleMapper;
    private final VerificationService verificationService;
    private final UserProfileMapper userProfileMapper;
    private final UserProfileService userProfileService;
    private final RandomUtil randomUtil;
    private final ApplicationEventPublisher eventPublisher;
    private final HashingUtil hashUtil;
    private final DeviceService deviceService;

    @Autowired
    public UserServiceImpl(UserMapper userMapper, JwtUtil jwtUtil,
                           UserRoleMapper userRoleMapper, VerificationService verificationService,
                           RandomUtil randomUtil, UserProfileMapper userProfileMapper,
                           UserProfileService userProfileService,
                           ApplicationEventPublisher eventPublisher,
                           HashingUtil hashUtil,
                           CryptoUtil cryptoUtil,
                           DeviceService deviceService
    ) {
        this.userMapper = userMapper;
        this.jwtUtil = jwtUtil;
        this.userRoleMapper = userRoleMapper;
        this.verificationService = verificationService;
        this.randomUtil = randomUtil;
        this.userProfileMapper = userProfileMapper;
        this.userProfileService = userProfileService;
        this.eventPublisher = eventPublisher;
        this.hashUtil = hashUtil;
        this.deviceService = deviceService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDTO createUser(RegisterRequest createRequest) {
        log.debug("处理用户注册请求: {}", createRequest.getUsername());

        // 验证邮箱验证码
        boolean isCodeValid = verificationService.verifyEmailCode(
                createRequest.getEmail(),
                createRequest.getVerifyCode()
        );
        if (!isCodeValid) {
            throw new BusinessException("验证码不正确或已过期");
        }

        // 检查用户名是否已存在
        if (existsByUsername(createRequest.getUsername())) {
            throw new BusinessException(ResponseCode.USERNAME_ALREADY_EXISTS, "用户名已存在");
        }

        // 检查邮箱是否已存在
        Long existingUserIdForEmail = userProfileService.findUserIdByEmail(createRequest.getEmail());
        if (existingUserIdForEmail != null && existingUserIdForEmail > 0) {
            throw new BusinessException(ResponseCode.EMAIL_ALREADY_EXISTS, "邮箱已被注册");
        }

        // 创建用户实体
        User user = new User();
        user.setUsername(createRequest.getUsername());
        user.setPassword(hashUtil.passwordEncoder(createRequest.getPassword()));

        user.setEnabled(true);
        user.setAccountNonExpired(true);
        user.setAccountNonLocked(true);
        user.setCredentialsNonExpired(true);
        user.setCreatedTime(Instant.now());
        user.setUpdatedTime(Instant.now());

        // 向MySQL创建用户
        userMapper.insert(user);

        // 获取新创建的用户信息（包含生成的ID）
        Long newUserId = user.getId();
        if (newUserId == null) {
            UserDTO tempUser = userMapper.findByUsername(user.getUsername());
            if (tempUser == null || tempUser.getId() == null) {
                throw new BusinessException("用户创建失败：未能获取用户ID");
            }
            newUserId = tempUser.getId();
        }

        try {
            // 分配默认角色 ROLE_USER (假设ID = 2L)
            userRoleMapper.assignRoleToUser(newUserId, 2L);
            log.info("已为用户 {} (ID: {}) 分配默认角色 ROLE_USER", user.getUsername(), newUserId);
        } catch (Exception e) {
            log.error("为用户 {} (ID: {}) 分配角色失败: {}", user.getUsername(), newUserId, e.getMessage(), e);
            throw new BusinessException("分配用户角色失败");
        }

        UserProfile userProfile = new UserProfile();
        userProfile.setId(newUserId);
        userProfile.setUsername(user.getUsername());
        userProfile.setPhone("");
        userProfile.setEmail(createRequest.getEmail());
        userProfile.setNickname("用户" + randomUtil.generateVerificationCode(8));
        userProfile.setAvatar("https://avatars.githubusercontent.com/u/159130137");
        userProfile.setDescription("");
        int rowsAffected = userProfileService.createNewUserProfile(userProfile);
        if (rowsAffected == 0) {
            throw new BusinessException("创建用户详细信息失败");
        }

        // 发布用户注册成功事件
        try {
            UserRegisteredEvent registrationEvent = new UserRegisteredEvent(this, newUserId, user.getUsername());
            eventPublisher.publishEvent(registrationEvent);
            log.info("已发布用户注册成功事件：用户ID {}，用户名 {}", newUserId, user.getUsername());
        } catch (Exception e) {
            log.error("发布用户注册成功事件失败：用户ID {}，用户名 {}，错误: {}", newUserId, user.getUsername(), e.getMessage(), e);
        }

        // 返回UserDTO
        UserDTO createdUserDTO = userMapper.findById(newUserId);
        if (createdUserDTO == null) {
            throw new BusinessException("用户创建成功但获取DTO失败");
        }
        return createdUserDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public UserDTO findById(Long id) {
        log.debug("根据ID查找用户: {}", id);
        UserDTO user = userMapper.findById(id);
        if (user == null) {
            log.error("ID:{}，用户不存在",id);
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @Transactional(readOnly = true)
    @Override
    public UserDTO findUserByUsername(String username) {
        log.debug("根据用户名查找用户: {}", username);
        UserDTO user = userMapper.findByUsername(username);
        if (user == null) {
            log.error("username:{}，用户不存在",username);
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    // 获取用户基本数据更改密码用
    @Override
    public User findByUsername(String username) {
        return userMapper.findByUserWithPasswordByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public User findUserWithPasswordByUserId(Long id){
        log.debug("根据ID查找用户信息包含密码信息: {}",id);
        return userMapper.findUserWithPasswordByUserId(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserDTO> findAll() {
        log.debug("查找所有用户");
        return userMapper.findAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void banUser(Long userId, long days, String reason) {
        log.info("开始封禁用户，userId: {}, 封禁天数: {}, 原因: {}", userId, days, reason);

        User user = findUserWithPasswordByUserId(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 设置封禁截止时间
        Instant banExpiresAt = Instant.now().plus(Duration.ofDays(days));
        user.setBanExpiresAt(banExpiresAt);
        updateUser(user);

        // 清除用户所有设备的Token信息，防止用户在封禁期间使用之前的token访问
        try {
            deviceService.removeAllUserDevices(user.getUsername());
            log.info("已清除用户 {} 的所有设备Token信息", user.getUsername());
        } catch (Exception e) {
            log.error("清除用户 {} 设备Token信息失败: {}", user.getUsername(), e.getMessage(), e);
            // 不抛出异常，因为封禁操作已经成功，设备清理失败不应该影响封禁
        }

        log.info("用户封禁成功，userId: {}, username: {}, 封禁至: {}, 原因: {}",
                 userId, user.getUsername(), banExpiresAt, reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbanUser(Long userId) {
        log.info("开始解封用户，userId: {}", userId);

        User user = findUserWithPasswordByUserId(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        user.setBanExpiresAt(null);
        updateUser(user);

        log.info("用户解封成功，userId: {}, username: {}", userId, user.getUsername());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUserBanned(Long userId) {
        User user = findUserWithPasswordByUserId(userId);
        return user != null &&
               user.getBanExpiresAt() != null &&
               user.getBanExpiresAt().isAfter(Instant.now());
    }

    @Override
    @Transactional(readOnly = true)
    public Duration getRemainingBanTime(Long userId) {
        User user = findUserWithPasswordByUserId(userId);
        if (user == null || user.getBanExpiresAt() == null) {
            return Duration.ZERO;
        }

        Instant now = Instant.now();
        if (user.getBanExpiresAt().isAfter(now)) {
            return Duration.between(now, user.getBanExpiresAt());
        }

        return Duration.ZERO;
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        log.debug("删除用户: {}", id);

        // 检查用户是否存在
        findById(id);

        // 删除用户记录
        userMapper.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return userMapper.countByUsername(username) > 0;
    }

    /**
     * 验证用户密码
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 密码是否匹配
     */
    private boolean validatePassword(String rawPassword, String encodedPassword) {
        return hashUtil.passwordMatch(rawPassword, encodedPassword);
    }

    @Override
    @Transactional
    public void updatePassword(PasswordUpdateDTO passwordDTO, User curentUser) {
        log.debug("更新用户密码, userId: {}", curentUser.getId());
        String newPassword=hashUtil.passwordEncoder(passwordDTO.getNewPassword());
        log.debug("id:{},password:{}",curentUser.getId(),newPassword);
        curentUser.setPassword(newPassword);
        int isUpdated=userMapper.update(curentUser);
        if (isUpdated == 0) {
            throw new BusinessException("更新用户密码失败");
        }
        log.debug("用户密码更新成功, userId: {}", curentUser.getId());
    }

    @Override
    @Transactional
    public void updateUser(User user){
        log.debug("更新用户信息, userId: {}", user.getId());
        int isUpdated=userMapper.update(user);
        if (isUpdated == 0) {
            throw new BusinessException("更新用户信息失败");
        }
        log.debug("用户信息更新成功, userId: {}", user.getId());
    }

    /**
     * 检查用户令牌是否即将过期
     * 如果minutesThreshold为0，则等同于检查token是否已过期
     *
     * @param token JWT令牌
     * @param minutesThreshold 过期阈值（分钟）
     * @return 如果令牌即将在指定分钟数内过期返回true，否则返回false
     */
    @Override
    public boolean isTokenAboutToExpire(String token, int minutesThreshold) {
        try {
            return jwtUtil.isTokenAboutToExpire(token, minutesThreshold);
        } catch (Exception e) {
            log.warn("Token validation failed: {}", e.getMessage());
            return true; // 任何验证失败都视为token即将过期
        }
    }

    /**
     * 根据用户名获取用户全部资料
     *
     * @param username 用户名
     * @return 用户DTO对象，不包含敏感信息
     */
    @Override
    @Transactional(readOnly = true)
    public UserWithUserProfileDTO getUserWithUserProfileDTOByUsername(String username) {
        log.debug("获取用户DTO, username: {}", username);
        UserWithUserProfileDTO userWithUserProfile = userMapper.UserWithUserProfileDTOByUsername(username);
        if(ObjectUtils.isEmpty(userWithUserProfile)){
            throw new BusinessException("查询到的用户资料为空");
        }

        // 获取角色并设置角色
        List<Role> roles =userRoleMapper.findRolesByUserId(userWithUserProfile.getId());
        List<String> rolesName=new ArrayList<>();
        for (Role role : roles) {
            rolesName.add(role.getName());
            userWithUserProfile.setRoles(rolesName);
        }

        // 设置权限
        List<String> permissions=new ArrayList<>();
        for (Role role : roles) {
            if ("admin".equals(role.getName())){
                permissions.add("*:*:*");
            }else {
                permissions.add("permission:btn:add");
                permissions.add("permission:btn:edit");
            }
            userWithUserProfile.setPermissions(permissions);
        }

        return userWithUserProfile;
    }

    /**
     * 校验JWT令牌
     *
     * @param token JWT令牌
     * @return 令牌是否有效
     */
    @Override
    public boolean validateToken(String token) {
        try {
            return jwtUtil.validateToken(token);
        } catch (Exception e) {
            log.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查令牌是否过期
     *
     * @param token JWT令牌
     * @return 令牌是否过期
     */
    @Override
    public boolean isTokenExpired(String token) {
        try {
            return jwtUtil.isTokenExpired(token);
        } catch (Exception e) {
            log.warn("Token expiration check failed: {}", e.getMessage());
            return true; // 如果验证失败，视为已过期
        }
    }

    /**
     * 手动分页查询用户列表
     * <p>
     * 使用offset和limit实现手动分页，不依赖PageHelper插件
     * </p>
     *
     * @param pageRequest 分页请求参数
     * @return 分页结果，包含用户DTO列表
     */
    @Override
    @Transactional(readOnly = true)
    public PageFinalResult<UserDTO> findByPage(@Valid PageRequestDTO pageRequest) {
        log.debug("手动分页查询用户列表: 页码={}, 每页大小={}, 关键字={}",
                pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getKeyword());

        // 1. 计算分页参数
        // 计算跳过多少条数据，数据库按跳过多少条数据来获取结果
        int offset = pageRequest.getOffset();

        // 限制最多查询并获得多少行数据，数据库按限制来获取结果
        int limit = pageRequest.getPageSize();
        String keyword = pageRequest.getKeyword();

        // 2. 安全处理排序参数
        String orderBy = processOrderBy(pageRequest.getOrderBy());
        String orderDirection = processOrderDirection(pageRequest.getOrderDirection());

        // 3. 查询总记录数
        int total = userMapper.countUsers(keyword);

        // 4. 如果没有记录，返回空结果
        if (total == 0) {
            PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.builder()
                    .total(0)
                    .pageNum(pageRequest.getPageNum())
                    .pageSize(pageRequest.getPageSize())
                    .pages((int) Math.ceil((double) total / pageRequest.getPageSize()))
                    .build();
            return new PageFinalResult<>(null, pageResult);
        }

        try {
            // 5. 查询分页数据
            List<User> users = userMapper.findByPage(offset, limit, keyword, orderBy, orderDirection);

            // 6. 转换为DTO对象
            List<UserDTO> userDTOs = users.stream()
                    .map(UserDTO::fromUser)
                    .collect(java.util.stream.Collectors.toList());

            // 7. 构建分页结果
            PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.builder()
                    .total(total)
                    .pageNum(pageRequest.getPageNum())
                    .pageSize(pageRequest.getPageSize())
                    .pages((int) Math.ceil((double) total / pageRequest.getPageSize()))
                    .build();
            return new PageFinalResult<>(userDTOs, pageResult);
        } catch (Exception e) {
            log.error("分页查询用户失败: {}", e.getMessage(), e);

            // 8. 如果排序查询失败，尝试不带排序参数重新查询
            List<User> users = userMapper.findByPage(offset, limit, keyword, null, null);
            List<UserDTO> userDTOs = users.stream()
                    .map(UserDTO::fromUser)
                    .collect(java.util.stream.Collectors.toList());

            PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.builder()
                    .total(total)
                    .pageNum(pageRequest.getPageNum())
                    .pageSize(pageRequest.getPageSize())
                    .pages((int) Math.ceil((double) total / pageRequest.getPageSize()))
                    .build();
            return new PageFinalResult<>(userDTOs, pageResult);
        }
    }

    /**
     * 处理排序字段参数，防止SQL注入
     *
     * @param orderBy 原始排序字段
     * @return 安全的排序字段
     */
    private String processOrderBy(String orderBy) {
        if (orderBy == null || orderBy.isEmpty()) {
            return null;
        }

        // 1. 移除"order by"前缀，如果存在
        if (orderBy.toLowerCase().contains("order by")) {
            orderBy = orderBy.toLowerCase().replace("order by", "").trim();
        }

        // 2. 白名单验证排序字段
        List<String> validColumns = java.util.Arrays.asList(
            "id", "username", "email", "nickname", "gender",
            "createdTime", "updatedTime", "lastLoginTime", "score"
        );

        // 3. 只接受白名单中的字段
        if (!validColumns.contains(orderBy)) {
            log.warn("尝试使用无效的排序字段: {}", orderBy);
            return null; // 返回null，使用默认排序
        }

        return orderBy;
    }

    /**
     * 处理排序方向参数
     *
     * @param orderDirection 原始排序方向
     * @return 安全的排序方向
     */
    private String processOrderDirection(String orderDirection) {
        if (orderDirection == null || orderDirection.isEmpty()) {
            return "desc"; // 默认使用降序
        }

        // 只接受"asc"或"desc"
        if ("asc".equalsIgnoreCase(orderDirection)) {
            return "asc";
        } else {
            return "desc";
        }
    }
}
