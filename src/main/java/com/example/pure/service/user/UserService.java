package com.example.pure.service.user;

import com.example.pure.common.PageFinalResult;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.dto.request.user.PasswordUpdateDTO;
import com.example.pure.model.dto.request.user.RegisterRequest;
import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.model.dto.response.user.UserWithUserProfileDTO;
import com.example.pure.model.entity.User;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.Duration;
import java.util.List;

/**
 * 用户服务接口
 */
@Validated
public interface UserService {

    /**
     * 用户注册
     *
     * @param createRequest 注册请求
     * @return 注册成功的用户信息
     */
    UserDTO createUser(RegisterRequest createRequest);


    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    UserDTO findById(Long id);

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);

    // 查询用户信息，只能修改密码使用
    User findUserWithPasswordByUserId(Long id);


    @Transactional(readOnly = true)
    UserDTO findUserByUsername(String username);

    /**
     * 查找所有用户
     *
     * @return 用户列表
     */
    List<UserDTO> findAll();


    /**
     * 封禁用户指定天数
     * @param userId 用户ID
     * @param days 封禁天数
     * @param reason 封禁原因
     */
    void banUser(Long userId, long days, String reason);

    /**
     * 解除用户封禁
     * @param userId 用户ID
     */
    void unbanUser(Long userId);

    /**
     * 检查用户是否被封禁
     * @param userId 用户ID
     * @return 是否被封禁
     */
    boolean isUserBanned(Long userId);

    /**
     * 获取用户剩余封禁时间
     * @param userId 用户ID
     * @return 剩余封禁时间
     */
    Duration getRemainingBanTime(Long userId);

    /**
     * 删除用户
     *
     * @param id 用户ID
     */
    void deleteById(Long id);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);


    /**
     * 更新用户密码
     */
    void updatePassword(PasswordUpdateDTO passwordDTO, User curentUser);


    void updateUser(User user);
    /**
     * 根据用户名获取用户全部信息
     *
     * @param username 用户名
     * @return 用户DTO对象，不包含敏感信息
     */
    UserWithUserProfileDTO getUserWithUserProfileDTOByUsername(String username);

    /**
     * 校验JWT令牌
     *
     * @param token JWT令牌
     * @return 令牌是否有效
     */
    boolean validateToken(String token);

    /**
     * 检查令牌是否过期
     *
     * @param token JWT令牌
     * @return 令牌是否过期
     */
    boolean isTokenExpired(String token);

    /**
     * 检查令牌是否即将过期
     *
     * @param token JWT令牌
     * @param minutesThreshold 过期阈值（分钟）
     * @return 令牌是否即将在指定分钟数内过期
     */
    boolean isTokenAboutToExpire(String token, int minutesThreshold);

    /**
     * 手动分页查询用户列表
     * <p>
     * 查询指定页码、每页大小的用户数据，支持条件过滤
     * </p>
     *
     * @param pageRequest 分页请求参数
     * @return 分页结果，包含用户DTO列表
     */
    PageFinalResult<UserDTO> findByPage(@Valid PageRequestDTO pageRequest);
}
