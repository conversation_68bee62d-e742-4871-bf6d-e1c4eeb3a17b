package com.example.pure.service.userprofile;

import com.example.pure.model.entity.UserProfile;

/**
 * 用户详细信息服务接口
 */
public interface UserProfileService {

    /**
     * 根据用户ID查找用户详细信息
     * @param userId 用户ID
     * @return 用户详细信息 DTO，如果找不到返回 null
     */
    UserProfile findUserProfileByUserId(Long userId);

    /**
     * 根据用户名查找用户详细信息
     * @param username 用户名
     * @return 用户详细信息 DTO，如果找不到返回 null
     */
    UserProfile findUserProfileByUsername(String username);

    /**
     * 创建新的用户详细信息记录
     * (通常在用户注册时调用)
     * @param userProfile 包含用户ID和其他详情的用户详细信息对象
     */
    public int createNewUserProfile(UserProfile userProfile);

    /**
     * 根据用户ID更新用户详细信息
     * @param userProfile 包含要更新的用户ID和新信息的对象
     */
    void updateUserProfileByUserId(UserProfile userProfile);

    /**
     * 根据用户名更新用户详细信息
     * @param userProfile 包含要更新的用户名和新信息的对象
     */
    void updateUserProfileByUsername(UserProfile userProfile);

    /**
     * 根据用户ID删除用户详细信息
     * (通常在删除用户时调用)
     * @param userId 要删除详情的用户ID
     */
    void deleteUserProfileByUserId(Long userId);

    /**
     * 根据邮箱查找用户ID
     * @param email 邮箱地址
     * @return 用户ID，如果找不到返回 null
     */
    Long findUserIdByEmail(String email);
}
