package com.example.pure.service.auth.impl;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.service.auth.VerificationService;
import com.example.pure.util.RandomUtil;
import com.example.pure.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
public class VerificationServiceImpl implements VerificationService {

    private final RedisUtil redisUtil;
    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${verification.code.length}")
    private int codeLength;

    @Value("${verification.code.expiration}")
    private long codeExpiration;

    private RandomUtil randomUtil;


    @Autowired
    public VerificationServiceImpl(RedisUtil redisUtil,
                                   JavaMailSender mailSender,
                                   TemplateEngine templateEngine,
                                   RandomUtil randomUtil) {
        this.redisUtil = redisUtil;
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
        this.randomUtil = randomUtil;
    }

    @Override
    public void sendEmailVerificationCode(String email) {
        // 生成随机验证码
        String verificationCode = randomUtil.generateVerificationCode(codeLength);



        // 保存到Redis中
        String redisKey = getRedisKey(email);
        redisUtil.set(redisKey, verificationCode, codeExpiration);

        // 发送邮件
        sendVerificationEmail(email, verificationCode);

        log.info("验证码已发送到邮箱: {}, 验证码: {}", email, verificationCode);
    }

    @Override
    public boolean verifyEmailCode(String email, String code) {
        if (email == null || code == null) {
            return false;
        }

        String redisKey = getRedisKey(email);
        String storedCode = redisUtil.getString(redisKey);

        // 验证码匹配且不为空
        boolean isValid = code.equals(storedCode) && storedCode != null;

        // 验证通过后，删除Redis中的验证码，防止重复使用
        if (isValid) {
            redisUtil.delete(redisKey);
        }

        return isValid;
    }



    /**
     * 获取Redis中存储验证码的key
     */
    private String getRedisKey(String email) {
        return SecurityConstants.EMAIL_CODE_PREFIX + email;
    }

    /**
     * 发送验证码邮件
     */
    private void sendVerificationEmail(String toEmail, String verificationCode) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            // 使用多部份模式
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject("您的验证码");

            // 使用Thymeleaf模板引擎处理HTML邮件模板
            Context context = new Context();
            context.setVariable("verificationCode", verificationCode);
            // 使用目标模板位置
            String emailContent = templateEngine.process("email/VerifyCode", context);

            helper.setText(emailContent, true);

            mailSender.send(message);
        } catch (MessagingException e) {
            log.error("发送邮件失败", e);
            throw new RuntimeException("发送邮件失败: " + e.getMessage());
        }
    }
}
