package com.example.pure.service.auth.impl;


import com.example.pure.common.PageFinalResult;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.OperatingLogMapper;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.entity.OperatingLog;
import com.example.pure.service.auth.OperatingLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志服务实现类
 */
@Slf4j
@Service
public class OperatingLogServiceImpl implements OperatingLogService {

    private final OperatingLogMapper operatingLogMapper;

    @Autowired
    public OperatingLogServiceImpl(OperatingLogMapper operatingLogMapper) {
        this.operatingLogMapper = operatingLogMapper;
    }

    // 记录运行Api日志
    @Override
    @Transactional
    public OperatingLog recordOperatingLog(OperatingLog operatingLog) {
        // 设置操作时间为当前时间
        if (operatingLog.getOperatingTime() == null) {
            operatingLog.setOperatingTime(LocalDateTime.now());
        }

        // 设置默认地址
        if (operatingLog.getAddress() == null) {
            operatingLog.setAddress("中国广东省");
        }

        operatingLogMapper.insertOperatingLog(operatingLog);
        log.info("记录用户操作日志: userId={}, summary={}", operatingLog.getUserId(), operatingLog.getSummary());
        return operatingLog;
    }

    // 获取记录的Api日志
    @Override
    public  PageFinalResult<OperatingLog> getUserOperatingLogs(Long userId, PageRequestDTO pageRequest) {
        // 验证分页参数
        if (pageRequest == null || pageRequest.getPageNum() == null || pageRequest.getPageSize() == null) {
            throw new IllegalArgumentException("分页参数不能为空");
        }

        if (pageRequest.getPageNum() <= 0 || pageRequest.getPageSize() <= 0) {
            throw new IllegalArgumentException("分页参数必须大于0");
        }

        // 计算跳过多少行和限制请求每页多少行
        int offset = pageRequest.getOffset();
        int limit = pageRequest.getPageSize();

        // 获取总记录数
        int total = operatingLogMapper.countOperatingLogsByUserId(userId);
        if (total <= 0) {
            throw new BusinessException("没有查询到操作日志数据");
        }

        // 查询分页数据
        List<OperatingLog> operatingLogsList = operatingLogMapper.findOperatingLogsByUserId(userId, offset, limit);

        // 构建分页结果对象
        PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.of(pageRequest.getPageNum(), pageRequest.getPageSize(), total);
        PageFinalResult<OperatingLog> pageFinalResult=new PageFinalResult<>(operatingLogsList,pageResult);

        return pageFinalResult;
    }

    @Override
    public int countUserOperatingLogs(Long userId) {
        return operatingLogMapper.countOperatingLogsByUserId(userId);
    }
}
