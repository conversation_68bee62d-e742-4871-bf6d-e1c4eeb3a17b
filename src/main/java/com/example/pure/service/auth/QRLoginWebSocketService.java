package com.example.pure.service.auth;

import java.util.Date;

/**
 * 二维码登录 WebSocket 服务接口
 * <p>
 * 提供基于 WebSocket 的二维码登录状态推送服务
 * </p>
 */
public interface QRLoginWebSocketService {

    /**
     * 推送二维码状态变化
     * <p>
     * 当二维码状态发生变化时，向订阅该二维码的客户端推送状态更新
     * </p>
     *
     * @param qrId 二维码唯一标识
     */
    void notifyStatusChange(String qrId);

    /**
     * 推送二维码状态变化（使用已有的二维码信息）
     * <p>
     * 当二维码状态发生变化时，向订阅该二维码的客户端推送状态更新
     * 使用已获取的二维码信息，避免重复查询 Redis
     * </p>
     *
     * @param qrInfo 二维码信息
     */
    void notifyStatusChange(com.example.pure.util.QRLoginRedisUtil.QRCodeInfo qrInfo);

    /**
     * 推送登录成功事件
     * <p>
     * 当用户确认登录时，推送包含用户信息和令牌的登录成功事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     */
    void notifyLoginSuccess(String qrId, String accessToken, String refreshToken, Date tokenExpiration);

    /**
     * 推送登录成功事件（使用已有的二维码信息）
     * <p>
     * 当用户确认登录时，推送包含用户信息和令牌的登录成功事件
     * 使用已获取的二维码信息，避免重复查询 Redis
     * </p>
     *
     * @param qrInfo 二维码信息
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     */
    void notifyLoginSuccess(com.example.pure.util.QRLoginRedisUtil.QRCodeInfo qrInfo,
                          String accessToken, String refreshToken, Date tokenExpiration);

    /**
     * 推送登录取消事件
     * <p>
     * 当用户取消登录时，推送取消事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     */
    void notifyLoginCancel(String qrId);

    /**
     * 推送错误事件
     * <p>
     * 当发生错误时，推送错误事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @param error 错误信息
     */
    void notifyError(String qrId, String error);
}
