package com.example.pure.service.auth;

import com.example.pure.common.PageFinalResult;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.entity.OperatingLog;

/**
 * 操作日志服务接口
 */
public interface OperatingLogService {

    /**
     * 记录用户操作日志
     * @param operatingLog 操作日志对象
     * @return 保存后的操作日志对象（包含ID等生成的字段）
     */
    OperatingLog recordOperatingLog(OperatingLog operatingLog);

    /**
     * 获取用户操作日志（分页）
     *
     * @param userId 用户ID
     * @param pageRequest 分页请求参数
     * @return 包含操作日志列表和分页信息的Map:
     *         - "logsList": 操作日志列表
     *         - "pageResult": 分页元数据
     * @throws IllegalArgumentException 当分页参数无效时
     * @throws BusinessException 当没有数据时
     */
    PageFinalResult<OperatingLog> getUserOperatingLogs(Long userId, PageRequestDTO pageRequest);

    /**
     * 获取用户操作日志数量
     *
     * @param userId 用户ID
     * @return 操作日志数量
     */
    int countUserOperatingLogs(Long userId);
}
