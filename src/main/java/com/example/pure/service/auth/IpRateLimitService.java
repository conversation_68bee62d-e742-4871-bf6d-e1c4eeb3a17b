package com.example.pure.service.auth;

import com.example.pure.exception.RateLimitExceededException;
import com.example.pure.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.time.Duration;

/**
 * IP限流服务
 * 处理基于IP的访问频率限制
 */
@Slf4j
@Service
public class IpRateLimitService {

    private final RedisUtil redisUtil;

    // IP限流配置
    private static final int IP_HOURLY_LIMIT = 13;  // IP每小时访问限制
    private static final int IP_DAILY_LIMIT = 30;  // IP每天访问限制

    // Redis key格式
    private static final String IP_RATE_LIMIT_KEY = "rate:ip:%s:%s:%s";  // rate:ip:${ip}:${endpoint}:${period}

    // 时间维度标识
    private static final String HOUR_SUFFIX = "per_hour";
    private static final String DAILY_SUFFIX = "per_day";

    @Autowired
    public IpRateLimitService(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    /**
     * 检查IP访问频率限制
     */
    public void checkIpRateLimit(String ip, String endpoint) {
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        String hourlyKey = String.format(IP_RATE_LIMIT_KEY, ip, endpoint, HOUR_SUFFIX);
        String dailyKey = String.format(IP_RATE_LIMIT_KEY, ip, endpoint, DAILY_SUFFIX + ":" + today);

        // 检查小时限制
        checkAndIncrementLimit(hourlyKey, IP_HOURLY_LIMIT, 1, TimeUnit.HOURS, "每小时");
        // 检查每日限制
        checkAndIncrementLimit(dailyKey, IP_DAILY_LIMIT, getSecondsUntilMidnight(),
            TimeUnit.SECONDS, "每天");
    }

    /**
     * 检查并递增计数器
     */
    private void checkAndIncrementLimit(String key, int limit, long timeout, TimeUnit timeUnit, String periodName) {
        Long count = redisUtil.increment(key, timeout, timeUnit);

        if (count != null && count > limit) {
            log.warn("IP访问频率超出限制: key={}, count={}, limit={}", key, count, limit);
            // 超出限制后将计数器减1，因为这次访问被拒绝了
            throw new RateLimitExceededException(String.format("访问太频繁，%s只能访问%d次", periodName, limit));
        }
    }

    /**
     * 计算到下一个0点的秒数
     */
    private long getSecondsUntilMidnight() {
        LocalDateTime now = LocalDateTime.now();
        //根据当前时间获取明天0点的时间
        LocalDateTime nextMidnight = now.toLocalDate().plusDays(1).atStartOfDay();
        //获取当前时间和目标时间的秒数并返回
        return Duration.between(now, nextMidnight).getSeconds();
    }
}
