package com.example.pure.service.auth.impl;

import com.example.pure.constant.ResponseCode;
import com.example.pure.exception.BusinessException;
import com.example.pure.service.auth.QRLoginWebSocketService;
import com.example.pure.util.QRLoginRedisUtil;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;
import com.example.pure.util.QRLoginRedisUtil.QRCodeStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码登录 WebSocket 服务实现类
 * <p>
 * 基于 WebSocket 实现二维码登录状态的实时推送
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QRLoginWebSocketServiceImpl implements QRLoginWebSocketService {

    private final QRLoginRedisUtil qrLoginUtil;
    private final SimpMessagingTemplate messagingTemplate;

    @Override
    public void notifyStatusChange(String qrId) {
        log.debug("推送二维码状态变化 (WebSocket) - QR ID: {}", qrId);

        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (qrInfo == null) {
            notifyError(qrId, "二维码不存在或已失效");
            return;
        }

        // 调用重载方法，避免重复逻辑
        notifyStatusChange(qrInfo);
    }

    @Override
    public void notifyStatusChange(QRCodeInfo qrInfo) {
        String qrId = qrInfo.getQrId();
        log.debug("推送二维码状态变化 (WebSocket, 使用已有信息) - QR ID: {}", qrId);

        // 构建状态变更消息
        Map<String, Object> statusMessage = buildStatusMessage(qrId, qrInfo);

        // 发送WebSocket消息到特定的二维码主题
        sendWebSocketMessage(qrId, statusMessage);
    }

    @Override
    public void notifyLoginSuccess(String qrId, String accessToken, String refreshToken, Date tokenExpiration) {
        log.info("推送登录成功事件 (WebSocket) - QR ID: {}", qrId);

        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (qrInfo == null) {
            notifyError(qrId, "二维码信息不存在");
            return;
        }

        // 调用重载方法，避免重复逻辑
        notifyLoginSuccess(qrInfo, accessToken, refreshToken, tokenExpiration);
    }

    @Override
    public void notifyLoginSuccess(QRCodeInfo qrInfo, String accessToken, String refreshToken, Date tokenExpiration) {
        String qrId = qrInfo.getQrId();
        log.info("推送登录成功事件 (WebSocket, 使用已有信息) - QR ID: {}", qrId);

        // 构建完整的登录响应信息，包含令牌和用户信息
        Map<String, Object> loginData = new HashMap<>();
        loginData.put("status", QRCodeStatus.CONFIRMED);
        loginData.put("qrId", qrId);
        loginData.put("userInfo", qrInfo.getUserInfo());
        loginData.put("accessToken", accessToken);
        loginData.put("refreshToken", refreshToken);
        loginData.put("tokenExpiration", tokenExpiration);

        // 通过WebSocket直接发送登录凭证
        sendWebSocketMessage(qrId, loginData);
    }

    @Override
    public void notifyLoginCancel(String qrId) {
        log.info("推送登录取消事件 (WebSocket) - QR ID: {}", qrId);

        Map<String, Object> cancelData = new HashMap<>();
        cancelData.put("status", QRCodeStatus.CANCELED);
        cancelData.put("qrId", qrId);
        cancelData.put("message", "用户取消了登录");

        // 通过WebSocket发送取消消息
        sendWebSocketMessage(qrId, cancelData);
    }

    @Override
    public void notifyError(String qrId, String error) {
        log.warn("推送错误事件 (WebSocket) - QR ID: {}, 错误: {}", qrId, error);

        Map<String, Object> errorData = new HashMap<>();
        errorData.put("status", "ERROR");
        errorData.put("qrId", qrId);
        errorData.put("error", error);
        errorData.put("message", "发生错误: " + error);

        // 通过WebSocket发送错误消息
        sendWebSocketMessage(qrId, errorData);
    }

    /**
     * 构建状态消息
     *
     * @param qrId 二维码ID
     * @param qrInfo 二维码信息
     * @return 状态消息
     */
    private Map<String, Object> buildStatusMessage(String qrId, QRCodeInfo qrInfo) {
        Map<String, Object> statusMessage = new HashMap<>();
        statusMessage.put("qrId", qrId);
        statusMessage.put("status", qrInfo.getStatus());
        statusMessage.put("expired", qrInfo.isExpired());

        // 如果状态为已确认，添加用户信息
        if (qrInfo.getStatus() == QRCodeStatus.CONFIRMED) {
            statusMessage.put("userInfo", qrInfo.getUserInfo());
        }

        return statusMessage;
    }

    /**
     * 发送WebSocket消息
     *
     * @param qrId 二维码ID
     * @param message 消息内容
     */
    private void sendWebSocketMessage(String qrId, Map<String, Object> message) {
        try {
            String topic = "/topic/qrlogin/" + qrId;
            messagingTemplate.convertAndSend(topic, message);
            log.debug("WebSocket 消息发送成功 - 主题: {}, QR ID: {}", topic, qrId);
        } catch (Exception e) {
            log.error("WebSocket 消息发送失败 - QR ID: {}", qrId, e);
            throw new BusinessException(ResponseCode.OPERATION_FAILED, "WebSocket 消息发送失败: " + e.getMessage());
        }
    }
}
