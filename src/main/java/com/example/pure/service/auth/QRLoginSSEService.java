package com.example.pure.service.auth;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 二维码登录 SSE 服务接口
 * <p>
 * 提供基于 Server-Sent Events 的二维码登录状态推送服务
 * </p>
 */
public interface QRLoginSSEService {

    /**
     * 创建二维码状态监听的 SSE 连接
     * <p>
     * 客户端调用此方法建立 SSE 连接，实时接收二维码状态变化
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return SSE发射器，用于推送状态变化
     */
    SseEmitter subscribeQRStatus(String qrId);

    /**
     * 推送二维码状态变化
     * <p>
     * 当二维码状态发生变化时，向所有订阅该二维码的客户端推送状态更新
     * </p>
     *
     * @param qrId 二维码唯一标识
     */
    void notifyStatusChange(String qrId);

    /**
     * 推送二维码状态变化（使用已有的二维码信息）
     * <p>
     * 当二维码状态发生变化时，向所有订阅该二维码的客户端推送状态更新
     * 使用已获取的二维码信息，避免重复查询 Redis
     * </p>
     *
     * @param qrInfo 二维码信息
     */
    void notifyStatusChange(com.example.pure.util.QRLoginRedisUtil.QRCodeInfo qrInfo);

    /**
     * 推送登录成功事件
     * <p>
     * 当用户确认登录时，推送包含用户信息和令牌的登录成功事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     */
    void notifyLoginSuccess(String qrId, String accessToken, String refreshToken, java.util.Date tokenExpiration);

    /**
     * 推送登录成功事件（使用已有的二维码信息）
     * <p>
     * 当用户确认登录时，推送包含用户信息和令牌的登录成功事件
     * 使用已获取的二维码信息，避免重复查询 Redis
     * </p>
     *
     * @param qrInfo 二维码信息
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     */
    void notifyLoginSuccess(com.example.pure.util.QRLoginRedisUtil.QRCodeInfo qrInfo,
                          String accessToken, String refreshToken, java.util.Date tokenExpiration);

    /**
     * 推送登录取消事件
     * <p>
     * 当用户取消登录时，推送取消事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     */
    void notifyLoginCancel(String qrId);

    /**
     * 推送错误事件
     * <p>
     * 当发生错误时，推送错误事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @param error 错误信息
     */
    void notifyError(String qrId, String error);

    /**
     * 移除 SSE 连接
     * <p>
     * 清理指定二维码的所有 SSE 连接
     * </p>
     *
     * @param qrId 二维码唯一标识
     */
    void removeConnection(String qrId);

    /**
     * 获取活跃连接数
     * <p>
     * 获取当前活跃的 SSE 连接总数，用于监控
     * </p>
     *
     * @return 活跃连接数
     */
    int getActiveConnectionCount();
}
