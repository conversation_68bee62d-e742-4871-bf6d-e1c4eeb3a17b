package com.example.pure.service.auth.impl;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.model.dto.request.user.QRLoginSSEResponse;
import com.example.pure.service.auth.AuthService;
import com.example.pure.service.auth.QRLoginSSEService;
import com.example.pure.util.QRLoginRedisUtil;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 二维码登录 SSE 服务实现类
 * <p>
 * 基于 Server-Sent Events 实现二维码登录状态的实时推送
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QRLoginSSEServiceImpl implements QRLoginSSEService {

    private final QRLoginRedisUtil qrLoginUtil;
    private final AuthService authService;
    private final ObjectMapper objectMapper;

    /**
     * 单例模式类(由spring管理@Bean使用注解默认作用域的情况)里方法外的ConcurrentHashMap(实例变量)是所有用户都共享的
     * 用户1在线程1保存到这，然后用户2用线程2是能访问他保护的数据的，而方法里的变量作用域每次调用都会重置的
     * SSE 连接管理
     * Key: qrId, Value: 该二维码对应的所有 SSE 连接列表
     * ConcurrentHashMap解决多线程下线程安全问题，你可以在多个线程中同时对它进行读写，它能保证数据的原子性和可见性
     */
    private final ConcurrentHashMap<String, CopyOnWriteArrayList<SseEmitter>> connections = new ConcurrentHashMap<>();

    /**
     * SSE 连接超时时间
     * 从 SecurityConstants 中获取统一配置
     */
    private static final long SSE_TIMEOUT = SecurityConstants.SSE_TIMEOUT;

    @Override
    public SseEmitter subscribeQRStatus(String qrId) {
        log.info("创建二维码 SSE 订阅连接 - QR ID: {}", qrId);

        // 创建 SSE 发射器，超时时间为创建开始计算，只要达到时间就停止连接
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

        // 添加到连接管理
        CopyOnWriteArrayList<SseEmitter> emitterList = connections.get(qrId);
        if (ObjectUtils.isEmpty(emitterList)) {
            emitterList = new CopyOnWriteArrayList<>();
            connections.put(qrId, emitterList);
        }
        emitterList.add(emitter);

        // 设置连接完成和超时回调
        emitter.onCompletion(() -> {
            removeEmitter(qrId, emitter);
            log.debug("SSE 连接完成 - QR ID: {}", qrId);
        });

        emitter.onTimeout(() -> {
            removeEmitter(qrId, emitter);
            log.debug("SSE 连接超时 - QR ID: {}", qrId);
        });

        emitter.onError((throwable) -> {
            removeEmitter(qrId, emitter);
            log.warn("SSE 连接错误 - QR ID: {}", qrId, throwable);
        });

        // http请求线程检测到@async启用线程池来调用子线程运行任务，然后http请求线程返回emitter，子线程后续把数据写入e
        // 异步发送初始状态
        sendInitialStatusAsync(emitter, qrId);

        return emitter;
    }

    @Override
    public void notifyStatusChange(String qrId) {
        log.debug("推送二维码状态变化 - QR ID: {}", qrId);

        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (qrInfo == null) {
            notifyError(qrId, "二维码不存在或已失效");
            return;
        }

        // 调用重载方法，避免重复逻辑
        notifyStatusChange(qrInfo);
    }

    @Override
    public void notifyStatusChange(QRCodeInfo qrInfo) {
        String qrId = qrInfo.getQrId();
        log.debug("推送二维码状态变化 (SSE, 使用已有信息) - QR ID: {}", qrId);

        QRLoginSSEResponse response = QRLoginSSEResponse.createStatusEvent(
                qrId, qrInfo.getStatus(), qrInfo.isExpired());

        sendEventToAllConnections(qrId, "status", response);
    }

    @Override
    public void notifyLoginSuccess(String qrId, String accessToken, String refreshToken, java.util.Date tokenExpiration) {
        log.info("推送登录成功事件 - QR ID: {}", qrId);

        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (qrInfo == null) {
            notifyError(qrId, "二维码信息不存在");
            return;
        }

        // 调用重载方法，避免重复逻辑
        notifyLoginSuccess(qrInfo, accessToken, refreshToken, tokenExpiration);
    }

    @Override
    public void notifyLoginSuccess(QRCodeInfo qrInfo, String accessToken, String refreshToken, java.util.Date tokenExpiration) {
        String qrId = qrInfo.getQrId();
        log.info("推送登录成功事件 (使用已有信息) - QR ID: {}", qrId);

        QRLoginSSEResponse response = QRLoginSSEResponse.createLoginSuccessEvent(
                qrId, qrInfo.getUserInfo(), accessToken, refreshToken, tokenExpiration);

        sendEventToAllConnections(qrId, "login", response);

        // 登录成功后清理连接
        removeConnection(qrId);
    }

    @Override
    public void notifyLoginCancel(String qrId) {
        log.info("推送登录取消事件 - QR ID: {}", qrId);

        QRLoginSSEResponse response = QRLoginSSEResponse.createCancelEvent(qrId);
        sendEventToAllConnections(qrId, "cancel", response);

        // 取消后清理连接
        removeConnection(qrId);
    }

    @Override
    public void notifyError(String qrId, String error) {
        log.warn("推送错误事件 - QR ID: {}, 错误: {}", qrId, error);

        QRLoginSSEResponse response = QRLoginSSEResponse.createErrorEvent(qrId, error);
        sendEventToAllConnections(qrId, "error", response);

        // 错误后清理连接
        removeConnection(qrId);
    }



    @Override
    public int getActiveConnectionCount() {
        return connections.values().stream()
                .mapToInt(CopyOnWriteArrayList::size)
                .sum();
    }

    /**
     * 异步发送初始状态
     */
    @Async("ioIntensiveTaskExecutor")
    public void sendInitialStatusAsync(SseEmitter emitter, String qrId) {
        try {
            // 发送开始事件
            QRLoginSSEResponse startResponse = QRLoginSSEResponse.createStartEvent(qrId);
            sendEvent(emitter, "start", startResponse);

            // 发送当前状态
            QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
            if (!ObjectUtils.isEmpty(qrInfo)) {
                // 二维码存在，发送状态信息
                QRLoginSSEResponse statusResponse = QRLoginSSEResponse.createStatusEvent(
                        qrId, qrInfo.getStatus(), qrInfo.isExpired());
                sendEvent(emitter, "status", statusResponse);
            } else {
                // 二维码不存在，发送错误信息
                QRLoginSSEResponse errorResponse = QRLoginSSEResponse.createErrorEvent(qrId, "二维码不存在或已失效");
                sendEvent(emitter, "error", errorResponse);
            }

            log.debug("初始状态发送完成 - QR ID: {}", qrId);
        } catch (Exception e) {
            log.error("发送初始状态失败 - QR ID: {}", qrId, e);
            try {
                emitter.completeWithError(e);
            } catch (Exception ex) {
                log.error("完成 SSE 连接时发生错误", ex);
            }
        }
    }

    @Override
    public void removeConnection(String qrId) {
        CopyOnWriteArrayList<SseEmitter> emitters = connections.remove(qrId);
        if (emitters != null) {
            for (SseEmitter emitter : emitters) {
                try {
                    QRLoginSSEResponse endResponse = QRLoginSSEResponse.createEndEvent(qrId);
                    sendEvent(emitter, "end", endResponse);
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("关闭 SSE 连接时发生错误 - QR ID: {}", qrId, e);
                }
            }
            log.debug("清理二维码 SSE 连接 - QR ID: {}, 连接数: {}", qrId, emitters.size());
        }
    }

    /**
     * 向指定二维码的所有连接发送事件
     */
    private void sendEventToAllConnections(String qrId, String eventName, QRLoginSSEResponse response) {
        CopyOnWriteArrayList<SseEmitter> emitters = connections.get(qrId);
        if (emitters == null || emitters.isEmpty()) {
            log.debug("没有找到二维码的 SSE 连接 - QR ID: {}", qrId);
            return;
        }

        // 使用迭代器安全地遍历和移除失效连接
        emitters.removeIf(emitter -> {
            try {
                sendEvent(emitter, eventName, response);
                return false; // 保留有效连接
            } catch (Exception e) {
                log.warn("发送 SSE 事件失败，移除连接 - QR ID: {}", qrId, e);
                try {
                    emitter.completeWithError(e);
                } catch (Exception ex) {
                    log.error("完成错误的 SSE 连接时发生异常", ex);
                }
                return true; // 移除失效连接
            }
        });

        log.debug("SSE 事件发送完成 - QR ID: {}, 事件: {}, 连接数: {}", qrId, eventName, emitters.size());
    }

    /**
     * 发送单个 SSE 事件
     */
    private void sendEvent(SseEmitter emitter, String eventName, QRLoginSSEResponse response) throws IOException {
        String jsonData = objectMapper.writeValueAsString(response);
        emitter.send(SseEmitter.event()
                .name(eventName)
                .data(jsonData)
                .id(String.valueOf(System.currentTimeMillis())));
    }

    /**
     * 从连接列表中移除指定的发射器
     */
    private void removeEmitter(String qrId, SseEmitter emitter) {
        CopyOnWriteArrayList<SseEmitter> emitters = connections.get(qrId);
        if (emitters != null) {
            emitters.remove(emitter);
            if (emitters.isEmpty()) {
                connections.remove(qrId);
            }
        }
    }
}
