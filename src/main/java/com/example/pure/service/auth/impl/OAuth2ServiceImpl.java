package com.example.pure.service.auth.impl;

import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.OAuth2Mapper;
import com.example.pure.mapper.primary.UserMapper;
import com.example.pure.mapper.primary.UserProfileMapper;
import com.example.pure.model.dto.response.auth.DeviceInfo;
import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.model.entity.OAuth2;
import com.example.pure.model.entity.User;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.service.auth.DeviceService;
import com.example.pure.service.auth.OAuth2Service;
import com.example.pure.service.userprofile.UserProfileService;
import com.example.pure.service.user.UserService;
import com.example.pure.util.HashingUtil;
import com.example.pure.util.HttpUtil;
import com.example.pure.util.JwtUtil;
import com.example.pure.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.model.AuthToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class OAuth2ServiceImpl implements OAuth2Service {


    private final UserMapper userMapper;
    private final UserProfileService userProfileService;


    private final JwtUtil jwtUtil;
    private final HttpUtil httpUtil;

    private final PasswordEncoder passwordEncoder;

    private final RedisTemplate<String, Object> redisTemplate;

    private final RedisUtil redisUtil;
    private final UserProfileMapper userProfileMapper;

    private final UserService userService;
    OAuth2Mapper oAuth2Mapper;

    private final DeviceService deviceService;
    private final HashingUtil hashUtil;

    @Autowired
    public OAuth2ServiceImpl(UserMapper userMapper, JwtUtil jwtUtil, HttpUtil httpUtil,
                             PasswordEncoder passwordEncoder, RedisTemplate<String, Object> redisTemplate,
                             RedisUtil redisUtil, UserProfileService userProfileService, UserProfileMapper userProfileMapper,
                             UserService userService, OAuth2Mapper oAuth2Mapper, DeviceService deviceService,
                             HashingUtil hashUtil
    ) {
        this.userMapper = userMapper;
        this.jwtUtil = jwtUtil;
        this.httpUtil = httpUtil;
        this.passwordEncoder = passwordEncoder;
        this.redisTemplate = redisTemplate;
        this.redisUtil = redisUtil;
        this.userProfileService = userProfileService;
        this.userProfileMapper = userProfileMapper;
        this.oAuth2Mapper = oAuth2Mapper;
        this.deviceService = deviceService;
        this.userService = userService;
        this.hashUtil = hashUtil;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> handleOAuth2User(AuthUser authUser) {
        try {
            // 检查Redis缓存中是否存在用户信息，复杂用到事务的用手动缓存到redis
            String cacheKey = "oauth2:user:" + authUser.getSource() + ":" + authUser.getUuid();
            Object cachedUser = redisUtil.get(cacheKey);

            if (cachedUser != null) {
                log.info("Found cached OAuth2 user information");
                //从Redis获取的Object实际上是Map<String, Object>使用（）强制类型转换为Map<String, Object>
                return (Map<String, Object>) cachedUser;
            }

            log.info("Processing OAuth2 user: {}", authUser.getUsername());

            // 1. 检查用户是否已存在
            UserDTO existingUser = userMapper.findByUsername(authUser.getUsername());

            // 用来确保创建和已存在用户都能获取到id
            Long createdUserId;

            // 2. 如果用户不存在，创建新用户
            if (ObjectUtils.isEmpty(existingUser)) {
                log.info("Creating new user for OAuth2 user: {}", authUser.getUsername());

                // 检查邮箱是否已被使用
                if (authUser.getEmail() != null && userProfileService.findUserIdByEmail(authUser.getEmail())>0) {
                    throw new BusinessException("邮箱已被使用");
                }

                User newUser = new User();
                newUser.setUsername(authUser.getUsername());
                // 为OAuth2用户生成随机密码
                String randomPassword = UUID.randomUUID().toString();
                newUser.setPassword(passwordEncoder.encode(randomPassword));
                newUser.setEnabled(true);
                newUser.setAccountNonExpired(true);
                newUser.setAccountNonLocked(true);
                newUser.setCredentialsNonExpired(true);
                newUser.setCreatedTime(Instant.now());
                newUser.setUpdatedTime(Instant.now());
                newUser.setLastLoginTime(Instant.now());

                // 创建并保存用户
                userMapper.insert(newUser);
                log.info("生成的用户ID为：{}",newUser.getId());
                log.info("Successfully created new user: {}", newUser.getUsername());

                // 保存OAuth2主要信息
                OAuth2 oAuth2 = new OAuth2();
                oAuth2.setUserId(newUser.getId());
                oAuth2.setProvider(authUser.getSource());
                oAuth2.setAccessToken(authUser.getToken().getAccessToken());
                oAuth2.setAccessTokenExpireIn(authUser.getToken().getExpireIn());
                oAuth2.setRefreshToken(authUser.getToken().getRefreshToken());
                oAuth2.setRefreshTokenExpireIn(authUser.getToken().getRefreshTokenExpireIn());

                // 插入OAuth2数据
                int oAuth2Result = oAuth2Mapper.insertOAuth2Info(oAuth2);
                if (oAuth2Result == 0) {
                    throw new BusinessException("保存OAuth2信息失败");
                }
                log.info("生成的OAuthId为：{}",oAuth2.getId());

                // 设置用户资料
                UserProfile userProfile = new UserProfile();
                userProfile.setId(newUser.getId());
                userProfile.setUsername(newUser.getUsername());
                userProfile.setPhone("");
                userProfile.setEmail(authUser.getEmail());
                userProfile.setNickname(authUser.getNickname());
                userProfile.setAvatar(authUser.getAvatar());
                userProfile.setDescription("");
                int rowsAffected=userProfileMapper.createNewUserProfile(userProfile);
                if (rowsAffected==0){
                    throw new BusinessException("创建用户详细信息失败");
                }



                // 设置用户角色
                int roleResult=userMapper.insertUserRole(newUser.getId(), 2L); // 假设1是ROLE_USER的ID
                if (roleResult==0){
                    throw new BusinessException("设置用户角色失败");
                }
                createdUserId=newUser.getId();

            } else {

                OAuth2 existingOAuth2=oAuth2Mapper.findOAuth2InfoByUserIdAndProvider(existingUser.getId(), authUser.getSource());
                createdUserId=existingUser.getId();

                // 如果用户已存在，更新Oauth2的刷新令牌
                existingOAuth2.setAccessToken(authUser.getToken().getAccessToken());
                existingOAuth2.setAccessTokenExpireIn(authUser.getToken().getExpireIn());
                existingOAuth2.setRefreshToken(authUser.getToken().getRefreshToken());
                existingOAuth2.setRefreshTokenExpireIn(authUser.getToken().getRefreshTokenExpireIn());

                int updateResult=oAuth2Mapper.updateOAuth2Info(existingOAuth2);
                if (updateResult == 0) {
                    throw new BusinessException("更新OAuth2信息失败");
                }

            }
            User userNow=userMapper.findUserWithPasswordByUserId(createdUserId);

            // 3. 创建UserDetails对象
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_COMMON"));
            UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                    userNow.getUsername(),
                    userNow.getPassword(),
                    authorities
            );

            // 4. 生成JWT令牌
            String accessToken =  jwtUtil.generateToken(userDetails);
            String refreshToken = jwtUtil.generateRefreshToken(userDetails);
            String encodedAccessToken=hashUtil.passwordEncoder(accessToken);
            String encodedRefreshToken=hashUtil.passwordEncoder(refreshToken);
            Date accessTokenExpiration = jwtUtil.getTokenExpirationDate(accessToken);
            Date refreshTokenExpiration = jwtUtil.getTokenExpirationDate(refreshToken);
            Instant refreshTokenExpires = refreshTokenExpiration.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toInstant();

            // 先用uuid顶替
            String deviceId = UUID.randomUUID().toString();
            // 获取当前HTTP请求，用于提取设备信息
            HttpServletRequest httpRequest = httpUtil.getCurrentRequest();

            // 创建设备信息
            DeviceInfo accessDeviceInfo = deviceService.createDeviceInfo(httpRequest, deviceId, encodedAccessToken);

            // 6. 设备管理：添加新设备或验证现有设备
            // 如果返回了设备ID，表示因为设备数量超限，移除了一个旧设备
            String removedAccessDeviceId = deviceService.addAccessTokenDevice(userNow.getUsername(), accessDeviceInfo);
            if (removedAccessDeviceId != null) {
                log.info("用户 {} 的 Access Token 设备数超限，已移除最早的设备: {}", userNow.getUsername(), removedAccessDeviceId);
            }

            // Refresh Token
            DeviceInfo refreshDeviceInfo = deviceService.createDeviceInfo(httpRequest, deviceId, encodedRefreshToken);
            String removedRefreshDeviceId = deviceService.addRefreshTokenDevice(userNow.getUsername(), refreshDeviceInfo);
            if (removedRefreshDeviceId != null) {
                log.info("用户 {} 的 Refresh Token 设备数超限，已移除最早的设备: {}", userNow.getUsername(), removedRefreshDeviceId);
            }

            // 更新用户最后登录时间和刷新令牌以及刷新令牌时间
            userNow.setLastLoginTime(Instant.now());
            userNow.setRefreshToken(encodedRefreshToken);
            userNow.setRefreshTokenExpires(refreshTokenExpires);
            userService.updateUser(userNow);


            // 5. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("access_token", accessToken);
            result.put("refresh_token", refreshToken);
            result.put("token_type", "Bearer");

            // 添加用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", userNow.getId());
            userInfo.put("username", userNow.getUsername());
            //userInfo.put("nickname", userNow.getNickname());
            //userInfo.put("email", userNow.getEmail());
            //userInfo.put("avatar", userNow.getAvatar());
            result.put("user_info", userInfo);

            // 添加第三方平台的token信息
            AuthToken authToken = authUser.getToken();
            if (authToken != null) {
                Map<String, Object> thirdPartyToken = new HashMap<>();
                thirdPartyToken.put("access_token",  authToken.getAccessToken());
                thirdPartyToken.put("refresh_token", authToken.getRefreshToken());
                thirdPartyToken.put("expire_in", authToken.getExpireIn());
                thirdPartyToken.put("scope", authToken.getScope());
                thirdPartyToken.put("token_type", authToken.getTokenType());
                thirdPartyToken.put("id_token", authToken.getIdToken());
                thirdPartyToken.put("platform", authUser.getSource());
                result.put("third_party_token", thirdPartyToken);
            }

            log.info("Successfully processed OAuth2 user: {}", authUser.getUsername());

            // 将结果缓存到Redis中，设置1小时过期时间
            redisUtil.set(cacheKey, result, 3600);

            return result;
        } catch (Exception e) {
            log.error("Error processing OAuth2 user: {}", authUser.getUsername(), e);
            throw new BusinessException("处理OAuth2用户失败: " + e.getMessage());
        }
    }

    /**
     * 转换性别格式
     * JustAuth的性别: UNKNOWN/MALE/FEMALE
     * 我们的性别: 0-未知，1-男，2-女
     */
    private Integer convertGender(AuthUserGender gender) {
        if (gender == null) {
            return 0;
        }
        switch (gender) {
            case MALE:
                return 1;
            case FEMALE:
                return 2;
            case UNKNOWN:
            default:
                return 0;
        }
    }
}
