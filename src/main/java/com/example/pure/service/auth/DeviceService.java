package com.example.pure.service.auth;

import com.example.pure.model.dto.response.auth.DeviceInfo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 设备管理服务接口
 * <p>
 * 用于管理用户登录设备信息，实现多设备登录限制
 * </p>
 */
public interface DeviceService {

    /**
     * 最大允许登录设备数
     */
    int MAX_DEVICES = 3;


    DeviceInfo createDeviceInfo(HttpServletRequest request, String deviceId, String token);

    /**
     * 添加用户设备登录记录
     * <p>
     * 当用户设备数量达到限制时，会删除最早登录的设备
     * </p>
     *
     * @param username 用户名
     * @param deviceInfo 设备信息 (包含 Access Token)
     * @return 被踢出的设备ID（如果有）
     */
    String addAccessTokenDevice(String username, DeviceInfo deviceInfo);

    /**
     * 添加用户 Refresh Token 设备登录记录
     *
     * @param username 用户名
     * @param deviceInfo 设备信息 (包含 Refresh Token)
     * @return 被踢出的设备ID（如果有）
     */
    String addRefreshTokenDevice(String username, DeviceInfo deviceInfo);

    /**
     * 检查 Access Token 是否在设备列表中有效
     *
     * @param username 用户名
     * @param accessToken JWT Access Token
     * @return 如果Token有效则返回true，否则返回false
     */
    boolean isAccessTokenValid(String username, String accessToken);

    /**
     * 移除用户特定 Access Token 的设备
     *
     * @param username 用户名
     * @param accessToken JWT Access Token
     * @return 如果成功移除则返回true，否则返回false
     */
    boolean removeDeviceByAccessToken(String username, String accessToken);

    /**
     * 移除用户特定 Refresh Token 的设备
     *
     * @param username 用户名
     * @param refreshToken JWT Refresh Token
     * @return 如果成功移除则返回true，否则返回false
     */
    boolean removeDeviceByRefreshToken(String username, String refreshToken);

    /**
     * 获取用户的所有 Access Token 登录设备
     *
     * @param username 用户名
     * @return 用户的所有登录设备信息
     */
    List<DeviceInfo> getAccessTokenDevices(String username);

    /**
     * 移除用户所有设备的登录信息
     * (将同时清除 access 和 refresh token)
     *
     * @param username 用户名
     */
    void removeAllUserDevices(String username);
}
