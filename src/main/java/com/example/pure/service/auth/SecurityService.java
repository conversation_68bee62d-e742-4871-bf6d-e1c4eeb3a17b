package com.example.pure.service.auth;

import com.example.pure.security.CustomUserDetails;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * 安全服务类
 * 提供安全相关的辅助方法
 */
@Service("securityService")
public class SecurityService {

    /**
     * 检查当前用户是否为指定ID的用户
     *
     * @param userId 用户ID
     * @return 如果当前用户ID与指定ID相同，返回true；否则返回false
     */
    public boolean isCurrentUser(Long userId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        Object principal = authentication.getPrincipal();
        // 检查 principal 对象是否是 CustomUserDetails 类型（或其子类型）
        if (principal instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) principal;
            return userDetails.getUserId().equals(userId);
        }
        return false;
    }
    public boolean isCurrentUser(String username) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) principal;
            return userDetails.getUsername().equals(username);
        }
        return false;
    }
}
