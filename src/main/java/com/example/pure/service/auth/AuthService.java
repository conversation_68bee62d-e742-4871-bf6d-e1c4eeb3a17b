package com.example.pure.service.auth;

import com.example.pure.model.dto.request.user.LoginRequest;
import com.example.pure.model.dto.response.auth.TokenResponse;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录成功的令牌信息
     */
    TokenResponse login(LoginRequest request);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的令牌信息
     */
    TokenResponse refreshToken(String refreshToken);

    /**
     * 检查用户令牌是否即将过期
     * 此方法会检查token是否会在指定的分钟数内过期，用于提前刷新token
     * 如果minutesThreshold为0，则等同于检查token是否已经过期
     * 如果token无效（签名错误等），也会返回true
     *
     * @param token JWT令牌
     * @param minutesThreshold 过期阈值（分钟），0表示检查是否已过期
     * @return 如果令牌即将在指定分钟数内过期返回true，否则返回false
     */
    boolean isTokenAboutToExpire(String token, int minutesThreshold);

    /**
     * 为指定用户创建JWT令牌
     *
     * @param username 用户名
     * @return 包含访问令牌和刷新令牌的响应
     */
    TokenResponse createQRLoginTokens(String username);

    /**
     * 用户注销
     * 从设备列表中移除对应的令牌
     *
     * @param username 用户名
     * @param token JWT令牌
     */
    void logout(String username, String token);

}
