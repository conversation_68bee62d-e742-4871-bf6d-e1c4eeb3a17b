package com.example.pure.service.auth;

import com.example.pure.model.dto.response.auth.QRLoginDTO.CommunicationType;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;

import java.util.Date;

/**
 * 二维码登录通知服务接口
 * <p>
 * 统一管理 WebSocket 和 SSE 两种通信方式的二维码登录通知
 * </p>
 */
public interface QRLoginNotificationService {

    /**
     * 根据通信类型推送状态变化
     *
     * @param qrId 二维码唯一标识
     * @param communicationType 通信类型
     */
    void notifyStatusChange(String qrId, CommunicationType communicationType);

    /**
     * 根据通信类型推送状态变化（使用已有的二维码信息）
     * <p>
     * 使用已获取的二维码信息，避免重复查询 Redis
     * </p>
     *
     * @param qrInfo 二维码信息
     * @param communicationType 通信类型
     */
    void notifyStatusChange(QRCodeInfo qrInfo, CommunicationType communicationType);

    /**
     * 自动获取二维码信息并推送状态变化
     * <p>
     * 从 Redis 中获取二维码信息，解析通信类型，然后推送状态变化
     * 避免控制器重复获取 QRCodeInfo
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 二维码信息（如果存在）
     * @throws com.example.pure.exception.BusinessException 如果二维码不存在
     */
    QRCodeInfo notifyStatusChangeAuto(String qrId);

    /**
     * 根据通信类型推送登录成功事件
     *
     * @param qrId 二维码唯一标识
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     * @param communicationType 通信类型
     */
    void notifyLoginSuccess(String qrId, String accessToken, String refreshToken,
                          Date tokenExpiration, CommunicationType communicationType);

    /**
     * 根据通信类型推送登录成功事件（使用已有的二维码信息）
     * <p>
     * 使用已获取的二维码信息，避免重复查询 Redis
     * </p>
     *
     * @param qrInfo 二维码信息
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     * @param communicationType 通信类型
     */
    void notifyLoginSuccess(QRCodeInfo qrInfo, String accessToken, String refreshToken,
                          Date tokenExpiration, CommunicationType communicationType);

    /**
     * 自动获取二维码信息并推送登录成功事件
     * <p>
     * 从 Redis 中获取二维码信息，解析通信类型，然后推送登录成功事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpiration 令牌过期时间
     * @return 二维码信息（如果存在）
     * @throws com.example.pure.exception.BusinessException 如果二维码不存在
     */
    QRCodeInfo notifyLoginSuccessAuto(String qrId, String accessToken, String refreshToken, Date tokenExpiration);

    /**
     * 根据通信类型推送登录取消事件
     *
     * @param qrId 二维码唯一标识
     * @param communicationType 通信类型
     */
    void notifyLoginCancel(String qrId, CommunicationType communicationType);

    /**
     * 自动获取二维码信息并推送登录取消事件
     * <p>
     * 从 Redis 中获取二维码信息，解析通信类型，然后推送登录取消事件
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 二维码信息（如果存在）
     * @throws com.example.pure.exception.BusinessException 如果二维码不存在
     */
    QRCodeInfo notifyLoginCancelAuto(String qrId);

    /**
     * 根据通信类型推送错误事件
     *
     * @param qrId 二维码唯一标识
     * @param error 错误信息
     * @param communicationType 通信类型
     */
    void notifyError(String qrId, String error, CommunicationType communicationType);

    /**
     * 获取系统统计信息
     *
     * @return 统计信息
     */
    java.util.Map<String, Object> getSystemStats();
}
