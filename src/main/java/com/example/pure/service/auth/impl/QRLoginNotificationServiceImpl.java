package com.example.pure.service.auth.impl;

import com.example.pure.constant.ResponseCode;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.auth.QRLoginDTO.CommunicationType;
import com.example.pure.service.auth.QRLoginNotificationService;
import com.example.pure.service.auth.QRLoginSSEService;
import com.example.pure.service.auth.QRLoginWebSocketService;
import com.example.pure.util.QRLoginRedisUtil;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码登录通知服务实现类
 * <p>
 * 统一管理 WebSocket 和 SSE 两种通信方式的二维码登录通知
 * 根据客户端选择的通信类型，自动路由到相应的服务
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QRLoginNotificationServiceImpl implements QRLoginNotificationService {

    private final QRLoginSSEService sseService;
    private final QRLoginWebSocketService webSocketService;
    private final QRLoginRedisUtil qrLoginUtil;

    @Override
    public void notifyStatusChange(String qrId, CommunicationType communicationType) {
        log.debug("统一推送状态变化 - QR ID: {}, 通信类型: {}", qrId, communicationType);

        if (communicationType == CommunicationType.SSE) {
            sseService.notifyStatusChange(qrId);
        } else {
            // 默认使用 WebSocket
            webSocketService.notifyStatusChange(qrId);
        }
    }

    @Override
    public void notifyStatusChange(QRCodeInfo qrInfo, CommunicationType communicationType) {
        String qrId = qrInfo.getQrId();
        log.debug("统一推送状态变化 (使用已有信息) - QR ID: {}, 通信类型: {}", qrId, communicationType);

        if (communicationType == CommunicationType.SSE) {
            sseService.notifyStatusChange(qrInfo);
        } else {
            // 默认使用 WebSocket
            webSocketService.notifyStatusChange(qrInfo);
        }
    }

    @Override
    public QRCodeInfo notifyStatusChangeAuto(String qrId) {
        log.debug("自动推送状态变化 - QR ID: {}", qrId);

        // 获取二维码信息并解析通信类型
        QRInfoWithCommunicationType result = getQRInfoWithCommunicationType(qrId);

        // 推送状态变化（使用已有的二维码信息，避免重复查询）
        notifyStatusChange(result.getQrInfo(), result.getCommunicationType());

        return result.getQrInfo();
    }

    @Override
    public void notifyLoginSuccess(String qrId, String accessToken, String refreshToken,
                                 Date tokenExpiration, CommunicationType communicationType) {
        log.info("统一推送登录成功事件 - QR ID: {}, 通信类型: {}", qrId, communicationType);

        if (communicationType == CommunicationType.SSE) {
            sseService.notifyLoginSuccess(qrId, accessToken, refreshToken, tokenExpiration);
        } else {
            // 默认使用 WebSocket
            webSocketService.notifyLoginSuccess(qrId, accessToken, refreshToken, tokenExpiration);
        }
    }

    @Override
    public void notifyLoginSuccess(QRCodeInfo qrInfo, String accessToken, String refreshToken,
                                 Date tokenExpiration, CommunicationType communicationType) {
        String qrId = qrInfo.getQrId();
        log.info("统一推送登录成功事件 (使用已有信息) - QR ID: {}, 通信类型: {}", qrId, communicationType);

        if (communicationType == CommunicationType.SSE) {
            sseService.notifyLoginSuccess(qrInfo, accessToken, refreshToken, tokenExpiration);
        } else {
            // 默认使用 WebSocket
            webSocketService.notifyLoginSuccess(qrInfo, accessToken, refreshToken, tokenExpiration);
        }
    }

    @Override
    public QRCodeInfo notifyLoginSuccessAuto(String qrId, String accessToken, String refreshToken, Date tokenExpiration) {
        log.info("自动推送登录成功事件 - QR ID: {}", qrId);

        // 获取二维码信息并解析通信类型
        QRInfoWithCommunicationType result = getQRInfoWithCommunicationType(qrId);

        // 推送登录成功事件（使用已有的二维码信息，避免重复查询）
        notifyLoginSuccess(result.getQrInfo(), accessToken, refreshToken, tokenExpiration, result.getCommunicationType());

        return result.getQrInfo();
    }

    @Override
    public void notifyLoginCancel(String qrId, CommunicationType communicationType) {
        log.info("统一推送登录取消事件 - QR ID: {}, 通信类型: {}", qrId, communicationType);

        if (communicationType == CommunicationType.SSE) {
            sseService.notifyLoginCancel(qrId);
        } else {
            // 默认使用 WebSocket
            webSocketService.notifyLoginCancel(qrId);
        }
    }

    @Override
    public QRCodeInfo notifyLoginCancelAuto(String qrId) {
        log.info("自动推送登录取消事件 - QR ID: {}", qrId);

        // 获取二维码信息并解析通信类型
        QRInfoWithCommunicationType result = getQRInfoWithCommunicationType(qrId);

        // 推送登录取消事件
        notifyLoginCancel(qrId, result.getCommunicationType());

        return result.getQrInfo();
    }

    @Override
    public void notifyError(String qrId, String error, CommunicationType communicationType) {
        log.warn("统一推送错误事件 - QR ID: {}, 错误: {}, 通信类型: {}", qrId, error, communicationType);

        if (communicationType == CommunicationType.SSE) {
            sseService.notifyError(qrId, error);
        } else {
            // 默认使用 WebSocket
            webSocketService.notifyError(qrId, error);
        }
    }

    @Override
    public Map<String, Object> getSystemStats() {
        Map<String, Object> stats = new HashMap<>();

        // SSE 连接统计
        stats.put("activeSseConnections", sseService.getActiveConnectionCount());

        // 系统信息
        stats.put("timestamp", LocalDateTime.now());
        stats.put("supportedCommunicationTypes", new String[]{"WEBSOCKET", "SSE"});

        // 运行时信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memoryInfo = new HashMap<>();
        memoryInfo.put("totalMemory", runtime.totalMemory());
        memoryInfo.put("freeMemory", runtime.freeMemory());
        memoryInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        memoryInfo.put("maxMemory", runtime.maxMemory());
        stats.put("memoryInfo", memoryInfo);

        log.debug("获取系统统计信息: SSE连接数={}", sseService.getActiveConnectionCount());

        return stats;
    }

    /**
     * 获取二维码信息并解析通信类型
     * <p>
     * 统一的二维码信息获取和通信类型解析逻辑
     * 避免在多个方法中重复相同的代码
     * </p>
     *
     * @param qrId 二维码唯一标识
     * @return 包含二维码信息和通信类型的结果对象
     * @throws BusinessException 如果二维码不存在或已失效
     */
    private QRInfoWithCommunicationType getQRInfoWithCommunicationType(String qrId) {
        // 从 Redis 中获取二维码信息
        QRCodeInfo qrInfo = qrLoginUtil.getQRCodeInfo(qrId);
        if (qrInfo == null) {
            throw new BusinessException(ResponseCode.NOT_FOUND, "二维码不存在或已失效");
        }

        // 解析通信类型
        CommunicationType communicationType = parseCommunicationType(qrInfo.getCommunicationType());

        return new QRInfoWithCommunicationType(qrInfo, communicationType);
    }

    /**
     * 解析通信类型
     * <p>
     * 将存储在 Redis 中的字符串类型转换为枚举类型
     * 如果解析失败，使用默认的 WebSocket 类型
     * </p>
     *
     * @param communicationTypeStr 通信类型字符串
     * @return 通信类型枚举
     */
    private CommunicationType parseCommunicationType(String communicationTypeStr) {
        if (!StringUtils.hasText(communicationTypeStr)) {
            log.warn("通信类型为空，使用默认值 {}", SecurityConstants.DEFAULT_COMMUNICATION_TYPE);
            return CommunicationType.valueOf(SecurityConstants.DEFAULT_COMMUNICATION_TYPE);
        }

        try {
            return CommunicationType.valueOf(communicationTypeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("无法解析通信类型: {}, 使用默认值 {}", communicationTypeStr, SecurityConstants.DEFAULT_COMMUNICATION_TYPE);
            return CommunicationType.valueOf(SecurityConstants.DEFAULT_COMMUNICATION_TYPE);
        }
    }

    /**
     * 二维码信息和通信类型的组合结果
     * <p>
     * 内部类，用于封装二维码信息和解析后的通信类型
     * </p>
     */
    private static class QRInfoWithCommunicationType {
        private final QRCodeInfo qrInfo;
        private final CommunicationType communicationType;

        public QRInfoWithCommunicationType(QRCodeInfo qrInfo, CommunicationType communicationType) {
            this.qrInfo = qrInfo;
            this.communicationType = communicationType;
        }

        public QRCodeInfo getQrInfo() {
            return qrInfo;
        }

        public CommunicationType getCommunicationType() {
            return communicationType;
        }
    }
}
