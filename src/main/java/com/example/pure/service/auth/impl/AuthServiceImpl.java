package com.example.pure.service.auth.impl;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.RoleMapper;
import com.example.pure.model.entity.User;
import com.example.pure.model.dto.response.auth.DeviceInfo;
import com.example.pure.model.dto.request.user.LoginRequest;
import com.example.pure.model.dto.response.auth.TokenResponse;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.security.CustomUserDetailsService;
import com.example.pure.service.auth.AuthService;
import com.example.pure.service.auth.DeviceService;
import com.example.pure.service.auth.RoleService;
import com.example.pure.service.user.UserService;
import com.example.pure.util.HashingUtil;
import com.example.pure.util.HttpUtil;
import com.example.pure.util.IpUtil;
import com.example.pure.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Date;

/**
 * 认证服务实现类
 * 一般抛出异常通常在Service类里使用throw new抛出
 * 少数情况下在上一级抛出，function() throws Ex 然后方法里必须要手动throw才能让上一级catch后throw出异常
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    // AuthenticationManager 用于处理认证逻辑
    private final AuthenticationManager authenticationManager;
    private final UserService userService;
    private final RoleService roleService; // 注意：这个依赖似乎没有在 login 方法中使用，可以考虑移除如果确实没用到
    private final CustomUserDetailsService userDetailsService; // 注意：这个依赖似乎没有在 login 方法中使用，可以考虑移除如果确实没用到
    private final JwtUtil jwtUtil;
    private final RoleMapper roleMapper; // 注意：这个依赖似乎没有在 login 方法中使用，可以考虑移除如果确实没用到
    private final DeviceService deviceService;
    private final IpUtil ipUtil;
    private final HttpUtil httpUtil;
    private final HashingUtil hashUtil;


    /**
     * 构造函数注入依赖
     *
     * @param authenticationConfiguration 用于获取自动配置的 AuthenticationManager
     * @param userService 用户服务
     * @param roleService 角色服务 (如果需要)
     * @param userDetailsService 用户详情服务 (如果需要)
     * @param jwtUtil JWT 工具类
     * @param passwordEncoder 密码编码器
     * @param roleMapper 角色 Mapper (如果需要)
     * @param deviceService 设备服务
     * @param ipUtil IP 工具类
     * @throws Exception 当从 AuthenticationConfiguration 获取 AuthenticationManager 失败时抛出
     */
    @Autowired
    public AuthServiceImpl(
            AuthenticationConfiguration authenticationConfiguration, // 注入 AuthenticationConfiguration
            UserService userService,
            RoleService roleService,
            CustomUserDetailsService userDetailsService,
            JwtUtil jwtUtil,
            PasswordEncoder passwordEncoder,
            RoleMapper roleMapper,
            DeviceService deviceService,
            IpUtil ipUtil,
            HttpUtil httpUtil,
            HashingUtil hashUtil
            ) throws Exception { // 添加 throws Exception
        // 从 AuthenticationConfiguration 获取由 Spring Boot 自动配置好的 AuthenticationManager
        this.authenticationManager = authenticationConfiguration.getAuthenticationManager();
        this.userService = userService;
        this.roleService = roleService;
        this.userDetailsService = userDetailsService;
        this.jwtUtil = jwtUtil;
        this.roleMapper = roleMapper;
        this.deviceService = deviceService;
        this.ipUtil = ipUtil;
        this.httpUtil = httpUtil;
        this.hashUtil = hashUtil;

    }


    @Override
    public TokenResponse login(LoginRequest request) {
        log.debug("处理用户登录请求: {}", request.getUsername());
        String username = request.getUsername();
        String password = request.getPassword();
        String deviceId = request.getDeviceId(); // 获取前端可能提供的设备ID


        try {
            // 1. 使用注入的 AuthenticationManager 进行认证
            // 它会使用已配置的CustomUserDetailsService的获取账号加密的密码并保存到CustomUserDetails，使用PasswordEncoder来进行验证对比来验证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password)
            );

            // 2. 认证成功，从 Authentication 对象中获取 UserDetails
            // Spring Security 默认会将 UserDetailsService 返回的 UserDetails 放入 Principal
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();

            // 3. 生成访问令牌
            String accessToken = jwtUtil.generateToken(userDetails);
            String refreshToken = jwtUtil.generateRefreshToken(userDetails);

            // 加密令牌以存储数据库或Redis
            String encodedAccessToken=hashUtil.passwordEncoder(accessToken);
            String encodedFreshAccessToken=hashUtil.passwordEncoder(refreshToken);

            Date accessTokenExpiration = jwtUtil.getTokenExpirationDate(accessToken);
            Date refreshTokenExpiration = jwtUtil.getTokenExpirationDate(refreshToken);
            // 将Date对象转换为Instant
            Instant refreshTokenExpires = refreshTokenExpiration.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toInstant();


            // 4. 获取当前HTTP请求，用于提取设备信息
            HttpServletRequest httpRequest = httpUtil.getCurrentRequest();

            // 5. 为 Access Token 和 Refresh Token 分别创建设备信息并添加
            // Access Token
            DeviceInfo accessDeviceInfo = deviceService.createDeviceInfo(httpRequest, deviceId, encodedAccessToken);
            String removedAccessDeviceId = deviceService.addAccessTokenDevice(username, accessDeviceInfo);
            if (removedAccessDeviceId != null) {
                log.info("用户 {} 的 Access Token 设备数超限，已移除最早的设备: {}", username, removedAccessDeviceId);
            }

            // Refresh Token
            DeviceInfo refreshDeviceInfo = deviceService.createDeviceInfo(httpRequest, deviceId, encodedFreshAccessToken);
            String removedRefreshDeviceId = deviceService.addRefreshTokenDevice(username, refreshDeviceInfo);
            if (removedRefreshDeviceId != null) {
                log.info("用户 {} 的 Refresh Token 设备数超限，已移除最早的设备: {}", username, removedRefreshDeviceId);
            }


            // 7. 更新用户最后登录时间和刷新令牌

            User user = userService.findByUsername(username);
            user.setLastLoginTime(Instant.now());
            user.setRefreshToken(encodedFreshAccessToken);
            user.setRefreshTokenExpires(refreshTokenExpires);
            userService.updateUser(user);

            // 8. 构建返回结果
            log.info("用户登录成功: {}", username);
            return new TokenResponse(username,accessToken, refreshToken, accessTokenExpiration);

        } catch (BadCredentialsException e) {
            log.warn("用户登录失败 - 凭证错误: {}", username);
            throw new BusinessException("用户名或密码错误");
        } catch (UsernameNotFoundException e) {
            log.warn("用户登录失败 - 用户未找到: {}", username);
            throw new BusinessException("用户不存在");
        } catch (BusinessException e) {
            log.warn("用户登录失败 - 业务逻辑: {}", e.getMessage());
            throw e; // 直接重新抛出，由全局异常处理器处理
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            throw new BusinessException("登录失败: 账号或密码输入无效");
        }
    }







    @Override
    public TokenResponse refreshToken(String refreshToken) {
        if (refreshToken == null) {
            throw new BusinessException("请填写令牌Token");
        }
        log.debug("处理令牌刷新请求");

        // 去除令牌前缀（如果有）
        if (refreshToken.startsWith(SecurityConstants.TOKEN_PREFIX)) {
            refreshToken = refreshToken.substring(7);
        }

        try {
            // 1. 验证刷新令牌
            if (!jwtUtil.validateToken(refreshToken)) {
                log.warn("刷新令牌无效或已过期");
                throw new BusinessException("无效的刷新令牌");
            }

            // 2. 从令牌中获取用户名，并加载用户详情
            String username = jwtUtil.getUsernameFromToken(refreshToken);
            CustomUserDetails userDetails = (CustomUserDetails) userDetailsService.loadUserByUsername(username);
            if (userDetails == null) {
                log.warn("刷新令牌对应的用户不存在: {}", username);
                throw new BusinessException("用户不存在或已被删除");
            }

            // 3. 生成新的令牌
            String newAccessToken = jwtUtil.generateToken(userDetails);
            String newRefreshToken = jwtUtil.generateRefreshToken(userDetails);
            Date tokenExpiration = jwtUtil.getTokenExpirationDate(newAccessToken);
            Date refreshTokenExpiration = jwtUtil.getTokenExpirationDate(newRefreshToken);
            log.info("用户令牌创建成功: {}", username);

            // 加密新令牌以存储到Redis
            String encodedNewAccessToken = hashUtil.passwordEncoder(newAccessToken);
            String encodedNewRefreshToken = hashUtil.passwordEncoder(newRefreshToken);

            // 将Date对象转换为LocalDateTime
            Instant refreshTokenExpires = refreshTokenExpiration.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toInstant();
            // 4. 获取当前HTTP请求
            HttpServletRequest httpRequest = httpUtil.getCurrentRequest();

            // 5. 设备管理：移除旧的 Refresh Token 记录
            boolean removed = deviceService.removeDeviceByRefreshToken(username, refreshToken);
            if (!removed) {
                log.warn("未找到与旧刷新令牌关联的设备信息: {}", username);
                // 继续处理，因为这可能是首次使用此功能的旧用户
            }

            // 6. 创建新的 Access Token 和 Refresh Token 设备信息
            // Access Token
            DeviceInfo accessDeviceInfo = deviceService.createDeviceInfo(httpRequest, null, encodedNewAccessToken);
            String removedAccessDeviceId = deviceService.addAccessTokenDevice(username, accessDeviceInfo);
            if (removedAccessDeviceId != null) {
                log.info("用户 {} 刷新令牌后，Access Token 设备数超限，已移除最早的设备: {}", username, removedAccessDeviceId);
            }

            // Refresh Token
            DeviceInfo refreshDeviceInfo = deviceService.createDeviceInfo(httpRequest, null, encodedNewRefreshToken);
            String removedRefreshDeviceId = deviceService.addRefreshTokenDevice(username, refreshDeviceInfo);
            if (removedRefreshDeviceId != null) {
                log.info("用户 {} 刷新令牌后，Refresh Token 设备数超限，已移除最早的设备: {}", username, removedRefreshDeviceId);
            }

            User user = userService.findByUsername(username);
            if (ObjectUtils.isEmpty(user)) {
                log.error("无法更新用户最后登录时间：用户不存在: {}", username);
                throw new BusinessException("用户不存在: " + username);
            }

            user.setLastLoginTime(Instant.now());
            user.setRefreshToken(encodedNewRefreshToken);
            user.setRefreshTokenExpires(refreshTokenExpires);
            userService.updateUser(user);

            // 8. 构建返回结果

            log.info("令牌刷新成功: {}", username);
            return new TokenResponse(username,newAccessToken, newRefreshToken, tokenExpiration);
        } catch (BusinessException e) {
            // 直接抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage(), e);
            throw new BusinessException("令牌刷新失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isTokenAboutToExpire(String token, int minutesThreshold) {
        try {
            return jwtUtil.isTokenAboutToExpire(token, minutesThreshold);
        } catch (Exception e) {
            log.warn("Token validation failed: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 为指定用户创建JWT令牌
     * <p>
     * 此方法用于第三方认证（如OAuth2）或其他需要为已验证用户生成令牌的场景。
     * 用户身份已通过其他方式验证，无需密码验证。
     * </p>
     *
     * @param username 用户名
     * @return 包含访问令牌和刷新令牌的响应
     */
    @Override
    public TokenResponse createQRLoginTokens(String username) {
        log.debug("为用户创建令牌: {}", username);

        try {
            // 1. 加载用户详情
            CustomUserDetails userDetails = (CustomUserDetails) userDetailsService.loadUserByUsername(username);
            if (userDetails == null) {
                log.error("无法创建令牌：用户不存在: {}", username);
                throw new UsernameNotFoundException("用户不存在: " + username);
            }

            // 2. 生成访问令牌和刷新令牌
            String accessToken = jwtUtil.generateToken(userDetails);
            String refreshToken = jwtUtil.generateRefreshToken(userDetails);
            Date tokenExpiration = jwtUtil.getTokenExpirationDate(accessToken);
            Date refreshTokenExpiration = jwtUtil.getTokenExpirationDate(refreshToken);
            log.info("用户令牌创建成功: {}", username);

            // 加密令牌以存储到Redis
            String encodedAccessToken = hashUtil.passwordEncoder(accessToken);
            String encodedRefreshToken = hashUtil.passwordEncoder(refreshToken);

            // 将Date对象转换为Instant
            Instant refreshTokenExpires = refreshTokenExpiration.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toInstant();


            // 3. 获取当前HTTP请求
            HttpServletRequest httpRequest = httpUtil.getCurrentRequest();

            // 4. 创建设备信息并注册
            // Access Token
            DeviceInfo accessDeviceInfo = deviceService.createDeviceInfo(httpRequest, null, encodedAccessToken);
            deviceService.addAccessTokenDevice(username, accessDeviceInfo);

            // Refresh Token
            DeviceInfo refreshDeviceInfo = deviceService.createDeviceInfo(httpRequest, null, encodedRefreshToken);
            deviceService.addRefreshTokenDevice(username, refreshDeviceInfo);


            // 5. 更新用户的最后登录时间
            User user = userService.findByUsername(username);
            if (ObjectUtils.isEmpty(user)) {
                log.error("无法更新用户最后登录时间：用户不存在: {}", username);
                throw new BusinessException("用户不存在: " + username);
            }
            user.setLastLoginTime(Instant.now());
            user.setRefreshToken(encodedRefreshToken);
            user.setRefreshTokenExpires(refreshTokenExpires);
            userService.updateUser(user);

            // 6. 构建返回结果

            return new TokenResponse(user.getUsername(),accessToken, refreshToken, tokenExpiration);
        } catch (UsernameNotFoundException e) {
            log.error("创建令牌失败 - 用户不存在: {}", username);
            throw new BusinessException("用户不存在: " + username);
        } catch (BusinessException e) {
            log.error("创建令牌失败 - 业务逻辑: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建令牌失败: {}", e.getMessage(), e);
            throw new BusinessException("创建令牌失败: " + e.getMessage());
        }
    }

    /**
     * 用户注销登录
     * <p>
     * 从设备列表中移除对应的令牌，使其失效。
     * 注意：JWT本身不会失效，但系统将不再接受该令牌。
     * </p>
     *
     * @param username 用户名
     * @param token JWT令牌
     */
    @Override
    public void logout(String username, String token) {
        log.debug("处理用户注销请求: {}", username);

        try {
            // 1. 如果令牌包含前缀，去除前缀
            if (token.startsWith(SecurityConstants.TOKEN_PREFIX)) {
                token = token.substring(7);
            }

            // 2. 从设备列表中移除对应的 Access Token 令牌
            // 传递原始token，由DeviceService内部处理匹配
            boolean removed = deviceService.removeDeviceByAccessToken(username, token);

            // 3. 记录结果
            if (removed) {
                log.info("用户 {} 注销成功，设备已从登录设备列表中移除", username);
            } else {
                log.warn("用户 {} 注销成功，但未找到对应的设备信息", username);
            }

            // 4. 可选：记录用户注销事件
            //accessLogService.logAccess(username, "LOGOUT", ipUtil.getClientIp(getCurrentRequest()));

        } catch (BusinessException e) {
            log.error("用户注销失败 - 业务逻辑: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("用户注销失败: {}", e.getMessage(), e);
            throw new BusinessException("注销失败: " + e.getMessage());
        }
    }
}
