package com.example.pure.service.video;

/**
 * FFmpeg 服务，用于视频处理
 */
public interface FFmpegService {
    /**
     * 从视频生成雪碧图，上传后返回其公开访问URL。
     *
     * @param videoUrl      要处理的视频的URL
     * @param videoId       视频的ID，用于组织雪碧图的存储
     * @param episodeNumber 分集编号，用于为雪碧图创建唯一名称
     * @param uuid          用于R2存储路径的唯一标识符
     * @return 上传后的雪碧图的公开URL
     */
    String generateAndUploadSprite(String videoUrl, Long videoId, String episodeNumber, String uuid);
}
