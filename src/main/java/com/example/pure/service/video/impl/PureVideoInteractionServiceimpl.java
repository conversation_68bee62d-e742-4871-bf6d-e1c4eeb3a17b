package com.example.pure.service.video.impl;

import com.example.pure.model.dto.response.video.EpisodeLikeInfoDTO;
import com.example.pure.model.dto.request.video.VideoEpisodesLikesRequest;
import com.example.pure.mapper.primary.PureVideoInteractionMapper;
import com.example.pure.model.entity.VideoEpisodesLikes;
import com.example.pure.service.video.PureVideoInteractionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.Instant;

/**
 * 视频互动服务的实现类
 */
@Service
@Slf4j
public class PureVideoInteractionServiceimpl implements PureVideoInteractionService {

    private final PureVideoInteractionMapper pureVideoInteractionMapper;

    @Autowired
    public PureVideoInteractionServiceimpl(PureVideoInteractionMapper pureVideoInteractionMapper) {
        this.pureVideoInteractionMapper = pureVideoInteractionMapper;
    }


    /**
     * 创建或更新用户对视频分集的点赞状态。
     * <p>
     * 此方法实现了完整的点赞/取消点赞逻辑：
     * 1. 根据分集ID和用户ID，查询是否已存在点赞记录。
     * 2. 如果不存在（首次点赞），则创建一条新的点赞记录，状态(status)设置为 true。
     * 3. 如果已存在，则将该记录的 status 字段值取反，实现"取消点赞"或"重新点赞"的功能。
     * </p>
     * @param videoLikesRequest 包含视频分集ID和用户ID的请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEpisodesVideoLikes(VideoEpisodesLikesRequest videoLikesRequest) {
        Long videoEpisodeId = videoLikesRequest.getVideoEpisodeId();
        Long userId = videoLikesRequest.getUserId();

        VideoEpisodesLikes existingLike = pureVideoInteractionMapper.findLikesByEpisodeIdAndUserId(videoEpisodeId, userId);

        // 判断对数据库操作有没有成功的变量
        int rowsAffected;
        // 检查是否存在点赞记录
        if (ObjectUtils.isEmpty(existingLike)) {
            // 首次点赞，插入新记录
            VideoEpisodesLikes newLike = new VideoEpisodesLikes();
            newLike.setVideoEpisodeId(videoEpisodeId);
            newLike.setUserId(userId);
            newLike.setStatus(true); // 首次点赞，状态为true
            newLike.setCreatedTime(Instant.now());
            newLike.setUpdatedTime(Instant.now());
            rowsAffected = pureVideoInteractionMapper.insertVideoEpisodesLikes(newLike);
            log.info("用户 {} 首次点赞视频分集 {}", userId, videoEpisodeId);
        } else {
            // 已有记录，更新状态
            existingLike.setStatus(!existingLike.getStatus()); // 状态取反
            existingLike.setUpdatedTime(Instant.now());
            rowsAffected=pureVideoInteractionMapper.updateVideoEpisodesLikesStatus(existingLike);
            log.info("用户 {} 更新了对视频分集 {} 的点赞状态为: {}", userId, videoEpisodeId, existingLike.getStatus());
        }
        if (rowsAffected == 0) {
            throw new RuntimeException("更新点赞状态失败");
        }
    }


    /**
     * {@inheritDoc}
     * <p>
     * 直接调用Mapper层的方法，该方法通过高效的SQL查询来获取所需信息，
     * 并将结果直接映射到 {@link EpisodeLikeInfoDTO} 对象中。
     * </p>
     */
    @Override
    public EpisodeLikeInfoDTO getEpisodeLikeInfo(Long episodeId, Long userId) {
        log.debug("正在查询分集 {} 的点赞信息，用户ID: {}", episodeId, userId);
        EpisodeLikeInfoDTO likeInfo = pureVideoInteractionMapper.getEpisodeLikeInfo(episodeId, userId);
        log.debug("分集 {} 的点赞信息查询完成: {}", episodeId, likeInfo);
        return likeInfo;
    }
}
