package com.example.pure.service.video;

import com.example.pure.model.dto.response.video.EpisodeLikeInfoDTO;
import com.example.pure.model.dto.request.video.VideoEpisodesLikesRequest;

/**
 * 视频互动服务接口
 * <p>
 * 定义了处理视频互动（如评论、点赞）的业务逻辑。
 * </p>
 */
public interface PureVideoInteractionService {

    /**
     * 创建或更新用户对视频分集的点赞状态。
     * @param videoLikesRequest 包含视频分集ID和用户ID的请求对象
     */
    void createEpisodesVideoLikes(VideoEpisodesLikesRequest videoLikesRequest);

    /**
     * 获取单个视频分集的点赞信息，包括总点赞数和当前用户的点赞状态。
     *
     * @param episodeId 要查询的视频分集ID
     * @param userId    当前登录用户的ID (可以为null)
     * @return 包含该分集最新点赞信息的DTO
     */
    EpisodeLikeInfoDTO getEpisodeLikeInfo(Long episodeId, Long userId);

}
