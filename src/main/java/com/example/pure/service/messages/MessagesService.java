package com.example.pure.service.messages;

import com.example.pure.common.PageFinalResult;
import com.example.pure.model.dto.response.messages.MessageDTO;
import com.example.pure.model.dto.request.page.PageRequestDTO;
import com.example.pure.model.dto.request.messages.SystemMessageRequestDTO;
import com.example.pure.model.dto.response.messages.UnreadCountDTO; // 引入 UnreadCountDTO

/**
 * 消息通知功能的服务接口。
 */
public interface MessagesService {

    /**
     * 发送系统消息给用户。
     *
     * @param messageRequestDTO 系统消息请求 DTO。
     */
    void sendSystemMessage(SystemMessageRequestDTO messageRequestDTO);

    /**
     * 获取用户的未读消息数。
     *
     * @param userId 用户ID。
     * @return 未读消息数 DTO。
     */
    UnreadCountDTO getUnreadMessageCount(Long userId);

    /**
     * 将用户的所有未读消息标记为已读。
     *
     * @param userId The ID of the user.
     */
    void markAllMessagesAsRead(Long userId);

    /**
     * 将用户的单条消息标记为已读。
     *
     * @param userId    用户ID。
     * @param messageId 消息ID。
     */
    void markMessageAsRead(Long userId, Long messageId);

    /**
     * 获取用户的消息列表（带分页和状态过滤）。
     *
     * @param userId         用户ID。
     * @param pageRequestDTO 分页请求 DTO。
     * @param status         消息状态过滤 (例如, "UNREAD", "READ", "ALL")。
     * @return 包含消息列表的 PageFinalResult。
     */
    PageFinalResult<MessageDTO> getUserMessages(Long userId, PageRequestDTO pageRequestDTO, String status);


    void deleteAllMessages(Long userId);

    void deleteMessageById(Long userId, Long messageId);

        void sendCommentReplyNotification(Long replierUserId, Long parentCommentAuthorId, String commentContent, Long videoEpisodesId);
    }
