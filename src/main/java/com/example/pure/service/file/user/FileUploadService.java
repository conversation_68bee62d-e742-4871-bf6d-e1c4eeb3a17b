package com.example.pure.service.file.user;

import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 文件上传服务接口
 * <p>
 * 提供文件上传相关的方法，包括：
 * - 同步文件上传
 * - 异步文件上传
 * - 文件验证和处理
 * </p>
 */
public interface FileUploadService {

    /**
     * 上传文件（同步实现）
     * <p>
     * 同步方式上传文件到服务器，适用于小文件或对响应时间要求不高的场景
     * </p>
     *
     * @param file 上传的文件
     * @param type 文件类型（用于分类存储）
     * @param username 用户名（用于用户隔离）
     * @return 上传结果信息，包含文件路径、大小等
     */
    Map<String, Object> uploadFile(MultipartFile file, String type, String username);

    /**
     * 异步上传文件
     * <p>
     * 异步方式上传文件到服务器，提高服务器性能，适用于大文件上传
     * </p>
     *
     * @param file 上传的文件
     * @param type 文件类型（用于分类存储）
     * @param username 用户名（用于用户隔离）
     * @return 上传结果信息，包含文件路径、大小等
     */
    @Async("ioIntensiveTaskExecutor")
    CompletableFuture<Map<String, Object>> uploadFileAsync(MultipartFile file, String type, String username);

    /**
     * 验证上传文件
     * <p>
     * 验证文件的合法性，包括文件大小、类型、文件名等
     * </p>
     *
     * @param file 待验证的文件
     * @param maxFileSize 最大文件大小限制
     * @throws com.example.pure.exception.BusinessException 如果验证失败
     */
    void validateUploadFile(MultipartFile file, long maxFileSize);

    /**
     * 生成安全的文件名
     * <p>
     * 基于原始文件名生成安全的存储文件名，避免文件名冲突和安全问题
     * </p>
     *
     * @param fileExtension 原始文件名
     * @return 安全的文件名
     */
    String generateSafeFileName(String fileExtension);


    String validateFileType(String fileType,String originalFilename);

    /**
     * 构建文件存储路径
     * <p>
     * 根据用户名和文件类型构建文件存储路径
     * </p>
     *
     * @param username 用户名
     * @param type 文件类型
     * @return 文件存储路径
     */
    String buildStoragePath(String username, String type);

    /**
     * 构建上传结果信息
     * <p>
     * 构建统一的上传结果信息格式
     * </p>
     *
     * @param originalFilename 原始文件名
     * @param safeFileName 安全文件名
     * @param file 上传的文件
     * @param type 文件类型
     * @param targetPath 目标路径
     * @param username 用户名
     * @param status 上传状态（可选）
     * @return 上传结果信息
     */
    Map<String, Object> buildUploadResult(String originalFilename, String safeFileName,
                                         MultipartFile file, String type, String targetPath,
                                         String username, String status);

    /**
     * 上传图片文件并更新用户头像
     * <p>
     * 专门用于图片上传，会自动更新用户头像信息到数据库
     * </p>
     *
     * @param file 上传的图片文件
     * @param type 文件类型
     * @param username 用户名
     * @return 上传结果信息，包含头像URL
     */
    Map<String, Object> uploadImageAndUpdateAvatar(MultipartFile file, String type, String username);

}
