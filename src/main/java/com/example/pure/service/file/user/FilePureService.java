package com.example.pure.service.file.user;


import com.example.pure.model.dto.response.file.user.FileMetadata;
import com.example.pure.model.dto.request.file.user.RangeRequest;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;


/**
 * 文件服务接口
 * <p>
 * 提供文件操作相关的方法，包括：
 * - 视频流式播放
 * - 图片查看
 * - 文件下载
 * </p>
 */
public interface FilePureService {

    /**
     * 验证文件名是否合法
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */

    String getFileExtension(String fileName);

    /**
     * 获取视频流（传统实现）
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含视频流的Resource对象
     * @throws IOException 如果文件访问出错
     */
    ResponseEntity<Resource> streamVideo(String fileName, HttpHeaders headers) throws IOException;



    /**
     * 下载文件（传统实现）
     *
     * @param filename 文件名
     * @param headers  请求头
     * @return 包含文件内容的Resource对象
     * @throws IOException 如果文件访问出错
     */
    ResponseEntity<Resource> downloadFile(String filename, HttpHeaders headers) throws IOException;

    /**
     * 获取文件的MIME类型
     *
     * @param filePath 文件路径
     * @return 文件的MIME类型
     * @throws IOException 如果文件访问出错
     */
    String determineContentType(Path filePath) throws IOException;

    /**
     * 验证文件名是否合法
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */
    boolean isValidFileName(String fileName);

    /**
     * 获取文件元数据信息
     *
     * @param filename 文件名
     * @return 文件元数据，包含内容类型、大小等信息
     * @throws IOException 如果文件访问出错
     */
    FileMetadata getFileMetadata(String filename) throws IOException;

    /**
     * 异步流式传输视频
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行视频流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("ioIntensiveTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> streamVideoAsync(String fileName, HttpHeaders headers);

    /**
     * 异步流式传输文件（通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("ioIntensiveTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers);

    /**
     * 异步流式传输文件（带选项，通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * 支持自定义选项，如缓冲区大小、发送间隔等
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理Range请求和其他HTTP头信息
     * @param options  流传输选项，可包含缓冲区大小、发送间隔等参数
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("ioIntensiveTaskExecutor")
    CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers, java.util.Map<String, Object> options);

    /**
     * 获取文件的安全ContentType
     * <p>
     * 基于文件元数据和文件扩展名确定正确的MIME类型，提供默认值fallback
     * </p>
     *
     * @param filename 文件名
     * @param defaultContentType 默认的ContentType（当无法确定时使用）
     * @return 安全的ContentType字符串
     * @throws IOException 如果文件访问出错
     */
    String getSafeContentType(String filename, String defaultContentType) throws IOException;

    /**
     * 基于已有元数据获取安全ContentType（避免重复调用getFileMetadata）
     * <p>
     * 基于已获取的文件元数据确定正确的MIME类型，提供默认值fallback
     * </p>
     *
     * @param metadata 已获取的文件元数据
     * @param defaultContentType 默认的ContentType（当无法确定时使用）
     * @return 安全的ContentType字符串
     */
    String getSafeContentType(FileMetadata metadata, String defaultContentType);

    /**
     * 构建HTTP响应头信息
     * <p>
     * 根据文件元数据和请求头构建完整的HTTP响应头，包括ContentType、Range支持等
     * </p>
     *
     * @param filename 文件名
     * @param headers 请求头
     * @param defaultContentType 默认ContentType
     * @return 构建好的HttpHeaders对象
     * @throws IOException 如果文件访问出错
     */
    HttpHeaders buildResponseHeaders(String filename, HttpHeaders headers, String defaultContentType) throws IOException;

    /**
     * 基于已有元数据构建HTTP响应头信息（避免重复调用getFileMetadata）
     * <p>
     * 根据已获取的文件元数据和请求头构建完整的HTTP响应头，包括ContentType、Range支持等
     * </p>
     *
     * @param metadata 已获取的文件元数据
     * @param headers 请求头
     * @param defaultContentType 默认ContentType
     * @return 构建好的HttpHeaders对象
     */
    HttpHeaders buildResponseHeaders(FileMetadata metadata, HttpHeaders headers, String defaultContentType);

    /**
     * 构建文件下载的HTTP响应头信息
     * <p>
     * 专门用于文件下载，包括Content-Disposition、ContentType、Range支持等
     * </p>
     *
     * @param filename 文件名
     * @param headers 请求头
     * @return 构建好的HttpHeaders对象，包含下载特有的头信息
     * @throws IOException 如果文件访问出错
     */
    HttpHeaders buildDownloadResponseHeaders(String filename, HttpHeaders headers) throws IOException;

    /**
     * 基于已有元数据构建文件下载的HTTP响应头信息（避免重复调用getFileMetadata）
     * <p>
     * 专门用于文件下载，包括Content-Disposition、ContentType、Range支持等
     * </p>
     *
     * @param metadata 已获取的文件元数据
     * @param headers 请求头
     * @return 构建好的HttpHeaders对象，包含下载特有的头信息
     */
    HttpHeaders buildDownloadResponseHeaders(FileMetadata metadata, HttpHeaders headers);

    /**
     * 解析HTTP Range请求头
     * <p>
     * 解析Range请求头并返回标准化的范围信息，包括验证和调整
     * </p>
     *
     * @param rangeHeader Range请求头的值
     * @param fileSize 文件总大小
     * @return 解析后的范围请求对象
     * @throws IllegalArgumentException 如果Range头格式无效
     */
    RangeRequest parseRangeRequest(String rangeHeader, long fileSize);

    /**
     * 验证并调整Range请求
     * <p>
     * 验证Range请求的有效性，并根据文件大小进行必要的调整
     * </p>
     *
     * @param rangeRequest 原始的范围请求
     * @return 验证和调整后的范围请求
     */
    RangeRequest validateAndAdjustRange(RangeRequest rangeRequest);

    /**
     * 从HTTP请求头中提取并解析Range信息
     * <p>
     * 从HttpHeaders中提取Range头并解析为RangeRequest对象
     * </p>
     *
     * @param headers HTTP请求头
     * @param fileSize 文件总大小
     * @return 解析后的范围请求对象，如果没有Range头则返回完整文件范围
     */
    RangeRequest extractRangeFromHeaders(HttpHeaders headers, long fileSize);


}
