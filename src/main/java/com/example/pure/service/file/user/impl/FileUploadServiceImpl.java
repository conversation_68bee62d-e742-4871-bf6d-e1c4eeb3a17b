package com.example.pure.service.file.user.impl;

import com.example.pure.constant.ResponseCode;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.BusinessException;
import com.example.pure.model.dto.response.file.user.TransferProgress;
import com.example.pure.model.entity.UserProfile;
import com.example.pure.monitor.FileTransferProgressMonitor;
import com.example.pure.monitor.LocalFileTransferProgressMonitor;
import com.example.pure.service.file.user.FilePureService;
import com.example.pure.service.file.user.FileUploadService;
import com.example.pure.service.userprofile.UserProfileService;
import com.example.pure.util.FileTransferUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 文件上传服务实现类
 * <p>
 * 提供文件上传相关操作的实现，包括：
 * - 同步和异步文件上传
 * - 文件验证和安全检查
 * - 文件名处理和路径构建
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl implements FileUploadService {

    private final FilePureService filePureService;
    private final UserProfileService userProfileService;

    @Value("${file.storage.location.upload}")
    private String fileStorageLocation;

    /**
     * 上传文件（同步实现）
     * <p>
     * 同步方式上传文件到服务器，适用于小文件或需要立即获取结果的场景
     * 决策理由：保持原有的同步实现逻辑，优化异常处理和日志记录
     * </p>
     */
    @Override
    public Map<String, Object> uploadFile(MultipartFile file, String type, String username) {
        log.debug("开始同步上传文件: {}, 用户: {}, 类型: {}", file.getOriginalFilename(), username, type);

        // 校验文件类型
        String fileType=validateFileType(type,file.getOriginalFilename());
        // 生成安全文件名
        String safeFileName = generateSafeFileName(fileType);

        try (InputStream inputStream = file.getInputStream()) {
            // 构建存储路径
            Path userRootPath = Paths.get(fileStorageLocation, username, fileType).toAbsolutePath().normalize();
            Files.createDirectories(userRootPath);

            // 创建目标文件路径
            Path targetPath = userRootPath.resolve(safeFileName);

            // 保存文件,Files.copy方法自带8kb内存缓冲方式传输
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);

            log.info("文件同步上传成功: {}, 存储为: {}", file.getOriginalFilename(), safeFileName);

            // 构建响应信息
            return buildUploadResult(file.getOriginalFilename(), safeFileName, file, fileType,
                                   targetPath.toString(), username, "SUCCESS");

        } catch (IOException e) {
            log.error("文件同步上传失败: {}", file.getOriginalFilename(), e);
            throw BusinessException.of(ResponseCode.OPERATION_FAILED, "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 异步上传文件（带进度监控的 Chunked Transfer 实现）
     * <p>
     * 使用@Async注解在指定线程池中异步执行文件上传操作，支持：
     * - Chunked Transfer：分块传输，内存占用可控
     * - 进度监控：实时显示传输进度、速度、预计时间
     * - 性能优化：根据文件大小自动选择缓冲区大小
     * 决策理由：替换Files.copy()为ChunkedFileTransferUtil，提供详细的上传进度信息
     * </p>
     */
    @Override
    @Async("ioIntensiveTaskExecutor")
    public CompletableFuture<Map<String, Object>> uploadFileAsync(MultipartFile file, String type, String username) {
        log.debug("开始异步上传文件（带进度监控）: {}, 用户: {}, 类型: {}", file.getOriginalFilename(), username, type);

        // 校验文件类型
        String fileType=validateFileType(type,file.getOriginalFilename());
        // 生成安全文件名
        String safeFileName = generateSafeFileName(fileType);

        String originalFileName = file.getOriginalFilename();
        long fileSize = file.getSize();

        // 创建进度监控器（在外层作用域，以便在catch块中使用）
        FileTransferProgressMonitor progressMonitor = createProgressMonitor();

        InputStream inputStream = null;
        try {
            // 构建存储路径
            Path userRootPath = Paths.get(fileStorageLocation, username, fileType).toAbsolutePath().normalize();
            Files.createDirectories(userRootPath);
            Path targetPath = userRootPath.resolve(safeFileName);

            // 获取输入流
            inputStream = file.getInputStream();

            // 使用统一的文件传输工具进行带进度监控的文件传输
            // 决策理由：使用统一架构替代Files.copy()，提供实时进度监控和传输速度计算
            // 注意：transferFromStreamToFile内部会自动创建适合的传输配置
            long transferredBytes = FileTransferUtil.transferFromStreamToFile(
                    inputStream,
                    targetPath,
                    originalFileName,
                    fileSize,
                    progressMonitor);

            log.info("文件异步上传完成（带进度监控）: {} -> {}, 实际传输: {} bytes",
                    originalFileName, safeFileName, transferredBytes);

            // 构建成功响应，包含传输统计信息
            Map<String, Object> result = buildUploadResult(originalFileName, safeFileName, file, fileType,
                                                          targetPath.toString(), username, "SUCCESS");
            result.put("message", "文件上传成功");

            // 添加传输统计信息
            if (progressMonitor.getCurrentProgress() != null) {
                result.put("transferStats", buildTransferStats(progressMonitor));
            }

            return CompletableFuture.completedFuture(result);

        } catch (IOException e) {
            log.error("异步文件上传处理失败（带进度监控）: {}", originalFileName, e);

            // 构建失败响应
            Map<String, Object> result = buildUploadResult(originalFileName, safeFileName, file, type,
                                                          null, username, "FAILED");
            result.put("message", "文件上传失败: " + e.getMessage());
            result.put("error", e.getMessage());

            // 添加部分传输统计信息（如果有的话）
            if (progressMonitor.getCurrentProgress() != null) {
                result.put("transferStats", buildTransferStats(progressMonitor));
            }

            return CompletableFuture.completedFuture(result);
        } finally {
            // 确保输入流被关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }

    /**
     * 验证上传文件
     */
    @Override
    public void validateUploadFile(MultipartFile file, long maxFileSize) {
        // 文件为空检查
        if (file.isEmpty()) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "上传失败，文件为空");
        }

        // 文件大小检查
        if (file.getSize() > maxFileSize) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER,
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / (1024 * 1024)));
        }

        // MIME类型检查
        String contentType = file.getContentType();
        if (contentType == null || !SecurityConstants.ALLOWED_MIME_TYPES.contains(contentType.toLowerCase())) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "不支持的文件类型: " + contentType);
        }

        // 获取原始文件名并安全检查
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !filePureService.isValidFileName(originalFilename)) {
            throw BusinessException.of(ResponseCode.INVALID_PARAMETER, "文件名不合法");
        }
    }

    /**
     * 生成安全的文件名
     */
    @Override
    public String generateSafeFileName(String fileExtension) {

        // 生成UUID作为文件名，避免冲突和安全问题
        return UUID.randomUUID().toString() + (fileExtension.isEmpty() ? "" : "." + fileExtension);
    }

    @Override
    public String validateFileType(String fileType, String originalFilename) {

        // 获取文件扩展名
        String fileExtension = filePureService.getFileExtension(originalFilename);
        if (fileType.equals(fileExtension)) {
            return fileType;
        }else{
            return fileExtension;
        }

    }



    /**
     * 构建文件存储路径
     */
    @Override
    public String buildStoragePath(String username, String type) {
        return Paths.get(fileStorageLocation, username, type).toAbsolutePath().normalize().toString();
    }

    /**
     * 构建上传结果信息
     */
    @Override
    public Map<String, Object> buildUploadResult(String originalFilename, String safeFileName,
                                                MultipartFile file, String type, String targetPath,
                                                String username, String status) {
        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("originalFilename", originalFilename);
        fileInfo.put("storedFilename", safeFileName);
        fileInfo.put("size", file.getSize());
        fileInfo.put("type", type);
        fileInfo.put("contentType", file.getContentType());
        fileInfo.put("uploadedBy", username);

        if (targetPath != null) {
            fileInfo.put("path", targetPath);
        }

        if (status != null) {
            fileInfo.put("status", status);
        }

        return fileInfo;
    }

    /**
     * 构建传输统计信息
     * <p>
     * 从进度监控器中提取传输统计信息，用于响应结果
     * </p>
     *
     * @param progressMonitor 进度监控器
     * @return 传输统计信息Map
     */
    private Map<String, Object> buildTransferStats(FileTransferProgressMonitor progressMonitor) {
        Map<String, Object> stats = new HashMap<>();

        if (progressMonitor != null && progressMonitor.getCurrentProgress() != null) {
            var progress = progressMonitor.getCurrentProgress();

            stats.put("totalBytes", progress.getTotalBytes());
            stats.put("transferredBytes", progress.getTransferredBytes());
            stats.put("progressPercent", String.format("%.1f%%", progress.getProgressPercent() * 100));
            stats.put("elapsedTime", progress.getFormattedElapsedTime());
            stats.put("averageSpeed", progress.getFormattedAverageSpeed());
            stats.put("status", progress.getStatus().toString());

            // 如果传输完成，添加最终统计
            if (progress.getStatus() == TransferProgress.TransferStatus.COMPLETED) {
                stats.put("finalSpeed", progress.getFormattedAverageSpeed());
                stats.put("totalTime", progress.getFormattedElapsedTime());
            }
        }

        return stats;
    }



    /**
     * 创建进度监控器
     * <p>
     * 创建适合上传场景的进度监控器
     * </p>
     *
     * @return 进度监控器
     */
    private FileTransferProgressMonitor createProgressMonitor() {
        return new LocalFileTransferProgressMonitor();
    }

    /**
     * 上传图片文件并更新用户头像
     */
    @Override
    public Map<String, Object> uploadImageAndUpdateAvatar(MultipartFile file, String type, String username) {
        log.debug("开始上传图片并更新头像: {}, 用户: {}, 类型: {}", file.getOriginalFilename(), username, type);

        // 先执行普通上传
        Map<String, Object> uploadResult = uploadFile(file, type, username);

        // 检查是否为图片类型，如果是则更新头像
        String fileExtension = filePureService.getFileExtension(file.getOriginalFilename());
        String avatarUrl = null;

        if (SecurityConstants.IMAGE_TYPE.contains(fileExtension)) {
            try {
                UserProfile userProfile = userProfileService.findUserProfileByUsername(username);
                String safeFileName = (String) uploadResult.get("storedFilename");
                avatarUrl = "http://localhost:8080/api/view/images/image-general/" + safeFileName;
                userProfile.setAvatar(avatarUrl);
                userProfileService.updateUserProfileByUserId(userProfile);

                log.info("用户头像更新成功: {}, 头像URL: {}", username, avatarUrl);
            } catch (Exception e) {
                log.error("更新用户头像失败: {}", username, e);
                // 不抛出异常，因为文件已经上传成功
            }
        }

        // 添加头像URL到结果中
        uploadResult.put("avatarUrl", avatarUrl);
        return uploadResult;
    }

}
