package com.example.pure.service.file.server;

import com.example.pure.listener.VideoUploadProgressListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.transfer.s3.S3TransferManager;
import software.amazon.awssdk.transfer.s3.model.CompletedUpload;
import software.amazon.awssdk.transfer.s3.model.Upload;
import software.amazon.awssdk.transfer.s3.model.UploadRequest;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * R2 上传服务
 * <p>
 * 封装了与Cloudflare R2交互的两种核心上传逻辑。
 */
@Slf4j
@Service
public class R2Service {

    // 注入后端直传所需的S3Client

    final private S3Client s3Client;

    // 注入生成预签名URL所需的S3Presigner
    final private S3Presigner s3Presigner;

    // 注入优化配置的TransferManager
    final private S3TransferManager transferManager;

    // 共享的ExecutorService用于AsyncRequestBody
    private static final ExecutorService ASYNC_EXECUTOR = Executors.newCachedThreadPool(r -> {
        Thread t = new Thread(r, "R2Service-AsyncUpload");
        t.setDaemon(true); // 设置为守护线程，避免阻止JVM关闭
        return t;
    });

    // 从配置中注入存储桶名称等参数
    @Value("${cloudflare.r2.bucket}")
    private String bucketName;
    @Value("${cloudflare.r2.public-url}")
    private String publicUrlBase;
    @Value("${cloudflare.r2.presignUpload-duration-minutes}")
    private long presignUploadDuration;
    @Value("${cloudflare.r2.presignDownload-duration-minutes}")
    private long presignDownloadDuration;

    // 上传重试配置
    @Value("${r2.upload.max-retries:3}")
    private int maxUploadRetries;
    @Value("${r2.upload.retry-delay:1000}")
    private long retryDelay;

    @Autowired
    R2Service(S3Client s3Client, S3Presigner s3Presigner, S3TransferManager transferManager) {
        this.s3Client = s3Client;
        this.s3Presigner = s3Presigner;
        this.transferManager = transferManager;
    }





    /**
     * 功能一：生成预签名上传URL (客户端直接上传模式)
     * <p>
     * 这是性能最佳、最安全的方式。后端不接触文件内容，只负责授权。
     * 这张是上传二进制原始数据单文件，所以用binary来上传
     *
     * @param fileName    客户端提供的原始文件名
     * @param contentType 客户端提供的文件MIME类型 (e.g., "image/jpeg")
     * @return 一个有时效性的、可用于HTTP PUT上传的URL
     */
    public String generatePresignedUrl(String fileName, String contentType) {
        // 1. 创建一个标准的PutObjectRequest，描述将要上传的对象信息
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(fileName) // 使用唯一键以防止覆盖
                .contentType(contentType)
                .build();

        // 2. 创建一个预签名请求，设定URL的有效期
        PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(presignUploadDuration))
                .putObjectRequest(objectRequest)
                .build();

                // 3. 使用S3Presigner生成URL并返回其字符串形式
                return s3Presigner.presignPutObject(presignRequest).url().toString();
    }

            /**
             * 生成预签名下载URL (客户端临时访问模式)
             * <p>
             * 用于为存储在R2中的私有文件生成临时访问URL，允许客户端在指定时间内直接访问文件。
             * 这对于需要控制访问权限的文件（如付费内容、私人文件等）非常有用。
             *
             * @param objectKey R2中存储的对象键（文件路径）
             * @return 一个有时效性的、可用于HTTP GET访问的URL
             */
            public String generatePresignedGetUrl(String objectKey) {
                // 1. 创建一个GetObjectRequest，描述要访问的对象信息
                GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                        .bucket(bucketName)
                        .key(objectKey)
                        .build();

                // 2. 创建一个预签名请求，设定URL的有效期
                GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                        .signatureDuration(Duration.ofMinutes(presignDownloadDuration))
                        .getObjectRequest(getObjectRequest)
                        .build();

                // 3. 使用S3Presigner生成URL并返回其字符串形式
                return s3Presigner.presignGetObject(presignRequest).url().toString();
            }


    /**
     * 功能二：从服务器中转上传文件 (后端处理模式)
     * <p>
     * 适用于文件需要经过后端处理（如加水印、病毒扫描、内容修改）的场景。
     * 最佳实践是使用流(InputStream)来处理，以避免将大文件完全加载到内存中。
     *
     * 流式上传适合小于8MB的文件，性能损耗小，没有断点续传功能和进度功能
     * @param file 从Controller层接收到的MultipartFile对象
     * @return 文件成功上传到R2后的公开访问URL
     * @throws IOException 当从MultipartFile获取输入流失败时抛出
     */
    public String uploadFileFromServer(MultipartFile file, String folderName) throws IOException {
        // 为上传到R2的对象生成一个唯一的键（上传文件名称.类型）
        final String objectKeyName = generateUniqueKey(file.getOriginalFilename());

        // 准备上传请求对象
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(folderName + "/" + objectKeyName)
                .contentType(file.getContentType())
                .build();

        // 按照您的要求，我们使用 try-finally 结构来手动管理资源。
        final InputStream inputStream = file.getInputStream();
        try {
            // 在这里，您可以对inputStream进行包装以实现流式修改。
            // 例如: InputStream modifiedStream = new MyWatermarkingInputStream(inputStream);
            // 然后将 modifiedStream 传递给下面的 RequestBody。
            // 为简单起见，我们直接上传原始流。

            // s3Client.putObject是阻塞方法，它会读取流中的所有数据并完成上传。
            // RequestBody.fromInputStream是最高效的方式，它直接将输入流对接到底层HTTP客户端，
            // 避免了内存中的大量缓冲。
            s3Client.putObject(objectRequest, RequestBody.fromInputStream(inputStream, file.getSize()));
        } finally {
            // finally块确保无论try块中是否发生异常，这个关闭操作都会被执行。
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    // 记录关闭流时发生的次要异常，但不影响主流程
                    // 在生产环境中，这里应该使用日志框架，例如：
                    // log.warn("Failed to close upload input stream", e);
                    e.printStackTrace();
                }
            }
        }

        // 上传成功后，拼接并返回公开可访问的URL
        return publicUrlBase + "/" + folderName + "/" + objectKeyName;
    }

    /**
     * 内部辅助方法：为上传的文件生成一个唯一的键（文件名）
     *
     * @param originalFilename 原始文件名
     * @return "UUID-原始文件名" 格式的唯一键
     */
    private String generateUniqueKey(String originalFilename) {
        // \\在java里表示正则表达式\s替换所有空格成_
        return UUID.randomUUID().toString() + "-" + (originalFilename != null ? originalFilename.replaceAll("\\s+", "_") : "file");
    }
    /**
     * 通用文件上传方法 - 支持任意文件类型和文件夹结构
     * <p>
     * 统一的文件上传接口，支持自定义文件夹、文件名和扩展名
     * </p>
     *
     * @param file 要上传的文件 (MultipartFile)
     * @param folderType 文件类型文件夹 (如: "video", "image", "music", "document")
     * @param uuid 唯一标识符，作为子文件夹
     * @param fileName 文件名（不含扩展名）
     * @return 文件上传后的 objectKey
     * @throws IOException IO异常
     */
    public String uploadAsset(MultipartFile file, String folderType, String uuid, String fileName) throws IOException {
        // 1. 从原始文件名中获取文件扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);

        // 2. 构建在R2中存储的对象键 (Object Key)
        // 格式: folderType/uuid/fileName.extension
        String objectKey = folderType + "/" + uuid + "/" + fileName + extension;

        log.info("开始上传{}文件 - objectKey: {}, 文件大小: {} KB",
                folderType, objectKey, String.format("%.2f", file.getSize() / 1024.0));

        // 3. 创建S3上传请求
        PutObjectRequest objectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .contentType(file.getContentType())
                .build();

        // 4. 执行上传 (使用流式上传以提高效率)
        final InputStream inputStream = file.getInputStream();
        try {
            long startTime = System.currentTimeMillis();
            s3Client.putObject(objectRequest, RequestBody.fromInputStream(inputStream, file.getSize()));
            long uploadTime = System.currentTimeMillis() - startTime;

            log.info("{}文件上传成功 - objectKey: {}, 耗时: {}ms",
                    folderType, objectKey, uploadTime);
        } finally {
            // 确保输入流被关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭{}文件输入流失败 - objectKey: {}", folderType, objectKey, e);
                }
            }
        }

        return objectKey;
    }

    /**
     * 上传视频相关文件（如雪碧图）到指定的UUID和剧集编号路径下
     * <p>
     * 兼容性方法，内部调用通用上传方法
     * </p>
     *
     * @param file 要上传的文件 (MultipartFile)
     * @param uuid 视频级的唯一标识符，作为父文件夹
     * @param episodeNumber 剧集编号，作为文件名（不含扩展名）
     * @return 文件上传后的 objectKey
     * @throws IOException IO异常
     * @deprecated 推荐使用 {@link #uploadAsset(MultipartFile, String, String, String)} 代替，
     *             例如：uploadAsset(file, "video", uuid, episodeNumber)
     */
    @Deprecated
    public String uploadVideoAsset(MultipartFile file, String uuid, String episodeNumber) throws IOException {
        return uploadAsset(file, "video", uuid, episodeNumber);
    }


    /**
     * 通用流式上传方法 - 使用AWS TransferManager自动优化
     * <p>
     * 完全依赖AWS TransferManager的自动优化策略，支持任意文件类型：
     * </p>
     *
     * <h3>TransferManager自动优化：</h3>
     * <ul>
     *   <li><b>智能阈值</b>：文件≥8MB自动启用分片上传</li>
     *   <li><b>自适应策略</b>：根据文件大小、网络条件自动选择最优方案，可以实现上传进度条功能</li>
     *   <li><b>并行传输</b>：自动并行上传多个分片，最大化性能</li>
     *   <li><b>断点续传</b>：网络中断后自动从断点继续</li>
     *   <li><b>内存优化</b>：流式处理，避免大文件内存溢出</li>
     *   <li><b>网络自适应</b>：动态调整传输策略适应网络状况</li>
     * </ul>
     *
     * @param inputStream 文件的输入流
     * @param contentLength 文件内容长度（-1表示未知长度）
     * @param folderType 文件类型文件夹 (如: "video", "image", "music", "document")
     * @param uuid 唯一标识符，作为子文件夹
     * @param fileName 文件名（不含扩展名）
     * @param fileExtension 文件扩展名 (如: ".mp4", ".jpg", ".mp3")
     * @param contentType 文件的MIME类型
     * @return 文件上传后的objectKey
     * @throws IOException IO异常
     */
    public String uploadAssetFromStream(InputStream inputStream, long contentLength,
                                      String folderType, String uuid, String fileName,
                                      String fileExtension, String contentType) throws IOException {

        // 直接使用TransferManager，让它自动处理所有优化决策
        if (contentLength > 0) {
            log.info("开始上传{}文件（{} MB） - TransferManager自动优化策略",
                    folderType, String.format("%.2f", contentLength / (1024.0 * 1024.0)));
        } else {
            log.info("开始上传{}文件（大小未知） - TransferManager自动优化策略", folderType);
        }

        return uploadAssetWithTransferManager(inputStream, contentLength, folderType, uuid,
                                            fileName, fileExtension, contentType);
    }

    /**
     * 智能流式上传视频文件 - 使用AWS TransferManager自动优化
     * <p>
     * 兼容性方法，内部调用通用流式上传方法
     * </p>
     *
     * @param inputStream 视频文件的输入流
     * @param contentLength 文件内容长度（-1表示未知长度）
     * @param uuid 视频级的唯一标识符，作为父文件夹
     * @param episodeNumber 剧集编号，作为文件名（不含扩展名）
     * @param contentType 文件的MIME类型
     * @return 文件上传后的objectKey
     * @throws IOException IO异常
     * @deprecated 推荐使用 {@link #uploadAssetFromStream(InputStream, long, String, String, String, String, String)} 代替，
     *             例如：uploadAssetFromStream(inputStream, contentLength, "video", uuid, episodeNumber, ".mp4", contentType)
     */
    @Deprecated
    public String uploadVideoAssetFromStream(InputStream inputStream, long contentLength,
                                           String uuid, String episodeNumber, String contentType) throws IOException {
        return uploadAssetFromStream(inputStream, contentLength, "video", uuid,
                                   episodeNumber, ".mp4", contentType);
    }

    /**
     * 通用AWS TransferManager自动优化上传 - 生产级最佳实践
     * <p>
     * 使用AWS TransferManager的完全自动化优化策略，支持任意文件类型：
     * </p>
     *
     * <h3>自动优化特性：</h3>
     * <ul>
     *   <li><b>智能阈值</b>：文件≥8MB自动启用分片上传，<8MB使用单次上传</li>
     *   <li><b>自适应分片</b>：根据文件大小自动选择最优分片大小</li>
     *   <li><b>并行传输</b>：自动并行上传多个分片，最大化带宽利用</li>
     *   <li><b>断点续传</b>：网络中断后自动从断点继续，无需重新开始</li>
     *   <li><b>进度监控</b>：实时监控上传进度和传输速度</li>
     *   <li><b>资源管理</b>：自动管理连接池、线程池和内存使用</li>
     *   <li><b>网络自适应</b>：根据网络状况动态调整传输策略</li>
     * </ul>
     *
     * @param inputStream 文件的输入流
     * @param contentLength 文件内容长度
     * @param folderType 文件类型文件夹 (如: "video", "image", "music", "document")
     * @param uuid 唯一标识符，作为子文件夹
     * @param fileName 文件名（不含扩展名）
     * @param fileExtension 文件扩展名 (如: ".mp4", ".jpg", ".mp3")
     * @param contentType 文件的MIME类型
     * @return 文件上传后的objectKey
     * @throws IOException IO异常
     */
    public String uploadAssetWithTransferManager(InputStream inputStream, long contentLength,
                                               String folderType, String uuid, String fileName,
                                               String fileExtension, String contentType) throws IOException {
        // 1. 构建在R2中存储的对象键 (Object Key)
        String objectKey = folderType + "/" + uuid + "/" + fileName + fileExtension;

        Exception lastException = null;

        // 2. 内置重试逻辑
        for (int attempt = 1; attempt <= maxUploadRetries; attempt++) {
            try {
                if (attempt == 1) {
                    log.info("开始TransferManager自动优化上传{}文件 - objectKey: {}, 文件大小: {} MB (≥8MB自动分片)",
                            folderType, objectKey, String.format("%.2f", contentLength / (1024.0 * 1024.0)));
                } else {
                    log.info("重试TransferManager上传{}文件 - 第{}次尝试, objectKey: {}",
                            folderType, attempt, objectKey);
                }

                long startTime = System.currentTimeMillis();

                // 3. 创建上传请求 - 使用自定义进度监听器
                UploadRequest uploadRequest = UploadRequest.builder()
                        .putObjectRequest(PutObjectRequest.builder()
                                .bucket(bucketName)
                                .key(objectKey)
                                .contentType(contentType)
                                .contentLength(contentLength)
                                .build())
                        .requestBody(AsyncRequestBody.fromInputStream(inputStream, contentLength, ASYNC_EXECUTOR))
                        .addTransferListener(VideoUploadProgressListener.create(objectKey, contentLength)) // 使用自定义进度监听器
                        .build();

                // 4. 执行分片上传
                Upload upload = transferManager.upload(uploadRequest);

                // 5. 等待上传完成
                CompletedUpload completedUpload = upload.completionFuture().join();

                long uploadTime = System.currentTimeMillis() - startTime;
                double speedMBps = (contentLength / (1024.0 * 1024.0)) / (uploadTime / 1000.0);

                log.info("TransferManager自动优化上传{}文件成功 - objectKey: {}, 耗时: {}ms, 平均速度: {} MB/s, ETag: {}",
                        folderType, objectKey, uploadTime, String.format("%.2f", speedMBps), completedUpload.response().eTag());

                return objectKey; // 上传成功，返回结果

            } catch (Exception e) {
                lastException = e;

                // 判断是否为可重试的错误
                if (isRetryableException(e)) {
                    log.warn("TransferManager上传{}文件失败 - 第{}次尝试, objectKey: {}, 错误: {}",
                            folderType, attempt, objectKey, e.getMessage());

                    if (attempt < maxUploadRetries) {
                        try {
                            // 指数退避重试策略
                            long waitTime = retryDelay * (long) Math.pow(2, attempt - 1);
                            log.info("等待{}ms后重试上传{}文件...", waitTime, folderType);
                            Thread.sleep(waitTime);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new IOException("上传重试被中断", ie);
                        }
                    } else {
                        log.error("TransferManager上传{}文件失败 - 已重试{}次, objectKey: {}, 最终错误: {}",
                                folderType, maxUploadRetries, objectKey, e.getMessage(), e);
                        throw new IOException("TransferManager上传" + folderType + "文件到R2失败，已重试" + maxUploadRetries + "次: " + e.getMessage(), e);
                    }
                } else {
                    // 不可重试的错误，立即失败
                    log.error("TransferManager上传{}文件失败 - 不可重试错误, objectKey: {}, 错误: {}",
                            folderType, objectKey, e.getMessage(), e);
                    throw new IOException("TransferManager上传" + folderType + "文件到R2失败（不可重试）: " + e.getMessage(), e);
                }
            }
        }

        // 理论上不会到达这里
        throw new IOException("TransferManager上传" + folderType + "文件到R2失败，已重试" + maxUploadRetries + "次", lastException);
    }

    /**
     * 判断异常是否可重试
     * <p>
     * 根据异常类型和错误信息判断是否应该重试上传
     * </p>
     *
     * @param exception 发生的异常
     * @return true 如果可以重试，false 如果不应该重试
     */
    private boolean isRetryableException(Exception exception) {
        if (exception == null) {
            return false;
        }

        String message = exception.getMessage();
        if (message == null) {
            message = "";
        }
        message = message.toLowerCase();

        // 可重试的错误类型
        // 1. 网络相关错误
        if (message.contains("timeout") ||
            message.contains("connection") ||
            message.contains("network") ||
            message.contains("socket")) {
            return true;
        }

        // 2. 服务器临时错误
        if (message.contains("service unavailable") ||
            message.contains("internal server error") ||
            message.contains("502") || message.contains("503") || message.contains("504")) {
            return true;
        }

        // 3. 限流错误
        if (message.contains("throttl") ||
            message.contains("rate limit") ||
            message.contains("too many requests") ||
            message.contains("429")) {
            return true;
        }

        // 4. AWS SDK 特定的可重试异常
        if (exception instanceof software.amazon.awssdk.core.exception.SdkException) {
            // AWS SDK 的可重试异常通常包含这些关键词
            if (message.contains("retryable") || message.contains("throttle")) {
                return true;
            }
        }

        // 不可重试的错误类型
        // 1. 认证和权限错误
        if (message.contains("access denied") ||
            message.contains("unauthorized") ||
            message.contains("forbidden") ||
            message.contains("401") || message.contains("403")) {
            return false;
        }

        // 2. 客户端错误
        if (message.contains("bad request") ||
            message.contains("invalid") ||
            message.contains("malformed") ||
            message.contains("400")) {
            return false;
        }

        // 3. 文件相关错误
        if (message.contains("file not found") ||
            message.contains("no such file")) {
            return false;
        }

        // 默认情况下，对于未知错误，采用保守策略：重试
        // 这样可以最大化成功率，避免因为未识别的临时错误导致失败
        log.debug("未识别的异常类型，采用重试策略: {}", message);
        return true;
    }

    /**
     * AWS TransferManager自动优化上传视频文件 - 生产级最佳实践
     * <p>
     * 兼容性方法，内部调用通用TransferManager上传方法
     * </p>
     *
     * @param inputStream 视频文件的输入流
     * @param contentLength 文件内容长度
     * @param uuid 视频级的唯一标识符，作为父文件夹
     * @param episodeNumber 剧集编号，作为文件名（不含扩展名）
     * @param contentType 文件的MIME类型
     * @return 文件上传后的objectKey
     * @throws IOException IO异常
     * @deprecated 推荐使用 {@link #uploadAssetWithTransferManager(InputStream, long, String, String, String, String, String)} 代替，
     *             例如：uploadAssetWithTransferManager(inputStream, contentLength, "video", uuid, episodeNumber, ".mp4", contentType)
     */
    @Deprecated
    public String uploadVideoAssetWithTransferManager(InputStream inputStream, long contentLength,
                                                    String uuid, String episodeNumber, String contentType) throws IOException {
        return uploadAssetWithTransferManager(inputStream, contentLength, "video", uuid,
                                            episodeNumber, ".mp4", contentType != null ? contentType : "video/mp4");
    }

    /**
     * 内部辅助方法：从文件名中提取扩展名
     * @param filename 原始文件名
     * @return 文件扩展名 (e.g., ".jpg")
     */
    private String getFileExtension(String filename) {
        if (filename != null && filename.contains(".")) {
            return filename.substring(filename.lastIndexOf('.'));
        }
        return ""; // 如果没有扩展名，返回空字符串
    }

    /**
     * 列出所有存储桶
     */
    public List<Bucket> listBuckets() {
        try {
            return s3Client.listBuckets().buckets();
        } catch (S3Exception e) {
            throw new RuntimeException("Failed to list buckets: " + e.getMessage(), e);
        }
    }

    /*
     * 列出存储桶中的所有对象
     */
    public List<S3Object> listObjects(String bucketName) {
        try {
            ListObjectsV2Request request = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .build();

            return s3Client.listObjectsV2(request).contents();
        } catch (S3Exception e) {
            throw new RuntimeException("Failed to list objects in bucket " + bucketName + ": " + e.getMessage(), e);
        }
    }
}
