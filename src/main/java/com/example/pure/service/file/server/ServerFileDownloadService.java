package com.example.pure.service.file.server;

import java.io.IOException;
import java.nio.file.Path;

/**
 * 服务器端文件下载服务接口
 * <p>
 * 提供服务器端从远程URL下载文件到本地的功能，支持流式下载、重试机制等特性
 * </p>
 */
public interface ServerFileDownloadService {

    /**
     * 下载文件到指定路径
     * <p>
     * 使用流式下载，支持大文件，内存占用恒定
     * </p>
     *
     * @param url 远程文件URL
     * @param targetFile 本地目标文件路径
     * @return 下载的文件大小（字节）
     * @throws IOException 下载或写入文件时发生错误
     */
    long downloadToFile(String url, Path targetFile) throws IOException;

    /**
     * 带重试机制的文件下载
     * <p>
     * 支持下载失败时的自动重试，使用指数退避策略
     * </p>
     *
     * @param url 远程文件URL
     * @param targetFile 本地目标文件路径
     * @param maxRetries 最大重试次数
     * @return 下载的文件大小（字节）
     * @throws IOException 下载失败时抛出
     */
    long downloadWithRetry(String url, Path targetFile, int maxRetries) throws IOException;

    /**
     * 下载并上传文件的完整流程
     * <p>
     * 从远程URL下载文件到临时位置，然后上传到R2存储，最后清理临时文件
     * </p>
     *
     * @param url 远程文件URL
     * @param folderType 文件类型文件夹 (如: "video", "image", "music")
     * @param uuid 唯一标识符，作为子文件夹
     * @param fileName 文件名（不含扩展名）
     * @param fileExtension 文件扩展名 (如: ".mp4", ".jpg")
     * @param contentType 文件的MIME类型
     * @return R2存储中的objectKey
     * @throws IOException 下载或上传失败时抛出
     */
    String downloadAndUpload(String url, String folderType, String uuid,
                           String fileName, String fileExtension, String contentType) throws IOException;

    /**
     * 清理临时文件
     * <p>
     * 安全删除临时文件，防止磁盘空间浪费
     * </p>
     *
     * @param tempFile 临时文件路径
     * @param reason 清理原因（用于日志）
     */
    void cleanupTempFile(Path tempFile, String reason);
}
