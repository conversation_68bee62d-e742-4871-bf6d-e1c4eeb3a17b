package com.example.pure.service.file.server.impl;

import com.example.pure.service.file.server.R2Service;
import com.example.pure.service.file.server.ServerFileDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.time.Duration;

/**
 * 服务器端文件下载服务实现
 * <p>
 * 提供高性能的流式文件下载功能，支持大文件下载、重试机制、错误处理等特性
 * </p>
 */
@Slf4j
@Service
public class ServerFileDownloadServiceImpl implements ServerFileDownloadService {

    private final WebClient webClient;
    private final R2Service r2Service;

    // 下载重试配置
    @Value("${file.download.max-retries:3}")
    private int maxDownloadRetries;

    @Value("${file.download.retry-delay:1000}")
    private long downloadRetryDelay;

    @Autowired
    public ServerFileDownloadServiceImpl(WebClient webClient, R2Service r2Service) {
        this.webClient = webClient;
        this.r2Service = r2Service;
    }

    /**
     * 优化的流式下载文件到本地
     * <p>
     * 核心特性：
     * </p>
     * <ul>
     *   <li><b>真正的流式处理</b>：使用WebClient + Reactive Streams，内存占用恒定</li>
     *   <li><b>高性能传输</b>：直接从网络流写入文件，避免内存缓冲</li>
     *   <li><b>自动资源管理</b>：DataBufferUtils自动管理引用计数和释放</li>
     *   <li><b>背压控制</b>：防止内存溢出，适合大文件下载</li>
     *   <li><b>异常安全</b>：确保资源正确清理</li>
     *   <li><b>超时控制</b>：15分钟超时，适合大文件传输</li>
     * </ul>
     *
     * @param url 要下载的文件URL
     * @param targetFile 本地目标文件路径
     * @return 下载的文件大小（字节）
     * @throws IOException 下载或写入文件时发生错误
     */
    @Override
    public long downloadToFile(String url, Path targetFile) throws IOException {
        try {
            log.debug("开始流式下载文件: {} -> {}", url, targetFile.getFileName());

            // 创建响应式数据流 - 这是真正的流式处理核心
            Flux<DataBuffer> dataBufferFlux = webClient.get()
                    .uri(url)
                    .accept(MediaType.APPLICATION_OCTET_STREAM) // 明确接受二进制数据
                    .retrieve()
                    // bodyToFlux处理的是单个响应体多个元素,流式下载，bodyToMono()为一次性把下载文件加载到内存适合小文件
                    .bodyToFlux(DataBuffer.class) // 转换为DataBuffer流，逐块接收数据
                    .timeout(Duration.ofMinutes(15)) // 15分钟超时，适合大文件下载
                    .doOnNext(buffer -> log.trace("接收数据块: {} bytes", buffer.readableByteCount())) // 记录每个数据块
                    .doOnError(error -> log.error("下载流发生错误: {}", error.getMessage())); // 错误日志

            /*
             订阅流,从这开始download
             使用Spring官方安全写入方法 - 关键改进点
             DataBufferUtils.write() 的优势：
             1. 自动管理DataBuffer的引用计数和释放
             2. 支持背压控制，防止内存溢出
             3. 异常安全，确保资源正确清理
             4. 真正的流式处理，内存占用恒定*/
            return DataBufferUtils.write(dataBufferFlux, targetFile,
                    StandardOpenOption.CREATE, StandardOpenOption.WRITE) //第三个参数为penOption...，会把3和4的参数打包进数组，同时执行任务
                    .doOnSuccess(path -> log.debug("文件写入完成: {}", targetFile.getFileName()))
                    .then(Mono.fromCallable(() -> Files.size(targetFile))) // 返回文件大小
                    .block(); // 阻塞等待完成，返回最终结果

        } catch (Exception e) {
            // 异常处理：清理失败的文件，防止磁盘空间浪费
            cleanupFailedDownload(targetFile);
            throw new IOException("下载文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带重试机制的文件下载
     * <p>
     * 智能重试策略：
     * </p>
     * <ul>
     *   <li><b>指数退避</b>：重试间隔逐渐增加，减少服务器压力</li>
     *   <li><b>部分文件保留</b>：类似专业下载工具，保留已下载部分</li>
     *   <li><b>断点续传支持</b>：检测部分下载文件并尝试继续</li>
     *   <li><b>详细日志</b>：记录每次重试的详细信息</li>
     * </ul>
     *
     * @param url 远程文件URL
     * @param targetFile 本地目标文件路径
     * @param maxRetries 最大重试次数
     * @return 下载的文件大小（字节）
     * @throws IOException 下载失败时抛出
     */
    @Override
    public long downloadWithRetry(String url, Path targetFile, int maxRetries) throws IOException {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("开始下载文件，第{}次尝试 - URL: {}", attempt, url);

                // 检查是否有部分下载的文件
                if (attempt > 1 && Files.exists(targetFile)) {
                    long partialSize = Files.size(targetFile);
                    log.info("发现部分下载文件，大小: {} MB，尝试重新连接继续下载...",
                            String.format("%.2f", partialSize / (1024.0 * 1024.0)));
                }

                // 执行下载
                long downloadedBytes = downloadToFile(url, targetFile);
                log.info("下载成功 - 文件大小: {} MB, 文件: {}",
                        String.format("%.2f", downloadedBytes / (1024.0 * 1024.0)), targetFile.getFileName());
                return downloadedBytes; // 下载成功，返回结果

            } catch (Exception downloadException) {
                lastException = downloadException;
                log.warn("第{}次下载尝试失败, 错误: {}", attempt, downloadException.getMessage());

                // 检查部分下载的文件大小（用于诊断）
                if (Files.exists(targetFile)) {
                    try {
                        long partialSize = Files.size(targetFile);
                        log.debug("部分下载文件大小: {} MB", String.format("%.2f", partialSize / (1024.0 * 1024.0)));
                    } catch (IOException e) {
                        log.debug("无法获取部分下载文件大小: {}", e.getMessage());
                    }
                }

                if (attempt < maxRetries) {
                    try {
                        // 指数退避重试策略
                        long waitTime = downloadRetryDelay * (long) Math.pow(2, attempt - 1);
                        log.info("等待{}ms后重新连接重试下载（保留已下载部分）...", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("下载重试被中断", ie);
                    }
                } else {
                    // 下载失败，删除临时文件并抛出错误
                    cleanupTempFile(targetFile, "下载" + maxRetries + "次失败");
                    throw new IOException("下载失败，已重试" + maxRetries + "次", lastException);
                }
            }
        }

        // 理论上不会到达这里
        throw new IOException("下载失败，已重试" + maxRetries + "次", lastException);
    }

    /**
     * 下载并上传文件的完整流程
     * <p>
     * 高级功能：集成下载和上传的完整工作流
     * </p>
     * <ul>
     *   <li><b>智能重试</b>：下载和上传都支持重试机制</li>
     *   <li><b>资源管理</b>：自动清理临时文件</li>
     *   <li><b>性能优化</b>：流式处理，支持大文件</li>
     *   <li><b>错误恢复</b>：详细的错误处理和诊断信息</li>
     * </ul>
     *
     * @param url 远程文件URL
     * @param folderType 文件类型文件夹 (如: "video", "image", "music")
     * @param uuid 唯一标识符，作为子文件夹
     * @param fileName 文件名（不含扩展名）
     * @param fileExtension 文件扩展名 (如: ".mp4", ".jpg")
     * @param contentType 文件的MIME类型
     * @return R2存储中的objectKey
     * @throws IOException 下载或上传失败时抛出
     */
    @Override
    public String downloadAndUpload(String url, String folderType, String uuid,
                                  String fileName, String fileExtension, String contentType) throws IOException {
        Path tempFile = null;

        try {
            // 1. 创建唯一临时文件
            String uniqueFileName = String.format("%s_%s_%s_%d_%d",
                folderType, uuid, fileName, Thread.currentThread().getId(), System.currentTimeMillis());
            tempFile = Files.createTempFile(uniqueFileName + "_", fileExtension);
            log.debug("创建唯一临时文件: {}", tempFile.getFileName());

            // 2. 下载文件（带重试机制）
            long downloadedBytes = downloadWithRetry(url, tempFile, maxDownloadRetries);

            // 3. 上传文件（R2Service内置重试机制）
            log.info("开始上传{}文件到R2 - 临时文件: {}", folderType, tempFile.getFileName());

            String objectKey;
            InputStream fileInputStream = null;
            try {
                fileInputStream = Files.newInputStream(tempFile);
                // 使用通用方法上传（内置重试机制）
                objectKey = r2Service.uploadAssetFromStream(
                    fileInputStream, downloadedBytes, folderType, uuid, fileName, fileExtension, contentType);
            } finally {
                // 确保输入流被关闭
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close();
                    } catch (IOException e) {
                        log.warn("关闭{}文件输入流失败 - 文件: {}", folderType, tempFile.getFileName(), e);
                    }
                }
            }

            log.info("{}文件上传成功 - objectKey: {}", folderType, objectKey);
            return objectKey;

        } finally {
            // 清理临时文件
            cleanupTempFile(tempFile, "处理完成");
        }
    }

    /**
     * 清理临时文件的辅助方法
     * <p>
     * 安全删除临时文件，防止磁盘空间浪费和文件系统污染
     * </p>
     *
     * @param tempFile 临时文件路径
     * @param reason 清理原因（用于日志）
     */
    @Override
    public void cleanupTempFile(Path tempFile, String reason) {
        if (tempFile != null && Files.exists(tempFile)) {
            try {
                Files.delete(tempFile);
                log.debug("已删除临时文件: {} (原因: {})", tempFile.getFileName(), reason);
            } catch (IOException e) {
                log.warn("删除临时文件失败: {} (原因: {}), 错误: {}", tempFile.getFileName(), reason, e.getMessage());
            }
        }
    }

    /**
     * 清理下载失败的临时文件
     * <p>
     * 当下载过程中发生异常时，清理可能已创建的部分文件，
     * 防止磁盘空间浪费和文件系统污染
     * </p>
     *
     * <h3>清理策略：</h3>
     * <ul>
     *   <li><b>安全检查</b>：先检查文件是否存在，避免不必要的操作</li>
     *   <li><b>异常容错</b>：删除失败时只记录警告，不影响主流程</li>
     *   <li><b>资源释放</b>：确保临时文件不会占用磁盘空间</li>
     * </ul>
     *
     * @param targetFile 需要清理的文件路径
     */
    private void cleanupFailedDownload(Path targetFile) {
        if (Files.exists(targetFile)) {
            try {
                Files.delete(targetFile);
                log.debug("已清理失败的下载文件: {}", targetFile.getFileName());
            } catch (IOException e) {
                // 删除失败时只记录警告，不抛出异常，避免掩盖原始错误
                log.warn("清理失败的下载文件时出错: {}", targetFile.getFileName(), e);
            }
        }
    }
}
