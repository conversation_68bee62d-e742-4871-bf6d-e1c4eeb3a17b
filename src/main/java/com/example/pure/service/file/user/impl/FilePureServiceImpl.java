package com.example.pure.service.file.user.impl;

import com.example.pure.model.dto.response.file.user.FileMetadata;
import com.example.pure.model.dto.request.file.user.RangeRequest;
import com.example.pure.monitor.FileTransferProgressMonitor;
import com.example.pure.monitor.LocalFileTransferProgressMonitor;
import com.example.pure.service.file.user.FilePureService;
import com.example.pure.transfer.TransferConfig;
import com.example.pure.util.FileTransferUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.UrlResource;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.File;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件服务实现类
 * <p>
 * 提供文件相关操作的实现，包括：
 * - 视频流式播放
 * - 文件下载
 * - 文件类型判断
 * - 文件名安全验证
 * </p>
 */
@Slf4j
@Service
public class FilePureServiceImpl implements FilePureService {

    /**
     * 文件存储根目录
     */
    @Value("${file.storage.location.upload}")
    private  String fileStorageLocationStr;

    private final ResourceLoader resourceLoader;

    /**
     * 允许下载的文件扩展名白名单 (安全检查)
     */
    private static final List<String> ALLOWED_EXTENSIONS =
            Arrays.asList("pdf", "txt", "docx", "xlsx", "pptx", "zip", "rar", "exe");





    /**
     * 构造函数，初始化文件存储根目录
     *
     *
     */
    public FilePureServiceImpl(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 动态构建用户特定的文件路径
     *
     * @param fileName 文件名
     * @return 用户特定的文件路径
     * @throws IOException 如果无法获取用户名或构建路径时
     */
    private Path getUserSpecificFilePath(String fileName) throws IOException {




        // 存放位置按文件类型存放，所以先获取文件类型再合并文件名
        String fileType=fileName.substring( fileName.lastIndexOf(".")+1);

        // 创建存放文件路径
        Path filePath =Paths.get(fileStorageLocationStr,fileType).toAbsolutePath().normalize();
        Path fullFilePath=filePath.resolve(fileName);


        // 确保用户目录存在，如果不存在则创建
        try {
            Files.createDirectories(filePath);
        } catch (IOException e) {
            log.error("无法创建用户目录: {}", filePath, e);
            throw new IOException("无法创建用户目录: " + filePath, e);
        }

        return fullFilePath;
    }

    /**
     * 获取视频流
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含视频流的ResponseEntity
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> streamVideo(String fileName, HttpHeaders headers) throws IOException {
        // 安全性检查：防止目录遍历攻击
        if (!isValidFileName(fileName)) {
            throw new IOException("无效的文件名: " + fileName + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        try {
            // 动态构建并验证用户特定的文件路径
            Path filePath = getUserSpecificFilePath(fileName);

            // 使用 ResourceLoader 从文件系统加载视频文件。这比直接使用UrlResource更灵活
            Resource video = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在且可读
            if (!video.exists()) {
                log.warn("请求的文件不存在: {}", filePath);
                throw new IOException("文件不存在: " + fileName);
            }

            if (!video.isReadable()) {
                log.warn("请求的文件不可读: {}", filePath);
                throw new IOException("无法读取文件: " + fileName + "，请检查文件权限");
            }

            // 获取文件类型
            String contentType = determineContentType(filePath);

            // 使用统一的Range处理方法
            long fileSize = video.contentLength();
            RangeRequest rangeRequest;

            try {
                rangeRequest = extractRangeFromHeaders(headers, fileSize);
            } catch (IllegalArgumentException e) {
                log.warn("Range请求处理失败: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE)
                        .header(HttpHeaders.CONTENT_RANGE, "bytes */" + fileSize)
                        .build();
            }

            // 使用健壮的RangeResource来包装原始视频资源和请求的范围，Resource(Auto)和streamFileAsync()一样缓冲分块返回
            // RangeResource 确保了即使流被多次读取（例如，被框架检查元数据后再次发送），
            // 每次都能提供一个全新的、正确的范围流，避免了因 InputStream 只能读取一次而导致的问题。
            // 设置响应头
            HttpStatus status = rangeRequest.isRangeRequest() ? HttpStatus.PARTIAL_CONTENT : HttpStatus.OK;
            ResponseEntity.BodyBuilder responseBuilder = ResponseEntity.status(status)
                    .header(HttpHeaders.CONTENT_TYPE, contentType)
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(rangeRequest.getContentLength()))
                    .header(HttpHeaders.ACCEPT_RANGES, "bytes");

            // 如果是范围请求，添加Content-Range头
            if (rangeRequest.isRangeRequest()) {
                responseBuilder.header(HttpHeaders.CONTENT_RANGE, rangeRequest.getContentRangeHeader());
            }

            return responseBuilder.body(new RangeResource(video, rangeRequest.getRangeStart(), rangeRequest.getContentLength()));

        } catch (MalformedURLException e) {
            log.error("文件路径构建URL时出错: {}", fileName, e);
            throw new IOException("无效的文件路径: " + fileName, e);
        } catch (NumberFormatException e) {
            log.warn("解析Range头失败: {}, fileName={}", headers.getFirst(HttpHeaders.RANGE), fileName, e);
             throw new IOException("无效的Range请求头", e);
        }
    }

    /**
     * 下载文件
     *
     * @param filename 文件名
     * @param headers  请求头
     * @return 包含文件内容的ResponseEntity
     * @throws IOException 如果文件访问出错
     */
    @Override
    public ResponseEntity<Resource> downloadFile(String filename, HttpHeaders headers) throws IOException {
        // 安全检查: 防止路径遍历攻击
        if (!isValidFileName(filename)) {
            log.warn("请求下载的文件名无效: {}", filename);
            throw new IOException("无效的文件名: " + filename + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

         // 安全检查: 检查文件扩展名是否在白名单中
        String fileExtension = getFileExtension(filename);
        if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
            log.warn("请求下载的文件类型不被允许: {}, 扩展名: {}", filename, fileExtension);
            throw new IOException("不支持的文件类型: " + fileExtension + "。允许的文件类型: " + String.join(", ", ALLOWED_EXTENSIONS));
        }

        try {
            // 动态构建并验证用户特定的文件路径
            Path filePath = getUserSpecificFilePath(filename);
            Resource resource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在
            if (!resource.exists() || !resource.isReadable()) {
                 log.warn("请求下载的文件不存在或不是文件: {}", filePath);
                throw new IOException("文件不存在: " + filename);
            }

            // 获取文件MIME类型
            String contentType = determineContentType(filePath);

            // 设置HTTP头
            HttpHeaders responseHeaders = new HttpHeaders();
            // 使用Spring的ContentDisposition构建器来设置头，更健壮
            ContentDisposition contentDisposition = ContentDisposition.attachment()
                    .filename(resource.getFilename())
                    .build();
            responseHeaders.setContentDisposition(contentDisposition);
            responseHeaders.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            responseHeaders.add(HttpHeaders.PRAGMA, "no-cache");
            responseHeaders.add(HttpHeaders.EXPIRES, "0");
            responseHeaders.setContentType(MediaType.parseMediaType(contentType));

            // 构建响应 - 注意：对于下载，通常不处理Range请求，直接返回整个文件
            return ResponseEntity.ok()
                    .headers(responseHeaders)
                    .contentLength(resource.contentLength())
                    // 返回ResponseEntity.body（Resource）时自动从资源流获取输入流和设置缓冲区和输出流，最后返回数据
                    .body(resource);

        } catch (MalformedURLException e) {
             log.error("文件路径构建URL时出错: {}", filename, e);
            throw new IOException("无效的文件路径: " + filename, e);
        }
    }

    /**
     * 确定文件的MIME类型
     * 获取文件MIME类型，MIME类型（让浏览器识别这个类型之后，根据这个类型调用相应的事，比如说视频或图片在线观看或者下载）
     *
     * @param filePath 文件路径 (应该是已经验证过的绝对路径)
     * @return 文件的MIME类型
     * @throws IOException 如果文件访问出错
     */
    @Override
    public String determineContentType(Path filePath) throws IOException {
        // 获取文件MIME类型通过路径
        String contentType = Files.probeContentType(filePath);
        if (contentType == null) {
            // 如果 probeContentType 无法确定，则根据文件扩展名手动设置
            String fileName = filePath.getFileName().toString();
            String extension = getFileExtension(fileName); // 使用已有方法获取小写扩展名
            switch (extension) {
                case "mp4": contentType = "video/mp4"; break;
                case "webm": contentType = "video/webm"; break;
                case "ogg": contentType = "video/ogg"; break; // 通常是 .ogv 或 .oga
                case "ogv": contentType = "video/ogg"; break;
                case "mp3": contentType = "audio/mpeg"; break;
                case "wav": contentType = "audio/wav"; break;
                case "oga": contentType = "audio/ogg"; break;
                case "pdf": contentType = "application/pdf"; break;
                case "txt": contentType = "text/plain"; break;
                case "html": case "htm": contentType = "text/html"; break;
                case "css": contentType = "text/css"; break;
                case "js": contentType = "application/javascript"; break;
                case "json": contentType = "application/json"; break;
                case "xml": contentType = "application/xml"; break;
                case "doc": contentType = "application/msword"; break;
                case "docx": contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"; break;
                case "xls": contentType = "application/vnd.ms-excel"; break;
                case "xlsx": contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; break;
                case "ppt": contentType = "application/vnd.ms-powerpoint"; break;
                case "pptx": contentType = "application/vnd.openxmlformats-officedocument.presentationml.presentation"; break;
                case "zip": contentType = "application/zip"; break;
                case "rar": contentType = "application/vnd.rar"; break; // 或者 application/x-rar-compressed
                case "exe": contentType = "application/vnd.microsoft.portable-executable"; break; // 或者 application/x-msdownload
                case "jpg": case "jpeg": contentType = "image/jpeg"; break;
                case "png": contentType = "image/png"; break;
                case "gif": contentType = "image/gif"; break;
                case "bmp": contentType = "image/bmp"; break;
                case "webp": contentType = "image/webp"; break;
                case "svg": contentType = "image/svg+xml"; break;
                default: contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE; // 通用二进制流
            }
            log.debug("无法自动探测文件 '{}' 的类型，根据扩展名 '{}' 设置为 '{}'", filePath.getFileName(), extension, contentType);
        } else {
             log.debug("探测到文件 '{}' 的类型为 '{}'", filePath.getFileName(), contentType);
        }
        return contentType;
    }

    /**
     * 验证文件名是否合法，防止目录遍历攻击
     *
     * @param fileName 文件名
     * @return 如果文件名合法返回true，否则返回false
     */
    @Override
    public boolean isValidFileName(String fileName) {
        // 定义一个正则表达式，只允许字母、数字、下划线、连字符和点
        Pattern pattern = Pattern.compile("^[a-zA-Z0-9_\\-.]+$");
        Matcher matcher = pattern.matcher(fileName);
        // 检查文件名是否匹配正则表达式，并且不包含 ".." 序列
        return matcher.matches() && !fileName.contains("..");
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名
     */
    @Override
    public String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }




    /**
     * 最佳实践如果需要拿到异步数据的话，必须让异步获得的数据返回主线程，再从主线程返回客户端
     * 异步流式传输文件（通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("ioIntensiveTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers) {
        // 调用带选项的方法，传递默认选项 (null)
        log.debug("调用文件流异步处理方法，将委托给带选项的文件流处理方法: {}", fileName);
        // 注意：通用方法 streamFileAsync(带选项) 内部已经处理了文件名校验和路径构建
        return streamFileAsync(fileName, headers, null);
    }

    /**
     * 异步流式传输文件（带选项，通用方法，适用于任何文件类型）
     * <p>
     * 使用Spring的@Async注解，在专用线程池中执行文件流传输，避免阻塞请求处理线程
     * 支持自定义选项，如缓冲区大小、发送间隔等
     * </p>
     *
     * @param fileName 文件名
     * @param headers  请求头，用于处理Range请求和其他HTTP头信息
     * @param options  流传输选项，可包含缓冲区大小、发送间隔等参数
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("ioIntensiveTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamFileAsync(String fileName, HttpHeaders headers, java.util.Map<String, Object> options) {
        // 安全性检查：文件名本身
        if (!isValidFileName(fileName)) {
            log.warn("异步流式传输：无效的文件名: {}", fileName);
            CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
            future.completeExceptionally(new IOException("无效的文件名: " + fileName));
            return future;
        }

        // 检查是否为下载模式（通过options中的isDownload标志判断）
        boolean isDownloadMode = options != null && Boolean.TRUE.equals(options.get("isDownload"));

        // 如果是下载模式，进行文件扩展名白名单检查
        if (isDownloadMode) {
            String fileExtension = getFileExtension(fileName);
            if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
                log.warn("异步流式传输（下载模式）：请求的文件类型不被允许: {}, 扩展名: {}", fileName, fileExtension);
                CompletableFuture<ResponseBodyEmitter> future = new CompletableFuture<>();
                future.completeExceptionally(new IOException("不支持的文件类型: " + fileExtension));
                return future;
            }
        }

        CompletableFuture<ResponseBodyEmitter> resultFuture = new CompletableFuture<>();

        try {
            String modeDesc = isDownloadMode ? "下载模式" : "流式传输模式";
            log.debug("开始异步处理文件流(带选项，{}，ioIntensiveTaskExecutor): {}", modeDesc, fileName);

            // 动态构建并验证用户特定的文件路径
            final Path filePath = getUserSpecificFilePath(fileName);
            final Resource fileResource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());

            // 检查文件是否存在且可读
            if (!fileResource.exists() || !fileResource.isReadable()) {
                log.warn("异步流式传输：请求的文件不存在或不是文件: {}", filePath);
                throw new IOException("文件不存在: " + fileName);
            }
            if (!fileResource.isReadable()) {
                log.warn("异步流式传输：请求的文件不可读: {}", filePath);
                throw new IOException("无法读取文件: " + fileName);
            }

            // 获取文件元数据
            final long fileSize = fileResource.contentLength();

            // 使用统一的Range处理方法
            final RangeRequest rangeRequest;
            try {
                rangeRequest = extractRangeFromHeaders(headers, fileSize);
            } catch (IllegalArgumentException e) {
                log.warn("异步流式传输：Range请求处理失败: {}", e.getMessage());
                throw new IOException("无效的Range请求: " + e.getMessage(), e);
            }

            // 用于lambda表达式的final变量
            final long finalRangeStart = rangeRequest.getRangeStart();
            final long contentLength = rangeRequest.getContentLength();

            // 从options中获取缓冲区大小，如果没有指定则使用默认值8KB
            final int bufferSize = options != null && options.containsKey("bufferSize") ?
                    (int) options.get("bufferSize") : 8192; // 默认8KB

            // 从options中获取线程休眠时间，控制发送速度，如果没有指定则不休眠
            final long sleepTime = options != null && options.containsKey("sleepTime") ?
                    (long) options.get("sleepTime") : 0; // 默认0，不休眠

            log.debug("文件流传输使用的缓冲区大小: {} 字节, 发送间隔: {} ms", bufferSize, sleepTime);

            // 创建响应发射器，设置超时时间（可选，例如120秒）
            ResponseBodyEmitter emitter = new ResponseBodyEmitter(120_000L);

            // 使用统一的文件传输架构进行带进度监控的文件传输
            // 决策理由：替换原有的分块传输逻辑，使用统一架构提供进度监控和性能优化

            // 创建传输配置
            TransferConfig config = TransferConfig.builder()
                    .transferType("download")
                    .bufferSize(bufferSize)
                    .rangeStart(finalRangeStart)
                    .rangeEnd(finalRangeStart + contentLength - 1)
                    .sendIntervalMs(sleepTime)
                    .enableProgressMonitoring(true)
                    .build();

            // 创建进度监控器
            FileTransferProgressMonitor progressMonitor = new LocalFileTransferProgressMonitor();

            try {
                // 使用统一的文件传输工具进行带进度监控的下载传输
                long transferredBytes = FileTransferUtil.transferFromFileToEmitter(
                        filePath,
                        emitter,
                        fileName,
                        progressMonitor,
                        config);

                log.info("异步流式传输完成（带进度监控）: {} -> HTTP响应, 实际传输: {} bytes",
                        fileName, transferredBytes);

            } catch (IOException e) {
                log.error("异步流式传输过程中发生IO错误（统一架构）: {}", filePath, e);
                emitter.completeWithError(e);
            } catch (Exception e) {
                log.error("异步流式传输过程中发生意外错误（统一架构）: {}", filePath, e);
                emitter.completeWithError(e);
            }

            // 注册完成和超时的回调
            emitter.onCompletion(() -> log.info("Emitter completed for: {}", filePath.getFileName()));
            emitter.onTimeout(() -> {
                log.warn("Emitter timed out for: {}", filePath.getFileName());
                emitter.completeWithError(new IOException("流式传输超时 for " + filePath.getFileName()));
            });
            emitter.onError((throwable) -> log.error("Emitter error for: {}", filePath.getFileName(), throwable));

            // 异步任务已启动，立即完成外部的Future，值为emitter
            resultFuture.complete(emitter);

        } catch (IOException e) {
            // 处理在准备阶段发生的IO异常
            log.error("准备异步文件流时出错: {}", fileName, e);
            resultFuture.completeExceptionally(e);
        } catch (Exception e) {
            // 处理其他准备阶段的意外异常
            log.error("准备异步文件流时发生意外错误: {}", fileName, e);
            resultFuture.completeExceptionally(e);
        }

        return resultFuture;
    }



    /**
     * 获取文件元数据信息
     *
     * @param filename 文件名
     * @return 文件元数据对象
     * @throws IOException 如果文件访问出错或文件不存在
     */
    @Override
    public FileMetadata getFileMetadata(String filename) throws IOException {
        // 安全检查: 防止路径遍历攻击
        if (!isValidFileName(filename)) {
            throw new IOException("无效的文件名: " + filename + "。文件名只能包含字母、数字、下划线、连字符和点");
        }

        // 动态构建并验证用户特定的文件路径
        Path filePath = getUserSpecificFilePath(filename);
        Resource resource = resourceLoader.getResource("file:" + filePath.toAbsolutePath());
        File file = filePath.toFile();

        // 初始化元数据
        FileMetadata metadata = new FileMetadata();
        metadata.setFileName(filename); // 使用原始传入的文件名

        // 检查文件是否存在且可读
        boolean exists = resource.exists();
        boolean readable = exists && resource.isReadable(); // 只有存在的文件才能判断是否可读

        metadata.setExists(exists);
        metadata.setReadable(readable);

        if (exists) { // 只有文件存在时才获取大小和类型
            metadata.setFileSize(resource.contentLength());
            if (readable) { // 只有可读时尝试获取类型
                 try {
                    String contentType = determineContentType(filePath);
                    metadata.setContentType(contentType);
                 } catch (IOException e) {
                     log.warn("获取文件 {} 的 ContentType 时出错: {}", filePath, e.getMessage());
                     metadata.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 出错时给默认值
                 }
            } else {
                 log.warn("文件 {} 不可读，无法确定 ContentType", filePath);
                 metadata.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 不可读时给默认值
            }
        } else {
            // 文件不存在
            log.warn("请求获取元数据的文件不存在: {}", filePath);
            metadata.setFileSize(0);
            metadata.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE); // 不存在时给默认值
             // 可以考虑直接抛出FileNotFoundException，取决于业务需求
             // throw new FileNotFoundException("文件不存在: " + filename);
        }

        return metadata;
    }

    /**
     * 获取文件的安全ContentType
     * <p>
     * 基于文件元数据和文件扩展名确定正确的MIME类型，提供默认值fallback
     * </p>
     *
     * @param filename 文件名
     * @param defaultContentType 默认的ContentType（当无法确定时使用）
     * @return 安全的ContentType字符串
     * @throws IOException 如果文件访问出错
     */
    @Override
    public String getSafeContentType(String filename, String defaultContentType) throws IOException {
        try {
            FileMetadata metadata = getFileMetadata(filename);
            return getSafeContentType(metadata, defaultContentType);
        } catch (IOException e) {
            log.warn("获取文件ContentType失败: {}, 使用默认值: {}", filename, defaultContentType, e);
            return defaultContentType != null ? defaultContentType : MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
    }

    /**
     * 基于已有元数据获取安全ContentType（避免重复调用getFileMetadata）
     * <p>
     * 基于已获取的文件元数据确定正确的MIME类型，提供默认值fallback
     * </p>
     *
     * @param metadata 已获取的文件元数据
     * @param defaultContentType 默认的ContentType（当无法确定时使用）
     * @return 安全的ContentType字符串
     */
    @Override
    public String getSafeContentType(FileMetadata metadata, String defaultContentType) {
        String contentType = metadata.getContentType();

        // 如果Service已经确定了ContentType，直接使用
        if (contentType != null && !contentType.isEmpty() &&
            !MediaType.APPLICATION_OCTET_STREAM_VALUE.equals(contentType)) {
            return contentType;
        }

        // 如果Service无法确定，使用默认值
        return defaultContentType != null ? defaultContentType : MediaType.APPLICATION_OCTET_STREAM_VALUE;
    }

    /**
     * 构建HTTP响应头信息
     * <p>
     * 根据文件元数据和请求头构建完整的HTTP响应头，包括ContentType、Range支持等
     * </p>
     *
     * @param filename 文件名
     * @param headers 请求头
     * @param defaultContentType 默认ContentType
     * @return 构建好的HttpHeaders对象
     * @throws IOException 如果文件访问出错
     */
    @Override
    public HttpHeaders buildResponseHeaders(String filename, HttpHeaders headers, String defaultContentType) throws IOException {
        // 获取文件元数据
        FileMetadata metadata = getFileMetadata(filename);
        // 委托给基于元数据的方法
        return buildResponseHeaders(metadata, headers, defaultContentType);
    }

    /**
     * 基于已有元数据构建HTTP响应头信息（避免重复调用getFileMetadata）
     * <p>
     * 根据已获取的文件元数据和请求头构建完整的HTTP响应头，包括ContentType、Range支持等
     * </p>
     *
     * @param metadata 已获取的文件元数据
     * @param headers 请求头
     * @param defaultContentType 默认ContentType
     * @return 构建好的HttpHeaders对象
     */
    @Override
    public HttpHeaders buildResponseHeaders(FileMetadata metadata, HttpHeaders headers, String defaultContentType) {
        HttpHeaders responseHeaders = new HttpHeaders();

        // 1. 设置ContentType
        String contentType = getSafeContentType(metadata, defaultContentType);
        responseHeaders.setContentType(MediaType.parseMediaType(contentType));

        // 2. 设置Range支持
        responseHeaders.add(HttpHeaders.ACCEPT_RANGES, "bytes");

        // 3. 检查Range请求并设置相应头
        String range = headers.getFirst(HttpHeaders.RANGE);
        if (range != null && range.startsWith("bytes=")) {
            // 这里只设置基础的Range支持头，具体的Range处理由异步方法处理
            log.debug("检测到Range请求: {}", range);
        } else {
            // 非Range请求，设置完整内容长度
            responseHeaders.setContentLength(metadata.getFileSize());
        }

        // 4. 设置缓存控制（可根据文件类型调整）
        if (contentType.startsWith("image/") || contentType.startsWith("video/")) {
            // 图片和视频可以缓存较长时间
            responseHeaders.setCacheControl("public, max-age=3600"); // 1小时
        } else {
            // 其他文件类型使用较短缓存
            responseHeaders.setCacheControl("public, max-age=300"); // 5分钟
        }

        return responseHeaders;
    }

    /**
     * 构建文件下载的HTTP响应头信息
     * <p>
     * 专门用于文件下载，包括Content-Disposition、ContentType、Range支持等
     * </p>
     *
     * @param filename 文件名
     * @param headers 请求头
     * @return 构建好的HttpHeaders对象，包含下载特有的头信息
     * @throws IOException 如果文件访问出错
     */
    @Override
    public HttpHeaders buildDownloadResponseHeaders(String filename, HttpHeaders headers) throws IOException {
        // 获取文件元数据
        FileMetadata metadata = getFileMetadata(filename);
        // 委托给基于元数据的方法
        return buildDownloadResponseHeaders(metadata, headers);
    }

    /**
     * 基于已有元数据构建文件下载的HTTP响应头信息（避免重复调用getFileMetadata）
     * <p>
     * 专门用于文件下载，包括Content-Disposition、ContentType、Range支持等
     * </p>
     *
     * @param metadata 已获取的文件元数据
     * @param headers 请求头
     * @return 构建好的HttpHeaders对象，包含下载特有的头信息
     */
    @Override
    public HttpHeaders buildDownloadResponseHeaders(FileMetadata metadata, HttpHeaders headers) {
        HttpHeaders responseHeaders = new HttpHeaders();

        // 1. 设置ContentType（下载文件通常使用通用类型）
        String contentType = getSafeContentType(metadata, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        responseHeaders.setContentType(MediaType.parseMediaType(contentType));

        // 2. 设置Content-Disposition为attachment，指定下载文件名
        try {
            ContentDisposition contentDisposition = ContentDisposition.attachment()
                    .filename(metadata.getFileName())
                    .build();
            responseHeaders.setContentDisposition(contentDisposition);
        } catch (Exception e) {
            // 如果文件名有特殊字符，使用简单的方式设置
            log.warn("设置ContentDisposition失败，使用简单方式: {}", metadata.getFileName(), e);
            responseHeaders.set(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=\"" + metadata.getFileName() + "\"");
        }

        // 3. 设置Range支持
        responseHeaders.add(HttpHeaders.ACCEPT_RANGES, "bytes");

        // 4. 检查Range请求并设置相应头
        String range = headers.getFirst(HttpHeaders.RANGE);
        if (range != null && range.startsWith("bytes=")) {
            // 这里只设置基础的Range支持头，具体的Range处理由异步方法处理
            log.debug("检测到Range请求: {}", range);
        } else {
            // 非Range请求，设置完整内容长度
            responseHeaders.setContentLength(metadata.getFileSize());
        }

        // 5. 设置下载特有的缓存控制
        responseHeaders.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        responseHeaders.add(HttpHeaders.PRAGMA, "no-cache");
        responseHeaders.add(HttpHeaders.EXPIRES, "0");

        // 6. 设置缓冲区优化头
        responseHeaders.set("X-Accel-Buffering", "yes");

        return responseHeaders;
    }

    /**
     * 异步流式传输视频
     * <p>
     * 使用Spring的@Async注解在专用线程池中执行视频流传输，避免阻塞请求处理线程
     * </p>
     *
     * @param fileName 视频文件名
     * @param headers  请求头，用于处理范围请求
     * @return 包含ResponseBodyEmitter的CompletableFuture
     */
    @Async("ioIntensiveTaskExecutor")
    @Override
    public CompletableFuture<ResponseBodyEmitter> streamVideoAsync(String fileName, HttpHeaders headers) {
        // 直接调用通用的文件流方法
        log.debug("调用视频流异步处理方法，将委托给通用文件流处理方法: {}", fileName);
        // 注意：通用方法 streamFileAsync 内部已经处理了文件名校验和路径构建
        return streamFileAsync(fileName, headers);
    }



    /**
     * 代表底层资源的特定字节范围的资源实现。
     * <p>
     * 这个类专门用于处理HTTP范围请求（HTTP Range requests），例如视频拖动播放或文件断点续传。
     * 它通过每次调用 {@link #getInputStream()} 时都创建一个新的输入流，来确保资源的可重复读取性。
     * 这解决了直接使用 {@link InputStreamResource} 可能导致流只能被消费一次的问题，
     * 使其在Spring MVC环境中作为 {@link ResponseEntity} 的body部分时更加健壮。
     * </p>
     */
    public static class RangeResource extends org.springframework.core.io.AbstractResource {
        private final Resource resource;
        private final long start;
        private final long length;

        /**
         * 构造一个新的 {@code RangeResource}.
         * @param resource 底层的原始资源 (例如一个 {@link UrlResource} 指向完整文件)
         * @param start 范围的起始字节位置（包含）
         * @param length 范围的长度（字节数）
         */
        public RangeResource(Resource resource, long start, long length) {
            if (start < 0) {
                throw new IllegalArgumentException("Start position cannot be negative");
            }
            if (length < 0) {
                throw new IllegalArgumentException("Length cannot be negative");
            }
            this.resource = resource;
            this.start = start;
            this.length = length;
        }

        /**
         * {@inheritDoc}
         * <p>每次调用此方法时，都会从底层资源创建一个新的 {@link InputStream}，
         * 跳到指定的起始位置，并返回一个被 {@link LimitedInputStream} 包装的流，
         * 以确保只返回请求范围内的数据。</p>
         */
        @Override
        public InputStream getInputStream() throws IOException {
            InputStream is = this.resource.getInputStream();
            if (is.skip(this.start) < this.start) {
                try {
                    is.close();
                } catch (IOException ignored) {
                }
                throw new IOException("Failed to skip to start of range for resource: " + this.resource.getDescription());
            }
            return new LimitedInputStream(is, this.length);
        }

        @Override
        public long contentLength() {
            return this.length;
        }

        @Override
        public String getDescription() {
            return this.resource.getDescription() + " (range: " + this.start + " to " + (this.start + this.length - 1) + ")";
        }

        @Override
        public boolean isReadable() {
            return this.resource.isReadable();
        }
    }


    /**
     * 一个内部辅助类，它包装了一个现有的 {@link InputStream}，并限制从其读取的字节总数。
     * <p>
     * 当到达指定的字节限制后，该流的表现如同到达了末尾（read() 返回 -1）。
     * 这对于精确控制从流中读取的数据量非常有用，尤其是在处理文件范围时。
     * </p>
     */
    private static class LimitedInputStream extends FilterInputStream {
        private long remaining;

        LimitedInputStream(InputStream in, long length) {
            super(in);
            this.remaining = length;
        }

        @Override
        public int read() throws IOException {
            if (remaining <= 0) {
                return -1; // 没有更多字节可读
            }
            int result = super.read();
            if (result != -1) {
                remaining--;
            }
            return result;
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            if (remaining <= 0) {
                return -1; // 没有更多字节可读
            }
            // 调整读取长度，不超过剩余字节数
            int actualLen = (int) Math.min(len, remaining);
            int result = super.read(b, off, actualLen);
            if (result != -1) {
                remaining -= result;
            }
            return result;
        }

        @Override
        public long skip(long n) throws IOException {
             if (n <= 0) {
                return 0;
            }
            // 限制跳过的字节数不超过剩余字节数
            long actualSkip = Math.min(n, remaining);
            long skipped = super.skip(actualSkip);
            remaining -= skipped;
            return skipped;
        }

        @Override
        public int available() throws IOException {
            // 可用字节数是底层流的可用字节数和剩余字节数中的较小值
            return (int) Math.min(super.available(), remaining);
        }
    }

    // ==================== Range处理相关方法 ====================

    /**
     * 解析HTTP Range请求头
     * <p>
     * 解析Range请求头并返回标准化的范围信息，包括验证和调整
     * </p>
     *
     * @param rangeHeader Range请求头的值
     * @param fileSize 文件总大小
     * @return 解析后的范围请求对象
     * @throws IllegalArgumentException 如果Range头格式无效
     */
    @Override
    public RangeRequest parseRangeRequest(String rangeHeader, long fileSize) {
        if (rangeHeader == null || !rangeHeader.startsWith("bytes=")) {
            return RangeRequest.createFullFileRange(fileSize);
        }

        try {

            String rangeValue = rangeHeader.substring("bytes=".length());
            // range请求的值格式正常为byte=2111-3379
            String[] ranges = rangeValue.split("-");

            long rangeStart = 0;
            long rangeEnd = fileSize - 1;

            // 解析起始位置
            if (ranges.length > 0 && !ranges[0].isEmpty()) {
                rangeStart = Long.parseLong(ranges[0]);
            }

            // 解析结束位置
            if (ranges.length > 1 && !ranges[1].isEmpty()) {
                rangeEnd = Long.parseLong(ranges[1]);
            }

            return RangeRequest.builder()
                    .rangeStart(rangeStart)
                    .rangeEnd(rangeEnd)
                    .fileSize(fileSize)
                    .isRangeRequest(true)
                    .originalRangeHeader(rangeHeader)
                    .build();

        } catch (NumberFormatException e) {
            log.warn("解析Range头失败: {}", rangeHeader, e);
            throw new IllegalArgumentException("无效的Range请求头格式: " + rangeHeader, e);
        }
    }

    /**
     * 验证并调整Range请求
     * <p>
     * 验证Range请求的有效性，并根据文件大小进行必要的调整
     * </p>
     *
     * @param rangeRequest 原始的范围请求
     * @return 验证和调整后的范围请求
     */
    @Override
    public RangeRequest validateAndAdjustRange(RangeRequest rangeRequest) {
        long rangeStart = rangeRequest.getRangeStart();
        long rangeEnd = rangeRequest.getRangeEnd();
        long fileSize = rangeRequest.getFileSize();

        // 调整起始位置
        if (rangeStart < 0) {
            rangeStart = 0;
        }

        // 调整结束位置
        if (rangeEnd >= fileSize) {
            rangeEnd = fileSize - 1;
        }

        // 验证范围有效性
        if (rangeStart >= fileSize) {
            log.warn("请求范围无效，起始点超出文件大小: rangeStart={}, fileSize={}", rangeStart, fileSize);
            throw new IllegalArgumentException("请求范围无效，起始点超出文件大小");
        }

        if (rangeStart > rangeEnd) {
            log.warn("请求范围无效，起始点大于结束点: rangeStart={}, rangeEnd={}", rangeStart, rangeEnd);
            throw new IllegalArgumentException("请求范围无效，起始点大于结束点");
        }

        // 计算内容长度,+1是因为起始字节也包含在请求里比如203到211的所以内容字节长度为9
        long contentLength = rangeEnd - rangeStart + 1;

        return RangeRequest.builder()
                .rangeStart(rangeStart)
                .rangeEnd(rangeEnd)
                .contentLength(contentLength)
                .fileSize(fileSize)
                .isRangeRequest(rangeRequest.isRangeRequest())
                .originalRangeHeader(rangeRequest.getOriginalRangeHeader())
                .build();
    }

    /**
     * 从HTTP请求头中提取并解析Range信息
     * <p>
     * 从HttpHeaders中提取Range头并解析为RangeRequest对象
     * </p>
     *
     * @param headers HTTP请求头
     * @param fileSize 文件总大小
     * @return 解析后的范围请求对象，如果没有Range头则返回完整文件范围
     */
    @Override
    public RangeRequest extractRangeFromHeaders(HttpHeaders headers, long fileSize) {
        String rangeHeader = headers.getFirst(HttpHeaders.RANGE);
        RangeRequest rangeRequest = parseRangeRequest(rangeHeader, fileSize);
        return validateAndAdjustRange(rangeRequest);
    }
}
