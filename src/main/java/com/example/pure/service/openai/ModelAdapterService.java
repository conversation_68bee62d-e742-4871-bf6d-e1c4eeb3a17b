package com.example.pure.service.openai;

import com.example.pure.model.adapter.*;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.model.dto.response.openai.OpenAiImageResponse;
import com.example.pure.model.entity.UserApiKey;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 模型适配服务接口
 * <p>
 * 提供不同AI提供商的API格式适配和统一调用接口
 * </p>
 */
public interface ModelAdapterService {

    /**
     * 发起聊天完成请求（流式）
     * <p>
     * 统一的流式聊天接口，自动适配不同提供商的API格式
     * </p>
     *
     * @param provider    提供商类型
     * @param apiKey      API密钥
     * @param request     聊天请求
     * @return 流式响应
     */
    Flux<ChatCompletionChunk> streamChatCompletion(UserApiKey.ProviderType provider, String apiKey, ChatCompletionRequest request);

    /**
     * 发起聊天完成请求（非流式）
     * <p>
     * 统一的非流式聊天接口，自动适配不同提供商的API格式
     * </p>
     *
     * @param provider    提供商类型
     * @param apiKey      API密钥
     * @param request     聊天请求
     * @return 完整响应
     */
    Mono<ChatCompletionResponse> chatCompletion(UserApiKey.ProviderType provider, String apiKey, ChatCompletionRequest request);

    /**
     * 获取可用模型列表
     * <p>
     * 获取指定提供商的可用模型列表
     * </p>
     *
     * @param provider 提供商类型
     * @param apiKey   API密钥
     * @return 模型列表
     */
    Mono<List<ModelInfo>> getAvailableModels(UserApiKey.ProviderType provider, String apiKey);

    /**
     * 生成图片
     * <p>
     * 统一的图片生成接口，自动适配不同提供商的API格式
     * 支持ChatGPT和Gemini，不支持Claude
     * </p>
     *
     * @param provider 提供商类型
     * @param apiKey   API密钥
     * @param request  图片生成请求
     * @return 图片生成响应
     */
    Mono<OpenAiImageResponse> generateImage(UserApiKey.ProviderType provider, String apiKey, OpenAiImageRequest request);

    /**
     * 测试API密钥有效性
     * <p>
     * 通过简单的API调用测试密钥是否有效
     * </p>
     *
     * @param provider 提供商类型
     * @param apiKey   API密钥
     * @return 测试结果
     */
    Mono<ApiKeyValidationResult> validateApiKey(UserApiKey.ProviderType provider, String apiKey);

    // ========================
    // 注意：相关的数据传输对象已移动到独立的包中
    // 请参考：com.example.pure.model.adapter 包
    // ========================






}
