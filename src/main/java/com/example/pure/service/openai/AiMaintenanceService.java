package com.example.pure.service.openai;

import com.example.pure.mapper.primary.ChatSessionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * AI系统维护服务
 * <p>
 * 提供定时清理、健康检查等维护功能
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiMaintenanceService {

    private final ChatSessionMapper chatSessionMapper;

    /**
     * 清理过期会话
     * <p>
     * 每小时执行一次，清理过期的聊天会话
     * </p>
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupExpiredSessions() {
        try {
            int deletedCount = chatSessionMapper.deleteExpiredSessions();
            if (deletedCount > 0) {
                log.info("清理过期会话完成，删除数量: {}", deletedCount);
            }
        } catch (Exception e) {
            log.error("清理过期会话失败", e);
        }
    }

    /**
     * API密钥健康检查
     * <p>
     * 每5分钟执行一次，检查API密钥的健康状态
     * </p>
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void healthCheckApiKeys() {
        try {
            // 这里可以添加API密钥健康检查逻辑
            // 例如：定期测试API密钥的可用性
            log.debug("API密钥健康检查完成");
        } catch (Exception e) {
            log.error("API密钥健康检查失败", e);
        }
    }
}
