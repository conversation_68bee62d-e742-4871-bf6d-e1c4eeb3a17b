package com.example.pure.service.openai;

import com.example.pure.model.adapter.ChatCompletionChunk;
import com.example.pure.model.adapter.ChatCompletionResponse;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.entity.UserApiKey;

/**
 * 推理过程提取服务接口
 * <p>
 * 负责从不同AI提供商的响应中提取思考过程，并转换为OpenAI兼容格式
 * 支持OpenAI o1、Claude Extended Thinking、Gemini Thinking等格式
 * </p>
 */
public interface ReasoningExtractorService {

    /**
     * 从非流式响应中提取推理过程
     * 
     * @param provider AI提供商类型
     * @param response 原始响应
     * @param request 原始请求
     * @return 提取的推理过程内容，如果没有则返回null
     */
    String extractReasoning(UserApiKey.ProviderType provider, ChatCompletionResponse response, OpenAiChatRequest request);

    /**
     * 从流式响应块中提取推理过程
     * 
     * @param provider AI提供商类型
     * @param chunk 流式响应块
     * @param request 原始请求
     * @return 提取的推理过程片段，如果没有则返回null
     */
    String extractStreamReasoning(UserApiKey.ProviderType provider, ChatCompletionChunk chunk, OpenAiChatRequest request);

    /**
     * 检查是否应该启用推理过程
     * 
     * @param provider AI提供商类型
     * @param request 请求对象
     * @return 是否应该启用推理过程
     */
    boolean shouldEnableReasoning(UserApiKey.ProviderType provider, OpenAiChatRequest request);

    /**
     * 为特定提供商修改请求以启用思考过程
     * 
     * @param provider AI提供商类型
     * @param request 要修改的请求
     * @return 修改后的请求
     */
    <T> T enableReasoningForProvider(UserApiKey.ProviderType provider, T request);
}
