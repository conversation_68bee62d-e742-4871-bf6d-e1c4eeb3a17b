package com.example.pure.service.openai.impl;

import com.example.pure.model.adapter.ChatCompletionChunk;
import com.example.pure.model.adapter.ChatCompletionResponse;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.ReasoningExtractorService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 推理过程提取服务实现
 * <p>
 * 实现不同AI提供商思考过程的提取和格式转换
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReasoningExtractorServiceImpl implements ReasoningExtractorService {

    private final ObjectMapper objectMapper;

    @Override
    public String extractReasoning(UserApiKey.ProviderType provider, ChatCompletionResponse response, OpenAiChatRequest request) {
        if (!shouldEnableReasoning(provider, request)) {
            return null;
        }

        try {
            switch (provider) {
                case OPENAI:
                    return extractOpenAiReasoning(response);
                case ANTHROPIC:
                    return extractClaudeReasoning(response);
                case GOOGLE:
                    return extractGeminiReasoning(response);
                default:
                    log.warn("不支持的提供商推理提取: {}", provider);
                    return null;
            }
        } catch (Exception e) {
            log.error("提取推理过程失败 - 提供商: {}", provider, e);
            return null;
        }
    }

    @Override
    public String extractStreamReasoning(UserApiKey.ProviderType provider, ChatCompletionChunk chunk, OpenAiChatRequest request) {
        if (!shouldEnableReasoning(provider, request)) {
            return null;
        }

        try {
            switch (provider) {
                case OPENAI:
                    return extractOpenAiStreamReasoning(chunk);
                case ANTHROPIC:
                    return extractClaudeStreamReasoning(chunk);
                case GOOGLE:
                    return extractGeminiStreamReasoning(chunk);
                default:
                    log.warn("不支持的提供商流式推理提取: {}", provider);
                    return null;
            }
        } catch (Exception e) {
            log.error("提取流式推理过程失败 - 提供商: {}", provider, e);
            return null;
        }
    }

    @Override
    public boolean shouldEnableReasoning(UserApiKey.ProviderType provider, OpenAiChatRequest request) {
        // 检查请求是否明确要求推理过程
        if (!Boolean.TRUE.equals(request.getIncludeReasoning())) {
            return false;
        }

        // 检查提供商是否支持推理过程
        switch (provider) {
            case OPENAI:
                return isReasoningModel(request.getModel());
            case ANTHROPIC:
                return isClaudeReasoningModel(request.getModel());
            case GOOGLE:
                return isGeminiReasoningModel(request.getModel());
            default:
                return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T enableReasoningForProvider(UserApiKey.ProviderType provider, T request) {
        // 这里需要根据具体的请求类型来修改
        // 由于泛型限制，这里返回原始请求，具体实现在调用方处理
        return request;
    }

    // ========================
    // OpenAI 推理提取
    // ========================

    private String extractOpenAiReasoning(ChatCompletionResponse response) {
        // OpenAI o1格式：在message中有reasoning_content字段
        try {
            JsonNode responseNode = objectMapper.valueToTree(response);
            JsonNode choices = responseNode.path("choices");
            
            if (choices.isArray() && choices.size() > 0) {
                JsonNode message = choices.get(0).path("message");
                String reasoningContent = message.path("reasoning_content").asText();
                return reasoningContent.isEmpty() ? null : reasoningContent;
            }
        } catch (Exception e) {
            log.error("解析OpenAI推理内容失败", e);
        }
        return null;
    }

    private String extractOpenAiStreamReasoning(ChatCompletionChunk chunk) {
        // OpenAI流式推理提取
        try {
            JsonNode chunkNode = objectMapper.valueToTree(chunk);
            JsonNode choices = chunkNode.path("choices");
            
            if (choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).path("delta");
                String reasoningContent = delta.path("reasoning_content").asText();
                return reasoningContent.isEmpty() ? null : reasoningContent;
            }
        } catch (Exception e) {
            log.error("解析OpenAI流式推理内容失败", e);
        }
        return null;
    }

    // ========================
    // Claude 推理提取
    // ========================

    private String extractClaudeReasoning(ChatCompletionResponse response) {
        // Claude格式：在content数组中查找thinking类型的块
        try {
            JsonNode responseNode = objectMapper.valueToTree(response);
            JsonNode contentArray = responseNode.path("content");
            
            if (contentArray.isArray()) {
                StringBuilder reasoning = new StringBuilder();
                for (JsonNode contentBlock : contentArray) {
                    if ("thinking".equals(contentBlock.path("type").asText())) {
                        String thinkingContent = contentBlock.path("thinking").asText();
                        if (!thinkingContent.isEmpty()) {
                            reasoning.append(thinkingContent).append("\n");
                        }
                    }
                }
                return reasoning.length() > 0 ? reasoning.toString().trim() : null;
            }
        } catch (Exception e) {
            log.error("解析Claude推理内容失败", e);
        }
        return null;
    }

    private String extractClaudeStreamReasoning(ChatCompletionChunk chunk) {
        // Claude流式推理提取 - 查找thinking_delta类型
        try {
            JsonNode chunkNode = objectMapper.valueToTree(chunk);
            JsonNode choices = chunkNode.path("choices");
            
            if (choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).path("delta");
                if ("thinking_delta".equals(delta.path("type").asText())) {
                    return delta.path("thinking").asText();
                }
            }
        } catch (Exception e) {
            log.error("解析Claude流式推理内容失败", e);
        }
        return null;
    }

    // ========================
    // Gemini 推理提取
    // ========================

    private String extractGeminiReasoning(ChatCompletionResponse response) {
        // Gemini格式：在parts数组中查找thought=true的部分
        try {
            JsonNode responseNode = objectMapper.valueToTree(response);
            JsonNode candidates = responseNode.path("candidates");
            
            if (candidates.isArray() && candidates.size() > 0) {
                JsonNode content = candidates.get(0).path("content");
                JsonNode parts = content.path("parts");
                
                if (parts.isArray()) {
                    StringBuilder reasoning = new StringBuilder();
                    for (JsonNode part : parts) {
                        if (part.path("thought").asBoolean(false)) {
                            String text = part.path("text").asText();
                            if (!text.isEmpty()) {
                                reasoning.append(text).append("\n");
                            }
                        }
                    }
                    return reasoning.length() > 0 ? reasoning.toString().trim() : null;
                }
            }
        } catch (Exception e) {
            log.error("解析Gemini推理内容失败", e);
        }
        return null;
    }

    private String extractGeminiStreamReasoning(ChatCompletionChunk chunk) {
        // Gemini流式推理提取
        try {
            JsonNode chunkNode = objectMapper.valueToTree(chunk);
            JsonNode candidates = chunkNode.path("candidates");
            
            if (candidates.isArray() && candidates.size() > 0) {
                JsonNode content = candidates.get(0).path("content");
                JsonNode parts = content.path("parts");
                
                if (parts.isArray()) {
                    for (JsonNode part : parts) {
                        if (part.path("thought").asBoolean(false)) {
                            return part.path("text").asText();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析Gemini流式推理内容失败", e);
        }
        return null;
    }

    // ========================
    // 模型检查方法
    // ========================

    private boolean isReasoningModel(String model) {
        if (model == null) return false;
        String lowerModel = model.toLowerCase();
        return lowerModel.contains("o1") || lowerModel.contains("reasoning");
    }

    private boolean isClaudeReasoningModel(String model) {
        if (model == null) return false;
        String lowerModel = model.toLowerCase();
        return lowerModel.contains("claude-4") || lowerModel.contains("claude-3.7") || 
               lowerModel.contains("sonnet-4") || lowerModel.contains("opus-4");
    }

    private boolean isGeminiReasoningModel(String model) {
        if (model == null) return false;
        String lowerModel = model.toLowerCase();
        return lowerModel.contains("gemini-2.5") || lowerModel.contains("2.5");
    }
}
