package com.example.pure.service.openai.impl;

import com.example.pure.mapper.primary.UserAiConfigMapper;
import com.example.pure.mapper.primary.UserApiKeyMapper;
import com.example.pure.model.adapter.ApiKeyValidationResult;
import com.example.pure.model.dto.response.openai.ApiKeyParseResult;
import com.example.pure.model.dto.response.openai.ApiKeyTestResult;
import com.example.pure.model.entity.UserAiConfig;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.AiConfigService;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.service.openai.ModelAdapterService;
import com.example.pure.util.ApiKeyEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI配置管理服务实现类
 * <p>
 * 实现用户AI配置和API密钥的管理功能
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiConfigServiceImpl implements AiConfigService {

    private final UserAiConfigMapper userAiConfigMapper;
    private final UserApiKeyMapper userApiKeyMapper;
    private final LoadBalancerService loadBalancerService;
    private final ModelAdapterService modelAdapterService;
    private final ApiKeyEncryptionUtil encryptionUtil;

    // ========================
    // 用户AI配置管理
    // ========================

    @Override
    public UserAiConfig getUserConfig(Long userId) {
        log.debug("获取用户AI配置 - 用户ID: {}", userId);

        UserAiConfig config = userAiConfigMapper.selectByUserId(userId);
        if (config == null) {
            // 如果用户没有配置，创建并插入默认配置到数据库
            config = createDefaultConfig(userId);
            int inserted = userAiConfigMapper.insert(config);
            if (inserted == 0) {
                throw new RuntimeException("创建用户AI默认配置失败");
            }
            log.info("用户{}没有AI配置，已创建并插入默认配置到数据库", userId);
        }

        return config;
    }

    @Override
    @Transactional
    public UserAiConfig updateUserConfig(Long userId, UserAiConfig config) {
        log.info("更新用户AI配置 - 用户ID: {}", userId);

        config.setUserId(userId);

        // 检查用户是否已有配置
        if (userAiConfigMapper.existsByUserId(userId)) {
            // 更新现有配置
            int updated = userAiConfigMapper.updateByUserId(config);
            if (updated == 0) {
                throw new RuntimeException("更新用户AI配置失败");
            }
        } else {
            // 插入新配置
            int inserted = userAiConfigMapper.insert(config);
            if (inserted == 0) {
                throw new RuntimeException("创建用户AI配置失败");
            }
        }

        return getUserConfig(userId);
    }

    @Override
    @Transactional
    public UserAiConfig resetUserConfig(Long userId) {
        log.info("重置用户AI配置 - 用户ID: {}", userId);

        // 删除现有配置
        userAiConfigMapper.deleteByUserId(userId);

        // 创建默认配置
        UserAiConfig defaultConfig = createDefaultConfig(userId);
        userAiConfigMapper.insert(defaultConfig);

        return defaultConfig;
    }

    // ========================
    // API密钥管理
    // ========================

    @Override
    public List<UserApiKey> getUserApiKeys(Long userId) {
        log.debug("获取用户API密钥列表 - 用户ID: {}", userId);

        List<UserApiKey> apiKeys = userApiKeyMapper.selectByUserId(userId);

        // 脱敏处理
        return apiKeys.stream()
                .peek(this::maskApiKeyForDisplay)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserApiKey> getUserApiKeysByProvider(Long userId, UserApiKey.ProviderType provider) {
        log.debug("获取用户指定提供商的API密钥 - 用户ID: {}, 提供商: {}", userId, provider);

        List<UserApiKey> apiKeys = userApiKeyMapper.selectByUserIdAndProvider(userId, provider.name());

        // 脱敏处理
        return apiKeys.stream()
                .peek(this::maskApiKeyForDisplay)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public UserApiKey addApiKey(Long userId, UserApiKey.ProviderType provider, String keyName, String apiKey, Integer priority) {
        log.info("添加API密钥 - 用户ID: {}, 提供商: {}, 密钥名称: {}", userId, provider, keyName);

        // 加密API密钥
        String encryptedApiKey = encryptionUtil.encrypt(apiKey);

        // 创建API密钥实体
        UserApiKey userApiKey = new UserApiKey()
                .setUserId(userId)
                .setProvider(provider)
                .setKeyName(keyName)
                .setApiKeyEncrypted(encryptedApiKey)
                .setIsActive(true)
                .setPriority(priority != null ? priority : 1)
                .setUsageCount(0L);

        // 插入数据库
        int inserted = userApiKeyMapper.insert(userApiKey);
        if (inserted == 0) {
            throw new RuntimeException("添加API密钥失败");
        }

        // 初始化负载均衡状态
        loadBalancerService.initializeLoadBalanceState(userApiKey);

        // 脱敏处理后返回
        maskApiKeyForDisplay(userApiKey);

        log.info("API密钥添加成功 - ID: {}", userApiKey.getId());
        return userApiKey;
    }

    @Override
    @Transactional
    public UserApiKey updateApiKey(Long userId, Long keyId, String keyName, String apiKey, Integer priority, Boolean isActive) {
        log.info("更新API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询现有密钥
        UserApiKey existingKey = userApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限访问");
        }

        // 更新字段
        existingKey.setKeyName(keyName);
        if (StringUtils.hasText(apiKey)) {
            existingKey.setApiKeyEncrypted(encryptionUtil.encrypt(apiKey));
        }
        if (priority != null) {
            existingKey.setPriority(priority);
        }
        if (isActive != null) {
            existingKey.setIsActive(isActive);
        }

        // 更新数据库
        int updated = userApiKeyMapper.updateById(existingKey);
        if (updated == 0) {
            throw new RuntimeException("更新API密钥失败");
        }

        // 脱敏处理后返回
        maskApiKeyForDisplay(existingKey);

        log.info("API密钥更新成功 - ID: {}", keyId);
        return existingKey;
    }

    @Override
    @Transactional
    public boolean deleteApiKey(Long userId, Long keyId) {
        log.info("删除API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询现有密钥
        UserApiKey existingKey = userApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限访问");
        }

        // 清理负载均衡状态
        loadBalancerService.cleanupLoadBalanceState(keyId);

        // 删除密钥
        int deleted = userApiKeyMapper.deleteById(keyId);

        log.info("API密钥删除成功 - ID: {}", keyId);
        return deleted > 0;
    }

    @Override
    public ApiKeyTestResult testApiKey(Long userId, Long keyId) {
        log.info("测试API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询API密钥
        UserApiKey apiKey = userApiKeyMapper.selectById(keyId);
        if (apiKey == null || !apiKey.getUserId().equals(userId)) {
            return new ApiKeyTestResult(false, "API密钥不存在或无权限访问", 0L, null, null);
        }

        try {
            // 解密API密钥
            String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());

            // 测试API密钥有效性
            long startTime = System.currentTimeMillis();
            ApiKeyValidationResult result =
                    modelAdapterService.validateApiKey(apiKey.getProvider(), decryptedKey).block();
            long responseTime = System.currentTimeMillis() - startTime;

            if (result != null && result.isValid()) {
                // 生成兼容格式的API密钥
                String compatibleKey = generateCompatibleApiKey(userId, keyId);

                return new ApiKeyTestResult(true, "API密钥有效，连接正常", responseTime,
                        apiKey.getProvider().getDisplayName(), compatibleKey);
            } else {
                return new ApiKeyTestResult(false, result != null ? result.getMessage() : "API密钥无效",
                        responseTime, null, null);
            }
        } catch (Exception e) {
            log.error("测试API密钥失败", e);
            return new ApiKeyTestResult(false, "测试失败: " + e.getMessage(), 0L, null, null);
        }
    }

    @Override
    public String generateCompatibleApiKey(Long userId, Long keyId) {
        return encryptionUtil.generateCompatibleApiKey(userId, keyId);
    }

    @Override
    public ApiKeyParseResult parseCompatibleApiKey(String compatibleKey) {
        ApiKeyEncryptionUtil.ParseResult result = encryptionUtil.parseCompatibleApiKey(compatibleKey);
        return new ApiKeyParseResult(result.isValid(), result.getUserId(), result.getKeyId(), result.getHash());
    }

    // ========================
    // 私有方法
    // ========================

    /**
     * 创建默认配置
     */
    private UserAiConfig createDefaultConfig(Long userId) {
        return new UserAiConfig()
                .setUserId(userId)
                .setPreferredModel("gpt-3.5-turbo")
                .setDefaultTemperature(new BigDecimal("0.7"))
                .setDefaultMaxTokens(8192)  // 决策理由：增加默认token数以支持更长的响应
                .setDefaultTopP(new BigDecimal("1.0"))
                .setStreamEnabled(true)
                .setTimeoutSeconds(120)  // 决策理由：增加默认超时时间
                .setSystemPrompt("你是一个有用的AI助手");
        // 决策理由：移除时间字段设置，让数据库的 DEFAULT CURRENT_TIMESTAMP(3) 自动处理
    }

    /**
     * 脱敏处理API密钥用于显示
     */
    private void maskApiKeyForDisplay(UserApiKey apiKey) {
        if (apiKey.getApiKeyEncrypted() != null) {
            try {
                String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());
                apiKey.setApiKeyEncrypted(decryptedKey);
            } catch (Exception e) {
                log.warn("脱敏API密钥失败", e);
                apiKey.setApiKeyEncrypted("****");
            }
        }
    }
}
