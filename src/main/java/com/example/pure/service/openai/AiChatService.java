package com.example.pure.service.openai;

import com.example.pure.model.dto.request.openai.AiChatRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI聊天服务接口
 * <p>
 * 提供AI聊天相关的服务方法，包括：
 * - 处理聊天消息
 * - 流式响应处理
 * - 会话管理
 * </p>
 */
public interface AiChatService {

    /**
     * 处理AI聊天请求并返回SSE流式响应
     *
     * @param request 聊天请求
     * @return SSE发射器
     */
    SseEmitter processChat(AiChatRequest request);

    /**
     * 根据用户输入匹配合适的回复内容
     *
     * @param userMessage 用户输入的消息
     * @param messageType 消息类型
     * @return 匹配的回复内容
     */
    String matchResponse(String userMessage, String messageType);

    /**
     * 生成会话ID
     *
     * @return 新的会话ID
     */
    String generateSessionId();
}
