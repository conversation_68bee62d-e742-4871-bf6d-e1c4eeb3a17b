package com.example.pure.service.openai.impl;

import com.example.pure.config.AiConfig;
import com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper;
import com.example.pure.mapper.primary.UserApiKeyMapper;
import com.example.pure.model.entity.ApiKeyLoadBalance;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 负载均衡服务实现类
 * <p>
 * 实现智能负载均衡算法，提供API密钥的智能选择和健康管理
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoadBalancerServiceImpl implements LoadBalancerService {

    private final ApiKeyLoadBalanceMapper loadBalanceMapper;
    private final UserApiKeyMapper userApiKeyMapper;
    private final AiConfig aiConfig;

    @Override
    public UserApiKey selectBestApiKey(Long userId, UserApiKey.ProviderType provider) {
        log.debug("选择最佳API密钥 - 用户ID: {}, 提供商: {}", userId, provider);

        // 获取用户的活跃API密钥，enum.name方法获取枚举字符串的值
        List<UserApiKey> activeKeys = userApiKeyMapper.selectActiveByUserIdAndProvider(userId, provider.name());
        if (activeKeys.isEmpty()) {
            log.warn("用户{}没有可用的{}提供商API密钥", userId, provider);
            return null;
        }

        // 获取健康的负载均衡状态
        List<ApiKeyLoadBalance> healthyStates = loadBalanceMapper.selectHealthyByUserIdAndProvider(userId, provider.name());

        // 如果没有健康的状态记录，初始化所有密钥的状态
        if (healthyStates.isEmpty()) {
            log.info("初始化用户{}的{}提供商API密钥负载状态", userId, provider);
            for (UserApiKey key : activeKeys) {
                initializeLoadBalanceState(key);
            }
            healthyStates = loadBalanceMapper.selectHealthyByUserIdAndProvider(userId, provider.name());
        }

        // 使用 for 循环实现负载均衡算法选择最佳密钥
        ApiKeyLoadBalance bestState = null;

        for (ApiKeyLoadBalance currentState : healthyStates) {
            // 第一个值先暂时把当前状态设置为最佳状态，以和后面值比较
            if (bestState == null) {
                bestState = currentState;
                continue;
            }

            if (isBetterChoice(currentState, bestState)) {
                bestState = currentState;
            }
        }

        if (bestState != null) {
            Long selectedKeyId = bestState.getApiKeyId();
            UserApiKey selectedKey = null;

            // 查找对应的 UserApiKey
            for (UserApiKey key : activeKeys) {
                if (key.getId().equals(selectedKeyId)) {
                    selectedKey = key;
                    break;
                }
            }

            if (selectedKey != null) {
                log.debug("选择API密钥 - ID: {}, 当前请求数: {}",
                        selectedKeyId, bestState.getCurrentRequests());
                return selectedKey;
            }
        }

        // 如果没有找到合适的密钥，返回第一个活跃密钥
        log.warn("未找到最佳API密钥，返回第一个活跃密钥");
        return activeKeys.get(0);
    }

    @Override
    @Transactional
    public void startUsingApiKey(Long apiKeyId) {
        log.debug("开始使用API密钥 - ID: {}", apiKeyId);

        try {
            int updated = loadBalanceMapper.incrementRequests(apiKeyId);
            if (updated == 0) {
                log.warn("更新API密钥请求计数失败 - ID: {}", apiKeyId);
            }
        } catch (Exception e) {
            log.error("开始使用API密钥时发生错误 - ID: {}", apiKeyId, e);
        }
    }

    @Override
    @Transactional
    public void finishUsingApiKey(Long apiKeyId, boolean success) {
        log.debug("结束使用API密钥 - ID: {}, 成功: {}", apiKeyId, success);

        try {
            // 减少当前请求数
            loadBalanceMapper.decrementRequests(apiKeyId);

            // 更新使用统计
            userApiKeyMapper.updateUsageStats(apiKeyId);

            // 如果失败，增加错误计数并检查健康状态
            if (!success) {
                reportApiKeyError(apiKeyId, "API调用失败");
            }
        } catch (Exception e) {
            log.error("结束使用API密钥时发生错误 - ID: {}", apiKeyId, e);
        }
    }

    @Override
    @Transactional
    public void reportApiKeyError(Long apiKeyId, String error) {
        log.warn("报告API密钥错误 - ID: {}, 错误: {}", apiKeyId, error);

        try {
            // 增加错误计数
            loadBalanceMapper.incrementErrorCount(apiKeyId);

            // 检查是否需要标记为不健康
            ApiKeyLoadBalance state = loadBalanceMapper.selectByApiKeyId(apiKeyId);
            if (state != null) {
                boolean shouldMarkUnhealthy = checkShouldMarkUnhealthy(state);
                if (shouldMarkUnhealthy && state.getIsHealthy()) {
                    updateApiKeyHealth(apiKeyId, false);
                    log.warn("API密钥标记为不健康 - ID: {}", apiKeyId);
                }
            }
        } catch (Exception e) {
            log.error("报告API密钥错误时发生异常 - ID: {}", apiKeyId, e);
        }
    }

    @Override
    public boolean isApiKeyHealthy(Long apiKeyId) {
        try {
            ApiKeyLoadBalance state = loadBalanceMapper.selectByApiKeyId(apiKeyId);
            return state != null && state.getIsHealthy();
        } catch (Exception e) {
            log.error("检查API密钥健康状态时发生错误 - ID: {}", apiKeyId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public void updateApiKeyHealth(Long apiKeyId, boolean isHealthy) {
        log.info("更新API密钥健康状态 - ID: {}, 健康: {}", apiKeyId, isHealthy);

        try {
            int updated = loadBalanceMapper.updateHealthStatus(apiKeyId, isHealthy);
            if (updated == 0) {
                log.warn("更新API密钥健康状态失败 - ID: {}", apiKeyId);
            }
        } catch (Exception e) {
            log.error("更新API密钥健康状态时发生错误 - ID: {}", apiKeyId, e);
        }
    }

    @Override
    public LoadBalanceStats getLoadBalanceStats(Long apiKeyId) {
        try {
            ApiKeyLoadBalance state = loadBalanceMapper.selectByApiKeyId(apiKeyId);
            if (state != null) {
                return new LoadBalanceStats(
                        state.getApiKeyId(),
                        state.getCurrentRequests(),
                        state.getTotalRequests(),
                        state.getErrorCount(),
                        state.getIsHealthy()
                );
            }
            return null;
        } catch (Exception e) {
            log.error("获取负载均衡统计时发生错误 - ID: {}", apiKeyId, e);
            return null;
        }
    }

    @Override
    @Transactional
    public void resetLoadBalanceState(Long apiKeyId) {
        log.info("重置API密钥负载状态 - ID: {}", apiKeyId);

        try {
            ApiKeyLoadBalance state = loadBalanceMapper.selectByApiKeyId(apiKeyId);
            if (state != null) {
                state.setCurrentRequests(0);
                state.setTotalRequests(0L);
                state.setErrorCount(0);
                state.setIsHealthy(true);
                state.setLastErrorAt(null);

                loadBalanceMapper.updateById(state);
            }
        } catch (Exception e) {
            log.error("重置API密钥负载状态时发生错误 - ID: {}", apiKeyId, e);
        }
    }

    @Override
    @Transactional
    public void initializeLoadBalanceState(UserApiKey apiKey) {
        log.debug("初始化API密钥负载状态 - ID: {}", apiKey.getId());

        try {
            // 检查是否已存在
            ApiKeyLoadBalance existing = loadBalanceMapper.selectByApiKeyId(apiKey.getId());
            if (existing != null) {
                log.debug("API密钥负载状态已存在 - ID: {}", apiKey.getId());
                return;
            }

            // 创建新的负载状态
            ApiKeyLoadBalance state = new ApiKeyLoadBalance()
                    .setUserId(apiKey.getUserId())
                    //mybatis自动把枚举类型转换为枚举的字符串常量插入数据库
                    .setApiKeyId(apiKey.getId())
                    .setProvider(apiKey.getProvider())
                    .setApiKeyId(apiKey.getId())
                    .setCurrentRequests(0)
                    .setTotalRequests(0L)
                    .setErrorCount(0)
                    .setIsHealthy(true)
                    .setUpdatedAt(TimeUtil.nowUtc());

            loadBalanceMapper.insert(state);
            log.debug("API密钥负载状态初始化完成 - ID: {}", apiKey.getId());
        } catch (Exception e) {
            log.error("初始化API密钥负载状态时发生错误 - ID: {}", apiKey.getId(), e);
        }
    }

    @Override
    @Transactional
    public void cleanupLoadBalanceState(Long apiKeyId) {
        log.info("清理API密钥负载状态 - ID: {}", apiKeyId);

        try {
            int deleted = loadBalanceMapper.deleteByApiKeyId(apiKeyId);
            log.debug("清理API密钥负载状态完成 - ID: {}, 删除记录数: {}", apiKeyId, deleted);
        } catch (Exception e) {
            log.error("清理API密钥负载状态时发生错误 - ID: {}", apiKeyId, e);
        }
    }

    // ========================
    // 私有方法
    // ========================

    /**
     * 计算错误率
     */
    private double calculateErrorRate(ApiKeyLoadBalance state) {
        if (state.getTotalRequests() == 0) {
            return 0.0;
        }
        return (double) state.getErrorCount() / state.getTotalRequests();
    }

    /**
     * 检查是否应该标记为不健康
     */
    private boolean checkShouldMarkUnhealthy(ApiKeyLoadBalance state) {
        AiConfig.LoadBalance config = aiConfig.getLoadBalance();

        // 错误次数超过阈值
        if (state.getErrorCount() >= config.getMaxErrorCount()) {
            return true;
        }

        // 错误率超过阈值（且有足够的请求样本）
        if (state.getTotalRequests() >= 10) {
            double errorRate = calculateErrorRate(state);
            if (errorRate >= config.getMaxErrorRate()) {
                return true;
            }
        }

        // 并发请求数过高
        if (state.getCurrentRequests() >= config.getMaxConcurrentRequests()) {
            return true;
        }

        return false;
    }

    /**
     * 判断候选状态是否比当前最佳状态更优
     * <p>
     * 决策理由：实现三层优先级比较逻辑，确保选择最优的API密钥
     * </p>
     *
     * @param candidate 候选状态
     * @param current   当前最佳状态
     * @return true 如果候选状态更优
     */
    private boolean isBetterChoice(ApiKeyLoadBalance candidate, ApiKeyLoadBalance current) {
        // 第一优先级：当前请求数（最少连接优先）
        if (candidate.getCurrentRequests() < current.getCurrentRequests()) {
            return true;  // candidate 请求数更少，更优
        }
        if (candidate.getCurrentRequests() > current.getCurrentRequests()) {
            return false; // candidate 请求数更多，不如 current
        }

        // 请求数相同，第二优先级：错误率
        double candidateErrorRate = calculateErrorRate(candidate);
        double currentErrorRate = calculateErrorRate(current);
        if (candidateErrorRate < currentErrorRate) {
            return true;  // candidate 错误率更低，更优
        }
        if (candidateErrorRate > currentErrorRate) {
            return false; // candidate 错误率更高，不如 current
        }

        // 错误率也相同，第三优先级：总请求数（轮询效果）
        if (candidate.getTotalRequests() < current.getTotalRequests()) {
            return true;  // candidate 总请求数更少，更优
        }

        return false; // candidate 总请求数更多或相等，不如 current
    }
}
