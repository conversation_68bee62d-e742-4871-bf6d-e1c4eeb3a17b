package com.example.pure.service.openai.impl;

import com.example.pure.model.dto.request.openai.AiChatRequest;
import com.example.pure.model.dto.response.openai.AiChatResponse;
import com.example.pure.service.openai.AiChatService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * AI聊天服务实现类
 * <p>
 * 实现AI聊天功能，包括：
 * - 预定义响应库管理
 * - 智能消息匹配
 * - SSE流式响应处理
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiChatServiceImpl implements AiChatService {

    private final ObjectMapper objectMapper;

    /**
     * 预定义的AI响应库
     * 根据关键词匹配返回相应的回复内容
     */
    private static final Map<String, List<String>> RESPONSE_LIBRARY = new HashMap<>();

    static {
        // 问候语相关
        RESPONSE_LIBRARY.put("greeting", Arrays.asList(
                "你好！我是AI助手，很高兴为您服务！有什么我可以帮助您的吗？",
                "您好！欢迎使用AI聊天系统，我会尽力回答您的问题。",
                "嗨！我是您的AI助手，请告诉我您需要什么帮助。"
        ));

        // Java相关
        RESPONSE_LIBRARY.put("java", Arrays.asList(
                "Java是一种面向对象的编程语言，具有跨平台、安全性高、性能优秀等特点。它广泛应用于企业级应用开发、Android应用开发、大数据处理等领域。",
                "Java的核心特性包括：面向对象编程、平台无关性、自动内存管理、丰富的API库、强大的多线程支持等。",
                "学习Java建议从基础语法开始，然后学习面向对象编程、集合框架、多线程、IO操作，最后学习Spring等企业级框架。"
        ));

        // Spring相关
        RESPONSE_LIBRARY.put("spring", Arrays.asList(
                "Spring是Java企业级应用开发的主流框架，提供了依赖注入、面向切面编程、事务管理等核心功能。",
                "Spring Boot简化了Spring应用的配置和部署，提供了自动配置、内嵌服务器、监控等特性，大大提高了开发效率。",
                "Spring生态系统包括Spring MVC、Spring Data、Spring Security、Spring Cloud等模块，可以满足各种企业级应用需求。"
        ));

        // 编程相关
        RESPONSE_LIBRARY.put("programming", Arrays.asList(
                "编程是一门艺术，也是一门科学。良好的编程习惯包括：编写清晰的代码、适当的注释、遵循设计模式、进行单元测试等。",
                "学习编程建议多动手实践，从简单的项目开始，逐步提高复杂度。同时要学会阅读优秀的开源代码，学习最佳实践。",
                "现代软件开发强调团队协作、版本控制、持续集成、敏捷开发等理念，这些都是成为优秀程序员的必备技能。"
        ));

        // 数据库相关
        RESPONSE_LIBRARY.put("database", Arrays.asList(
                "数据库是现代应用系统的核心组件，常见的关系型数据库有MySQL、PostgreSQL、Oracle等，NoSQL数据库有MongoDB、Redis、Elasticsearch等。",
                "数据库设计要遵循范式理论，合理设计表结构、索引、约束等。同时要考虑性能优化、数据安全、备份恢复等问题。",
                "SQL是操作关系型数据库的标准语言，掌握SQL的增删改查、连接查询、子查询、存储过程等是数据库开发的基础。"
        ));

        // 默认响应
        RESPONSE_LIBRARY.put("default", Arrays.asList(
                "这是一个很有趣的问题！虽然我的知识库中没有完全匹配的答案，但我建议您可以通过搜索引擎或相关技术文档来获取更详细的信息。",
                "感谢您的提问！这个话题比较专业，建议您查阅相关的技术文档或咨询领域专家获取更准确的答案。",
                "您提出了一个很好的问题！虽然我无法提供完整的答案，但我建议您可以从官方文档或技术社区寻找更详细的解答。"
        ));
    }

    @Override
    public SseEmitter processChat(AiChatRequest request) {
        // 创建SSE发射器，设置超时时间为30秒
        SseEmitter emitter = new SseEmitter(30000L);

        // 生成会话ID（如果没有提供）
        String sessionId = StringUtils.hasText(request.getSessionId())
                ? request.getSessionId()
                : generateSessionId();

        // 启用线程池调用其他线程来异步运行任务,主线程返回emitter释放请求http的主线程，后续其他线程流式发送数据到emitter
        // 异步处理聊天请求
        processChatAsync(emitter, request, sessionId);

        return emitter;
    }

    /**
     * 异步处理聊天请求
     * 使用IO密集型线程池处理SSE流式响应
     */
    @Async("ioIntensiveTaskExecutor")
    public CompletableFuture<Void> processChatAsync(SseEmitter emitter, AiChatRequest request, String sessionId) {
        try {
            log.info("开始处理AI聊天请求 - 会话ID: {}, 消息: {}", sessionId, request.getMessage());

            // 发送启动事件通知
            sendStartEvent(emitter, sessionId);

            // 匹配响应内容
            String responseContent = matchResponse(request.getMessage(), request.getMessageType());

            // 模拟AI逐字输出效果
            simulateTypingEffect(emitter, responseContent, sessionId);

            // 发送结束事件通知
            sendEndEvent(emitter, sessionId);

            // 完成SSE连接
            emitter.complete();

            log.info("AI聊天请求处理完成 - 会话ID: {}", sessionId);

        } catch (Exception e) {
            log.error("处理AI聊天请求时发生错误 - 会话ID: {}", sessionId, e);
            try {
                sendErrorEvent(emitter, "处理请求时发生错误: " + e.getMessage(), sessionId);
                emitter.completeWithError(e);
            } catch (IOException ioException) {
                log.error("发送错误响应失败", ioException);
            }
        }

        return CompletableFuture.completedFuture(null);
    }

    @Override
    public String matchResponse(String userMessage, String messageType) {
        if (!StringUtils.hasText(userMessage)) {
            return getRandomResponse("default");
        }

        String message = userMessage.toLowerCase();

        // 根据关键词匹配响应
        if (message.contains("你好") || message.contains("hello") || message.contains("hi")) {
            return getRandomResponse("greeting");
        } else if (message.contains("java")) {
            return getRandomResponse("java");
        } else if (message.contains("spring")) {
            return getRandomResponse("spring");
        } else if (message.contains("编程") || message.contains("programming") || message.contains("代码")) {
            return getRandomResponse("programming");
        } else if (message.contains("数据库") || message.contains("database") || message.contains("sql")) {
            return getRandomResponse("database");
        } else {
            return getRandomResponse("default");
        }
    }

    @Override
    public String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 从响应库中随机获取一个回复
     */
    private String getRandomResponse(String category) {
        List<String> responses = RESPONSE_LIBRARY.get(category);
        if (responses == null || responses.isEmpty()) {
            responses = RESPONSE_LIBRARY.get("default");
        }
        // 返回随机索引位置的值
        return responses.get(new Random().nextInt(responses.size()));
    }

    /**
     * 模拟AI逐字输出效果
     */
    private void simulateTypingEffect(SseEmitter emitter, String content, String sessionId) throws IOException, InterruptedException {
        StringBuilder currentContent = new StringBuilder();

        // 按字符逐个发送，模拟打字效果
        for (char c : content.toCharArray()) {
            currentContent.append(c);

            // 发送当前内容 - 使用专门的文本事件发送方法
            sendTextEvent(emitter, currentContent.toString(), sessionId);

            // 模拟打字延迟（50-150毫秒）
            Thread.sleep(50 + new Random().nextInt(100));
        }
    }

    /**
     * 发送文本内容事件（正常聊天内容）
     *
     * @param emitter SSE发射器
     * @param content 文本内容
     * @param sessionId 会话ID
     * @throws IOException IO异常
     */
    private void sendTextEvent(SseEmitter emitter, String content, String sessionId) throws IOException {
        AiChatResponse response = AiChatResponse.builder()
                .content(content)
                .type("text")
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .isEnd(false)
                .build();

        String jsonData = objectMapper.writeValueAsString(response);
        emitter.send(SseEmitter.event()
                .name("text")
                .data(jsonData)
                .id(String.valueOf(System.currentTimeMillis())));

        log.debug("发送文本事件 - 会话ID: {}, 内容长度: {}", sessionId, content.length());
    }

    /**
     * 发送控制事件（start/end/error等）
     *
     * @param emitter SSE发射器
     * @param eventType 事件类型 (start/end/error)
     * @param sessionId 会话ID
     * @param isEnd 是否为结束事件
     * @param error 错误信息(可选)
     * @throws IOException IO异常
     */
    private void sendControlEvent(SseEmitter emitter, String eventType,
                                 String sessionId, boolean isEnd, String error) throws IOException {
        AiChatResponse response = AiChatResponse.builder()
                .content("")
                .type(eventType)
                .sessionId(sessionId)
                .timestamp(LocalDateTime.now())
                .isEnd(isEnd)
                .error(error)
                .build();

        String jsonData = objectMapper.writeValueAsString(response);
        emitter.send(SseEmitter.event()
                .name(eventType)
                .data(jsonData)
                .id(String.valueOf(System.currentTimeMillis())));

        if (error != null) {
            log.debug("发送{}控制事件 - 会话ID: {}, 错误: {}", eventType, sessionId, error);
        } else {
            log.debug("发送{}控制事件 - 会话ID: {}", eventType, sessionId);
        }
    }

    /**
     * 发送启动事件通知
     */
    private void sendStartEvent(SseEmitter emitter, String sessionId) throws IOException {
        sendControlEvent(emitter, "start", sessionId, false, null);
    }

    /**
     * 发送结束事件通知
     */
    private void sendEndEvent(SseEmitter emitter, String sessionId) throws IOException {
        sendControlEvent(emitter, "end", sessionId, true, null);
    }

    /**
     * 发送错误事件通知
     */
    private void sendErrorEvent(SseEmitter emitter, String error, String sessionId) throws IOException {
        sendControlEvent(emitter, "error", sessionId, true, error);
    }
}
