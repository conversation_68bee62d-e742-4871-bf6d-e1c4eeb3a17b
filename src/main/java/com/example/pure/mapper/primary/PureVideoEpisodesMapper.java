package com.example.pure.mapper.primary;

import com.example.pure.model.dto.response.video.EpisodeLikeCountDTO;
import com.example.pure.model.dto.response.video.VideoEpisodesDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PureVideoEpisodesMapper {
    // 按Param值去映射到对应的collection的值实现遍历
int insertVideoEpisodes(@Param("videoEpisodesList") List<VideoEpisodesDTO> videoEpisodes);

    /**
     * 根据视频信息ID查找其所有的分集信息。
     * <p>
     * 经过重构，此方法现在通过一次JOIN查询，同时获取每个分集的点赞总数，
     * 以及当前登录用户（如果存在）对每个分集的点赞状态。
     *
     * @param videoInfoId 视频信息的ID
     * @param userId      当前登录用户的ID (可以为null，用于查询点赞状态)
     * @return 包含完整信息（包括点赞数和个人点赞状态）的分集DTO列表
     */
List<VideoEpisodesDTO> findVideoEpisodesByVideoInfoId(@Param("videoInfoId") Long videoInfoId, @Param("userId") Long userId);

    /**
     * 根据视频信息ID查找基础分集信息（不包含点赞数据）
     * <p>
     * 高性能查询：只查询基础字段，避免JOIN和聚合函数
     * </p>
     *
     * @param videoInfoId 视频信息的ID
     * @return 基础分集信息列表
     */
    List<VideoEpisodesDTO> findBasicVideoEpisodesByVideoInfoId(@Param("videoInfoId") Long videoInfoId);

    /**
     * 批量查询分集的点赞总数
     * <p>
     * 高性能查询：使用IN查询和GROUP BY，避免复杂JOIN
     * </p>
     *
     * @param episodeIds 分集ID列表
     * @return Map<episodeId, likesCount>
     */
    List<EpisodeLikeCountDTO> findLikesCountByEpisodeIds(@Param("episodeIds") List<Long> episodeIds);

    /**
     * 批量查询用户对分集的点赞状态
     * <p>
     * 高性能查询：使用IN查询，直接返回用户已点赞的分集ID
     * </p>
     *
     * @param episodeIds 分集ID列表
     * @param userId 用户ID
     * @return 用户已点赞的分集ID列表
     */
    List<Long> findUserLikedEpisodeIds(@Param("episodeIds") List<Long> episodeIds, @Param("userId") Long userId);

int deleteVideoEpisodesByVideoInfoId(Long videoInfoId);

    /**
     * 批量删除指定ID的分集
     * @param episodeIds 要删除的分集ID列表
     * @return 删除的行数
     */
    int deleteVideoEpisodesByIds(@Param("episodeIds") List<Long> episodeIds);

/**
 * 更新指定视频分集的雪碧图对象键
 * @param videoInfoId 视频ID
 * @param episodeNumber 分集编号
 * @param spriteSheetObjectKey 雪碧图对象键
 * @return 更新的行数
 */
int updateSpriteSheetObjectKey(@Param("videoInfoId") Long videoInfoId,
                              @Param("episodeNumber") String episodeNumber,
                              @Param("spriteSheetObjectKey") String spriteSheetObjectKey);

/**
 * 更新指定视频分集的对象键
 * @param videoInfoId 视频ID
 * @param episodeNumber 分集编号
 * @param objectKey 视频文件对象键
 * @return 更新的行数
 */
int updateEpisodeObjectKey(@Param("videoInfoId") Long videoInfoId,
                          @Param("episodeNumber") String episodeNumber,
                          @Param("objectKey") String objectKey);

/**
 * 获取指定视频分集的对象键（用于内部生成临时URL）
 * @param videoInfoId 视频ID
 * @param episodeNumber 分集编号
 * @param type 类型：video（视频文件）或 sprite（雪碧图）
 * @return 对象键，如果不存在返回null
 */
String getObjectKey(@Param("videoInfoId") Long videoInfoId,
                   @Param("episodeNumber") String episodeNumber,
                   @Param("type") String type);
}
