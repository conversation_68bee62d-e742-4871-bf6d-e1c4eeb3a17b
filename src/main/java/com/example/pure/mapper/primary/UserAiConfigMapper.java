package com.example.pure.mapper.primary;

import com.example.pure.model.entity.UserAiConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户AI配置数据访问层
 * <p>
 * 提供用户AI配置的CRUD操作
 * </p>
 */
@Mapper
public interface UserAiConfigMapper {

    /**
     * 根据用户ID查询AI配置
     *
     * @param userId 用户ID
     * @return 用户AI配置
     */
    UserAiConfig selectByUserId(Long userId);

    /**
     * 插入用户AI配置
     *
     * @param config 用户AI配置
     * @return 影响行数
     */
    int insert(UserAiConfig config);

    /**
     * 更新用户AI配置
     *
     * @param config 用户AI配置
     * @return 影响行数
     */
    int updateByUserId(UserAiConfig config);

    /**
     * 根据用户ID删除AI配置
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Long userId);

    /**
     * 检查用户是否已有AI配置
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByUserId(Long userId);
}
