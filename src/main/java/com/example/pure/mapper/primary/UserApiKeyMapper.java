package com.example.pure.mapper.primary;

import com.example.pure.model.entity.UserApiKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户API密钥数据访问层
 * <p>
 * 提供用户API密钥的CRUD操作
 * </p>
 */
@Mapper
public interface UserApiKeyMapper {

    /**
     * 根据ID查询API密钥
     *
     * @param id 密钥ID
     * @return API密钥信息
     */
    UserApiKey selectById(Long id);

    /**
     * 根据用户ID查询所有API密钥
     *
     * @param userId 用户ID
     * @return API密钥列表
     */
    List<UserApiKey> selectByUserId(Long userId);

    /**
     * 根据用户ID和提供商查询API密钥
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return API密钥列表
     */
    List<UserApiKey> selectByUserIdAndProvider(Long userId, String provider);

    /**
     * 查询用户的活跃API密钥
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return 活跃的API密钥列表
     */
    List<UserApiKey> selectActiveByUserIdAndProvider(Long userId, String provider);

    /**
     * 插入API密钥
     *
     * @param apiKey API密钥信息
     * @return 影响行数
     */
    int insert(UserApiKey apiKey);

    /**
     * 更新API密钥
     *
     * @param apiKey API密钥信息
     * @return 影响行数
     */
    int updateById(UserApiKey apiKey);

    /**
     * 更新API密钥使用统计
     *
     * @param id 密钥ID
     * @return 影响行数
     */
    int updateUsageStats(Long id);

    /**
     * 删除API密钥
     *
     * @param id 密钥ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据用户ID删除所有API密钥
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Long userId);
}
