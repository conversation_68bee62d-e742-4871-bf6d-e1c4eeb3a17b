package com.example.pure.mapper.primary;

import com.example.pure.model.entity.ChatSession;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 聊天会话数据访问层
 * <p>
 * 提供聊天会话的CRUD操作
 * </p>
 */
@Mapper
public interface ChatSessionMapper {

    /**
     * 根据会话ID查询会话信息
     *
     * @param sessionId 会话ID
     * @return 会话信息
     */
    ChatSession selectBySessionId(String sessionId);

    /**
     * 根据用户ID查询会话列表
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ChatSession> selectByUserId(Long userId);

    /**
     * 查询用户的活跃会话（未过期）
     *
     * @param userId 用户ID
     * @return 活跃会话列表
     */
    List<ChatSession> selectActiveByUserId(Long userId);

    /**
     * 插入会话信息
     *
     * @param session 会话信息
     * @return 影响行数
     */
    int insert(ChatSession session);

    /**
     * 更新会话信息
     *
     * @param session 会话信息
     * @return 影响行数
     */
    int updateBySessionId(ChatSession session);

    /**
     * 增加消息计数
     *
     * @param sessionId 会话ID
     * @return 影响行数
     */
    int incrementMessageCount(String sessionId);

    /**
     * 删除会话
     *
     * @param sessionId 会话ID
     * @return 影响行数
     */
    int deleteBySessionId(String sessionId);

    /**
     * 根据用户ID删除所有会话
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Long userId);

    /**
     * 删除过期会话
     *
     * @return 影响行数
     */
    int deleteExpiredSessions();

    /**
     * 检查会话是否存在
     *
     * @param sessionId 会话ID
     * @return 是否存在
     */
    boolean existsBySessionId(String sessionId);
}
