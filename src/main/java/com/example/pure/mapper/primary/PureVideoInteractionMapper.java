package com.example.pure.mapper.primary;

import com.example.pure.model.entity.VideoEpisodesLikes;
import com.example.pure.model.dto.response.video.EpisodeLikeInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PureVideoInteractionMapper {
    /**
     * 根据分集ID和用户ID查找点赞记录。
     *
     * @param videoEpisodeId 视频分集ID
     * @param userId          用户ID
     * @return 如果存在则返回点赞记录对象，否则返回null
     */
    VideoEpisodesLikes findLikesByEpisodeIdAndUserId(@Param("videoEpisodeId") Long videoEpisodeId, @Param("userId") Long userId);

    /**
     * 插入一条新的视频分集点赞记录。
     *
     * @param like 要插入的点赞记录对象
     * @return 影响的行数
     */
    int insertVideoEpisodesLikes(VideoEpisodesLikes like);

    /**
     * 更新视频分集点赞的状态。
     *
     * @param like 包含ID和新状态的点赞记录对象
     * @return 影响的行数
     */
    int updateVideoEpisodesLikesStatus(VideoEpisodesLikes like);

    /**
     * 获取单个分集的点赞总数和指定用户的点赞状态。
     *
     * @param episodeId 分集ID
     * @param userId    用户ID (可以为null，如果为null，则isLiked字段将为false)
     * @return 包含点赞总数和用户点赞状态的DTO
     */
    EpisodeLikeInfoDTO getEpisodeLikeInfo(@Param("episodeId") Long episodeId, @Param("userId") Long userId);

    /**
     * 查询指定用户在给定分集列表中已点赞（且状态为true）的分集ID。
     *
     * @param userId     用户ID
     * @param episodeIds 分集ID列表
     * @return 用户已点赞的分集ID列表
     */
    List<Long> findLikedEpisodeIdsByUser(@Param("userId") Long userId, @Param("episodeIds") List<Long> episodeIds);






}
