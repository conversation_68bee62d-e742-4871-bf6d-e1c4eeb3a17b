package com.example.pure.mapper.primary;

import com.example.pure.model.entity.ApiKeyLoadBalance;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * API密钥负载均衡状态数据访问层
 * <p>
 * 提供负载均衡状态的CRUD操作
 * </p>
 */
@Mapper
public interface ApiKeyLoadBalanceMapper {

    /**
     * 根据用户ID和提供商查询负载均衡状态
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return 负载均衡状态列表
     */
    List<ApiKeyLoadBalance> selectByUserIdAndProvider(Long userId, String provider);

    /**
     * 根据API密钥ID查询负载均衡状态
     *
     * @param apiKeyId API密钥ID
     * @return 负载均衡状态
     */
    ApiKeyLoadBalance selectByApiKeyId(Long apiKeyId);

    /**
     * 查询健康的API密钥负载状态
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return 健康的负载均衡状态列表
     */
    List<ApiKeyLoadBalance> selectHealthyByUserIdAndProvider(Long userId, String provider);

    /**
     * 插入负载均衡状态
     *
     * @param loadBalance 负载均衡状态
     * @return 影响行数
     */
    int insert(ApiKeyLoadBalance loadBalance);

    /**
     * 更新负载均衡状态
     *
     * @param loadBalance 负载均衡状态
     * @return 影响行数
     */
    int updateById(ApiKeyLoadBalance loadBalance);

    /**
     * 增加当前请求数
     *
     * @param apiKeyId API密钥ID
     * @return 影响行数
     */
    int incrementRequests(Long apiKeyId);

    /**
     * 减少当前请求数
     *
     * @param apiKeyId API密钥ID
     * @return 影响行数
     */
    int decrementRequests(Long apiKeyId);

    /**
     * 增加错误计数
     *
     * @param apiKeyId API密钥ID
     * @return 影响行数
     */
    int incrementErrorCount(Long apiKeyId);

    /**
     * 更新健康状态
     *
     * @param apiKeyId  API密钥ID
     * @param isHealthy 是否健康
     * @return 影响行数
     */
    int updateHealthStatus(Long apiKeyId, Boolean isHealthy);

    /**
     * 删除负载均衡状态
     *
     * @param apiKeyId API密钥ID
     * @return 影响行数
     */
    int deleteByApiKeyId(Long apiKeyId);
}
