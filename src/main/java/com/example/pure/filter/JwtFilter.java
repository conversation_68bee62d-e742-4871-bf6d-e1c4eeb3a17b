package com.example.pure.filter;

import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.JwtAuthenticationException;
import com.example.pure.handler.CustomAuthenticationEntryPoint;
import com.example.pure.security.CustomUserDetailsService;
import com.example.pure.service.auth.DeviceService;
import com.example.pure.util.CookieUtil;
import com.example.pure.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 * 负责处理JWT令牌的验证和用户认证
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final CustomUserDetailsService userDetailsService;
    private final AntPathMatcher pathMatcher;
    private final CookieUtil cookieUtil;
    private final DeviceService deviceService;
    private final CustomAuthenticationEntryPoint authenticationEntryPoint;

    public JwtFilter(
        JwtUtil jwtUtil,
        CustomUserDetailsService userDetailsService,
        CookieUtil cookieUtil,
        DeviceService deviceService,
        CustomAuthenticationEntryPoint authenticationEntryPoint
    ) {
        this.jwtUtil = jwtUtil;
        this.userDetailsService = userDetailsService;
        this.pathMatcher = new AntPathMatcher();
        this.cookieUtil = cookieUtil;
        this.deviceService = deviceService;
        this.authenticationEntryPoint = authenticationEntryPoint;
    }

    /**
     * 判断请求是否需要经过JWT过滤器
     * shouldNotFilter（）方法返回true表示让验证通过，让OncePerRequestFilter跳过后续的doFilterInternal过滤方法
     * shouldNotFilter（）方法返回false表示让验证不通过，让OncePerRequestFilter继续运行后续的doFilterInternal过滤方法验证身份
     *
     * <p>该方法用于确定哪些请求可以跳过JWT token的验证。以下请求将被跳过：
     * <ul>
     *     <li>公开接口（如登录、注册）</li>
     *     <li>Swagger文档接口</li>
     *     <li>预检请求（OPTIONS）</li>
     * </ul>
     * </p>
     *
     * @param request HTTP请求对象
     * @return true-该请求不需要经过过滤器，false-该请求需要经过过滤器
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {


        String path = request.getServletPath();
        // OPENAI兼容API接口跳过验证
        if (pathMatcher.match("/v1/**", path)) {
            return true; // 跳过JWT过滤器，直接放行
        }

        // 携带了Token的用户，全部请求都需要验证token，并把用户信息存储到SecurityContextHolder中
        if (StringUtils.hasText(extractJwtFromRequest(request)) ) {
            // 返回false，表示"不应该"跳过此过滤器，即需要执行doFilterInternal。
            return false;
        }

        // 如果请求中没有Token，再判断是否为public访问路径，这些路径可以匿名访问。
        // 检查是否是public访问路径
        // pattern是路径模式
        for (String pattern : SecurityConstants.PUBLIC_URLS) {
            //路径匹配相同就设置变量为true就可以不经过Filter，运行Api
            if (pathMatcher.match(pattern, path)) {
                // 是公开路径且无Token，返回true，表示应该跳过此过滤器。
                return true;
            }
        }

        // 检查是否是Swagger文档相关路径
        for (String pattern : SecurityConstants.SWAGGER_URLS) {
            if (pathMatcher.match(pattern, path)) {
                // 是Swagger路径且无Token，返回true，表示应该跳过此过滤器。
                return true;
            }
        }

        // 检查是否是预检请求（OPTIONS请求）
        if (request.getMethod().equals(HttpMethod.OPTIONS.name())) {
            // 是预检请求，返回true，表示应该跳过此过滤器。
            return true;
        }

        // 对于其他所有情况（即非public路径且没有Token），应执行过滤器，
        // 过滤器会因缺少Token而正确地拒绝访问。
        return false;
    }

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws ServletException, IOException {

        try {
            //提取token
            String jwt = extractJwtFromRequest(request);

            if (StringUtils.hasText(jwt) && jwtUtil.validateToken(jwt)) {
                //从Jwt令牌提取用户名
                String username = jwtUtil.getUsernameFromToken(jwt);

                 /*
                  获取uuid配合用户名查询当前用户绑定设备:从Redis里token作为key获取到value（设备id或者uuid）然后拿来验证设备
                  查询已绑定设备:Redis里hash的key的user:devices:{username}和列（设备id或者uuid）查询到结果才能通过 */

                if (!deviceService.isAccessTokenValid(username, jwt)) {
                    log.warn("用户 '{}' 的Token无效，此设备已失效", username);
                    throw JwtAuthenticationException.deviceLimit();
                }
                //用用户名从数据库中重新加载完整的用户信息（包含权限），也能从令牌提取权限来让用户通过，但是为了安全防止令牌被篡改，所以要从数据库来识别
                if (SecurityContextHolder.getContext().getAuthentication() == null) {
                    UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                /*
                 * 类里面的（）里有参数表示构造函数，所以下面3个参数已经注入到类的对象里了
                 * 用户信息详情，凭证，返回用户的权限信息Spring Security将使用这些权限来进行授权检查
                 * 创建一个 WebAuthenticationDetails 对象，将HTTP请求的IP地址、会话ID等相关信息存储到对象中，在存储到令牌里
                 * 将认证信息存储到spring Security的SecurityContextHolder中（存储用户认证信息的核心类），存储在 SecurityContextHolder 中的认证信息会在整个请求过程中可用
                 */

                // 创建认证令牌
                    UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities()
                        );
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    log.debug("用户 '{}' 认证成功", username);
                }
            }
            // 如果所有验证都通过，继续执行过滤器链
            filterChain.doFilter(request, response);
        } catch (JwtAuthenticationException e) {
            // 对于我们自定义的JWT异常，委托给AuthenticationEntryPoint处理
            SecurityContextHolder.clearContext(); // 清除上下文
            this.authenticationEntryPoint.commence(request, response, e);
        }

         //传递request和response对象传递给下一个目标资源给Servlet，Servlet再调用控制器
        // 总是继续过滤链.
        // 如果一个异常抛出，我们会通过spring security的ExceptionTranslationFilter来处理，然后调用我们的CustomAuthenticationEntryPoint.
        // 如果身份验证成功，SecurityContext会被填充，后续的过滤器/控制器可以使用它。

    }

    /**
     * 从请求中提取JWT令牌
     * 按以下顺序尝试获取令牌：
     * 1. Authorization头（Bearer token）
     * 2. Cookie中的访问令牌
     *
     * @param request HTTP请求对象
     * @return JWT令牌，如果不存在则返回null
     */
    private String extractJwtFromRequest(HttpServletRequest request) {
        // 尝试从Authorization头获取
        String bearerToken = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(bearerToken) ) {
            //去掉字符串里前7个字符'Bearer '，并返回token
            return jwtUtil.TokenCheck(bearerToken);
        }

        // 尝试从Cookie获取
        String cookieToken = cookieUtil.getAccessTokenFromCookies(request);
        if (StringUtils.hasText(cookieToken)) {
            return cookieToken;
        }

        return null;
    }
}
