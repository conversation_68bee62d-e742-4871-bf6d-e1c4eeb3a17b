<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 用户API密钥Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.UserApiKeyMapper">

    <!-- 用户API密钥结果映射 -->
    <resultMap id="UserApiKeyResultMap" type="com.example.pure.model.entity.UserApiKey">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="provider" column="provider"/>
        <result property="keyName" column="key_name"/>
        <result property="apiKeyEncrypted" column="api_key_encrypted"/>
        <result property="isActive" column="is_active"/>
        <result property="priority" column="priority"/>
        <result property="usageCount" column="usage_count"/>
        <result property="lastUsedAt" column="last_used_at"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, provider, key_name, api_key_encrypted,
        is_active, priority, usage_count, last_used_at,
        created_at, updated_at
    </sql>

    <!-- 根据ID查询API密钥 -->
    <select id="selectById" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询所有API密钥 -->
    <select id="selectByUserId" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys
        WHERE user_id = #{userId}
        ORDER BY priority ASC, created_at ASC
    </select>

    <!-- 根据用户ID和提供商查询API密钥 -->
    <select id="selectByUserIdAndProvider" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys
        WHERE user_id = #{userId} AND provider = #{provider}
        ORDER BY priority ASC, created_at ASC
    </select>

    <!-- 查询用户的活跃API密钥 -->
    <select id="selectActiveByUserIdAndProvider" resultMap="UserApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_api_keys
        WHERE user_id = #{userId} AND provider = #{provider}
        AND is_active = TRUE
        ORDER BY priority ASC, created_at ASC
    </select>

    <!-- 插入API密钥 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.UserApiKey"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO user_api_keys (
            user_id, provider, key_name, api_key_encrypted,
            is_active, priority
        ) VALUES (
            #{userId}, #{provider}, #{keyName}, #{apiKeyEncrypted},
            #{isActive}, #{priority}
        )
    </insert>

    <!-- 更新API密钥 -->
    <update id="updateById" parameterType="com.example.pure.model.entity.UserApiKey">
        UPDATE user_api_keys
        SET key_name = #{keyName},
            api_key_encrypted = #{apiKeyEncrypted},
            is_active = #{isActive},
            priority = #{priority},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新API密钥使用统计 -->
    <update id="updateUsageStats">
        UPDATE user_api_keys
        SET usage_count = usage_count + 1,
            last_used_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除API密钥 -->
    <delete id="deleteById">
        DELETE FROM user_api_keys
        WHERE id = #{id}
    </delete>

    <!-- 根据用户ID删除所有API密钥 -->
    <delete id="deleteByUserId">
        DELETE FROM user_api_keys
        WHERE user_id = #{userId}
    </delete>
</mapper>
