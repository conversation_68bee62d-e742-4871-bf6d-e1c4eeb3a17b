<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.pure.mapper.primary.VideoCommentInteractionMapper">

    <!-- 插入新的评论 -->
    <insert id="insertVideoComments" parameterType="com.example.pure.model.dto.request.videocomment.VideoCommentRequest" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        INSERT INTO video_comments(
            video_episodes_id, user_id, parent_comment_id, content, created_time, updated_time
        )
        VALUES (
                   #{videoEpisodesId}, #{userId}, #{parentCommentId}, #{content}, NOW(), NOW()
               )
    </insert>

    <!-- 获取评论 -->
    <select id="getBasicComments" resultType="com.example.pure.model.dto.response.videocomment.VideoCommentsDTO">
        SELECT
            vc.id, vc.video_episodes_id, vc.user_id, vc.parent_comment_id,
            vc.content, vc.created_time, vc.updated_time,
            up.username, up.avatar
        FROM video_comments vc
                 INNER JOIN user_profile up ON vc.user_id = up.id
        WHERE vc.video_episodes_id = #{videoEpisodesId}
        ORDER BY vc.created_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getCommentsLikeData" resultType="com.example.pure.model.dto.response.videocomment.CommentLikeData">
        SELECT
        vcl.comment_id,
        COUNT(CASE WHEN vcl.type = 'like' AND vcl.status = true THEN 1 END) as likes_count,
        COUNT(CASE WHEN vcl.type = 'dislike' AND vcl.status = true THEN 1 END) as dislikes_count,
        MAX(CASE WHEN vcl.user_id = #{userId} AND vcl.status = true THEN vcl.type END) as user_like_type
        FROM video_comment_likes vcl
        WHERE vcl.comment_id IN
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY vcl.comment_id
    </select>

    <!-- 删除评论 -->
    <delete id="deleteComment">
        DELETE FROM video_comments WHERE id = #{commentId}
    </delete>

    <!-- 根据评论ID获取用户ID -->
    <select id="getUserIdByCommentId" resultType="java.lang.Long">
        SELECT user_id
        FROM video_comments
        WHERE id = #{commentId}
    </select>

    <!--根据评论ID和用户ID查找点赞记录-->
    <select id="findByCommentIdAndUserId" resultType="com.example.pure.model.entity.VideoCommentLikes">
        SELECT *
        FROM video_comment_likes
        WHERE comment_id = #{commentId}
          AND user_id = #{userId}
    </select>

    <!--用户第一次点赞创建点赞记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO video_comment_likes (comment_id, user_id, status, type, created_time, updated_time)
        VALUES (#{commentId}, #{userId}, #{status}, #{type}, #{createdTime}, #{updatedTime})
    </insert>

    <!--更新点赞状态-->
    <update id="update">
        UPDATE video_comment_likes
        SET status = #{status},
            type = #{type},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <!--统计点赞数量和是否已经点赞-->
    <select id="countByCommentIdAndTypeAndStatus" resultType="long">
        SELECT count(*)
        FROM video_comment_likes
        WHERE comment_id = #{commentId}
          AND type = #{type}
          AND status = #{status}
    </select>
</mapper>
