<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 聊天会话Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.ChatSessionMapper">

    <!-- 聊天会话结果映射 -->
    <resultMap id="ChatSessionResultMap" type="com.example.pure.model.entity.ChatSession">
        <id property="sessionId" column="session_id"/>
        <result property="userId" column="user_id"/>
        <result property="model" column="model"/>
        <result property="provider" column="provider"/>
        <result property="apiKeyId" column="api_key_id"/>
        <result property="temperature" column="temperature"/>
        <result property="maxTokens" column="max_tokens"/>
        <result property="customPrompt" column="custom_prompt"/>
        <result property="messageCount" column="message_count"/>
        <result property="expiresAt" column="expires_at"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        session_id, user_id, model, provider, api_key_id,
        temperature, max_tokens, custom_prompt, message_count,
        expires_at, created_at
    </sql>

    <!-- 根据会话ID查询会话信息 -->
    <select id="selectBySessionId" resultMap="ChatSessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_sessions
        WHERE session_id = #{sessionId}
    </select>

    <!-- 根据用户ID查询会话列表 -->
    <select id="selectByUserId" resultMap="ChatSessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM chat_sessions
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 查询用户的活跃会话（未过期） -->
    <select id="selectActiveByUserId" resultMap="ChatSessionResultMap">
        <![CDATA[
        SELECT session_id, user_id, model, provider, api_key_id,
               temperature, max_tokens, custom_prompt, message_count,
               expires_at, created_at
        FROM chat_sessions
        WHERE user_id = #{userId}
        AND (expires_at IS NULL OR expires_at > NOW())
        ORDER BY created_at DESC
        ]]>
    </select>

    <!-- 插入会话信息 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.ChatSession">
        INSERT INTO chat_sessions (
            session_id, user_id, model, provider, api_key_id,
            temperature, max_tokens, custom_prompt, message_count, expires_at
        ) VALUES (
            #{sessionId}, #{userId}, #{model}, #{provider}, #{apiKeyId},
            #{temperature}, #{maxTokens}, #{customPrompt}, #{messageCount}, #{expiresAt}
        )
    </insert>

    <!-- 更新会话信息 -->
    <update id="updateBySessionId" parameterType="com.example.pure.model.entity.ChatSession">
        UPDATE chat_sessions
        SET model = #{model},
            provider = #{provider},
            api_key_id = #{apiKeyId},
            temperature = #{temperature},
            max_tokens = #{maxTokens},
            custom_prompt = #{customPrompt},
            message_count = #{messageCount},
            expires_at = #{expiresAt}
        WHERE session_id = #{sessionId}
    </update>

    <!-- 增加消息计数 -->
    <update id="incrementMessageCount">
        UPDATE chat_sessions
        SET message_count = message_count + 1
        WHERE session_id = #{sessionId}
    </update>

    <!-- 删除会话 -->
    <delete id="deleteBySessionId">
        DELETE FROM chat_sessions
        WHERE session_id = #{sessionId}
    </delete>

    <!-- 根据用户ID删除所有会话 -->
    <delete id="deleteByUserId">
        DELETE FROM chat_sessions
        WHERE user_id = #{userId}
    </delete>

    <!-- 删除过期会话 -->
    <delete id="deleteExpiredSessions">
        <![CDATA[
        DELETE FROM chat_sessions
        WHERE expires_at IS NOT NULL AND expires_at < NOW()
        ]]>
    </delete>

    <!-- 检查会话是否存在 -->
    <select id="existsBySessionId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM chat_sessions
        WHERE session_id = #{sessionId}
    </select>
</mapper>
