<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 用户AI配置Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.UserAiConfigMapper">

    <!-- 用户AI配置结果映射 -->
    <resultMap id="UserAiConfigResultMap" type="com.example.pure.model.entity.UserAiConfig">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="preferredModel" column="preferred_model"/>
        <result property="defaultTemperature" column="default_temperature"/>
        <result property="defaultMaxTokens" column="default_max_tokens"/>
        <result property="defaultTopP" column="default_top_p"/>
        <result property="streamEnabled" column="stream_enabled"/>
        <result property="timeoutSeconds" column="timeout_seconds"/>
        <result property="systemPrompt" column="system_prompt"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, preferred_model, default_temperature, default_max_tokens,
        default_top_p, stream_enabled, timeout_seconds, system_prompt,
        created_at, updated_at
    </sql>

    <!-- 根据用户ID查询AI配置 -->
    <select id="selectByUserId" resultMap="UserAiConfigResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_ai_config
        WHERE user_id = #{userId}
    </select>

    <!-- 插入用户AI配置 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.UserAiConfig"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO user_ai_config (
            user_id, preferred_model, default_temperature,
            default_max_tokens, default_top_p, stream_enabled,
            timeout_seconds, system_prompt
        ) VALUES (
            #{userId}, #{preferredModel}, #{defaultTemperature},
            #{defaultMaxTokens}, #{defaultTopP}, #{streamEnabled},
            #{timeoutSeconds}, #{systemPrompt}
        )
    </insert>

    <!-- 更新用户AI配置 -->
    <update id="updateByUserId" parameterType="com.example.pure.model.entity.UserAiConfig">
        UPDATE user_ai_config
        SET preferred_model = #{preferredModel},
            default_temperature = #{defaultTemperature},
            default_max_tokens = #{defaultMaxTokens},
            default_top_p = #{defaultTopP},
            stream_enabled = #{streamEnabled},
            timeout_seconds = #{timeoutSeconds},
            system_prompt = #{systemPrompt},
            updated_at = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 根据用户ID删除AI配置 -->
    <delete id="deleteByUserId">
        DELETE FROM user_ai_config
        WHERE user_id = #{userId}
    </delete>

    <!-- 检查用户是否已有AI配置 -->
    <select id="existsByUserId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_ai_config
        WHERE user_id = #{userId}
    </select>
</mapper>
