<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.PureVideoInteractionMapper">


    <!-- 根据分集ID和用户ID查找点赞记录 -->
    <select id="findLikesByEpisodeIdAndUserId" resultType="com.example.pure.model.entity.VideoEpisodesLikes">
        SELECT * FROM video_episodes_likes
        WHERE video_episode_id = #{videoEpisodeId} AND user_id = #{userId}
    </select>

    <!-- 插入新的分集点赞记录 -->
    <insert id="insertVideoEpisodesLikes" parameterType="com.example.pure.model.entity.VideoEpisodesLikes" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO video_episodes_likes (video_episode_id, user_id, status, created_time, updated_time)
        VALUES (#{videoEpisodeId}, #{userId}, #{status}, #{createdTime}, #{updatedTime})
    </insert>

    <!-- 更新分集点赞的状态 -->
    <update id="updateVideoEpisodesLikesStatus" parameterType="com.example.pure.model.entity.VideoEpisodesLikes">
        UPDATE video_episodes_likes
        SET status = #{status}, updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <!-- 获取单个分集的点赞总数和指定用户的点赞状态 -->
    <select id="getEpisodeLikeInfo" resultType="com.example.pure.model.dto.response.video.EpisodeLikeInfoDTO">
        SELECT
            -- 计算总的点赞数：只计算status为true的记录
            COUNT(CASE WHEN status = true THEN 1 END) as likesCount,

            -- 判断当前用户是否点赞
            <choose>
                <when test="userId != null">
                    -- 如果用户已登录，使用COALESCE将SUM可能返回的NULL转为0，再进行判断,>0为了把数字变成布尔值
                    COALESCE(SUM(CASE WHEN user_id = #{userId} AND status = true THEN 1 ELSE 0 END), 0) > 0 as status
                </when>
                <otherwise>
                    -- 如果用户未登录，直接返回false
                    false as status
                </otherwise>
            </choose>
        FROM
            video_episodes_likes
        WHERE
            video_episode_id = #{episodeId}
    </select>

    <!-- 查询指定用户在给定分集列表中已点赞的分集ID -->
    <select id="findLikedEpisodeIdsByUser" resultType="long">
        SELECT video_episode_id
        FROM video_episodes_likes
        WHERE user_id = #{userId}
        AND status = true
        AND video_episode_id IN
        <foreach item="episodeId" collection="episodeIds" open="(" separator="," close=")">
            #{episodeId}
        </foreach>
    </select>










</mapper>
