<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.MessagesMapper">
    <!-- SQL statements will be added later based on service layer requirements -->

    <!-- 插入消息到 messages 表 -->
    <insert id="insertMessage" parameterType="com.example.pure.model.entity.Message" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO messages (sender_id, target_type, target_id, title, content, message_type, created_time)
        VALUES (#{senderId}, #{targetType}, #{targetId}, #{title}, #{content}, #{messageType}, #{createdTime})
    </insert>

    <!-- 插入消息到 user_messages 表 -->
    <insert id="insertUserMessage" parameterType="com.example.pure.model.entity.UserMessage">
        INSERT INTO user_messages (user_id, message_id, is_read, read_time)
        VALUES (#{userId}, #{messageId}, #{isRead}, #{readTime})
    </insert>

    <!-- 统计指定用户未读消息的数量 -->
    <select id="countUnreadMessagesByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM user_messages
        WHERE user_id = #{userId} AND is_read = false
    </select>
    <!-- 将指定用户的所有未读消息标记为已读 -->
    <update id="markAllMessagesAsReadByUserId">
        UPDATE user_messages
        SET is_read = true, read_time = #{readTime}
        WHERE user_id = #{userId} AND is_read = false
    </update>
<!-- 将指定用户的特定消息标记为已读，并记录读取时间 -->
    <update id="markMessageAsReadByUserIdAndMessageId">
        UPDATE user_messages
        SET is_read = true, read_time = #{readTime}
        WHERE user_id = #{userId} AND message_id = #{messageId} AND is_read = false
    </update>

    <!-- 分页查询用户消息详情 -->
    <select id="selectUserMessagesWithDetails" resultType="com.example.pure.model.dto.response.messages.MessageDTO">
        SELECT
            m.id,
            m.sender_id AS senderId,
            m.title,
            m.content,
            m.message_type AS messageType,
            m.created_time AS createdTime,
            um.is_read AS isRead,
            um.read_time AS readTime
        FROM
            messages m
        JOIN
            user_messages um ON m.id = um.message_id
        WHERE
            um.user_id = #{userId}
            <if test="status != null and status == 'unread'">
                AND um.is_read = false
            </if>
            <if test="status != null and status == 'read'">
                AND um.is_read = true
            </if>
        ORDER BY
            m.created_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计符合条件的用户消息总数 -->
    <select id="countUserMessagesWithDetails" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            messages m
        JOIN
            user_messages um ON m.id = um.message_id
        WHERE
            um.user_id = #{userId}
            <if test="status != null and status == 'unread'">
                AND um.is_read = false
            </if>
            <if test="status != null and status == 'read'">
                AND um.is_read = true
            </if>
    </select>


    <delete id="deleteReadedMessagesByUserId">
        DELETE m,um FROM messages m
        INNER JOIN user_messages um ON m.id = um.message_id
        WHERE um.user_id = #{userId}
    </delete>

    <delete id="deleteMessageById">
        DELETE m,um FROM messages m
        INNER JOIN user_messages um ON m.id = um.message_id
        WHERE um.user_id = #{userId} AND m.id = #{messageId}
    </delete>

</mapper>
