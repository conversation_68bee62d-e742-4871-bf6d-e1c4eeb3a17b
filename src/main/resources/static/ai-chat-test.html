<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #fafafa;
            border-radius: 5px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
            margin-left: 20%;
        }
        .ai-message {
            background-color: #e9ecef;
            color: #333;
            margin-right: 20%;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        #messageInput {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        #sendButton {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        #sendButton:hover {
            background-color: #0056b3;
        }
        #sendButton:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .typing-indicator {
            font-style: italic;
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>AI聊天测试页面</h1>
        <div class="chat-messages" id="chatMessages"></div>
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="输入您的消息..." />
            <button id="sendButton">发送</button>
        </div>
        <div class="status" id="status">准备就绪</div>
    </div>

    <script>
        class AiChatClient {
            constructor() {
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.status = document.getElementById('status');
                this.sessionId = null;
                this.currentEventSource = null;

                this.init();
            }

            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });

                // 生成新的会话ID
                this.generateSessionId();
            }

            async generateSessionId() {
                try {
                    const response = await fetch('/api/ai-chat/session/new');
                    const result = await response.json();
                    if (result.success) {
                        this.sessionId = result.data;
                        this.updateStatus('已连接，会话ID: ' + this.sessionId, 'connected');
                    } else {
                        this.updateStatus('获取会话ID失败: ' + result.message, 'error');
                    }
                } catch (error) {
                    this.updateStatus('连接失败: ' + error.message, 'error');
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || !this.sessionId) return;

                // 显示用户消息
                this.addMessage(message, 'user');
                this.messageInput.value = '';
                this.sendButton.disabled = true;

                // 显示AI正在输入
                const typingDiv = this.addTypingIndicator();

                try {
                    // 发送SSE请求
                    const response = await fetch('/api/ai-chat/stream', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            sessionId: this.sessionId,
                            messageType: 'chat'
                        })
                    });

                    if (!response.ok) {
                        throw new Error('请求失败: ' + response.status);
                    }

                    // 处理SSE流
                    this.handleSSEStream(response, typingDiv);

                } catch (error) {
                    this.removeTypingIndicator(typingDiv);
                    this.addMessage('错误: ' + error.message, 'ai');
                    this.updateStatus('发送失败: ' + error.message, 'error');
                } finally {
                    this.sendButton.disabled = false;
                }
            }

            async handleSSEStream(response, typingDiv) {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let aiMessageDiv = null;

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.substring(6);
                                if (data.trim() === '') continue;

                                try {
                                    const messageData = JSON.parse(data);

                                    if (messageData.type === 'start') {
                                        // 开始事件
                                        this.updateStatus('AI开始响应...', 'connected');

                                    } else if (messageData.type === 'text') {
                                        // 移除打字指示器（如果还存在）
                                        if (typingDiv) {
                                            this.removeTypingIndicator(typingDiv);
                                            typingDiv = null;
                                        }

                                        // 创建或更新AI消息
                                        if (!aiMessageDiv) {
                                            aiMessageDiv = this.addMessage('', 'ai');
                                        }
                                        aiMessageDiv.textContent = messageData.content;
                                        this.scrollToBottom();

                                    } else if (messageData.type === 'end') {
                                        // 流结束
                                        this.updateStatus('消息接收完成', 'connected');
                                        break;

                                    } else if (messageData.type === 'error') {
                                        // 错误处理
                                        if (typingDiv) {
                                            this.removeTypingIndicator(typingDiv);
                                        }
                                        this.addMessage('AI错误: ' + messageData.error, 'ai');
                                        this.updateStatus('AI响应错误', 'error');
                                        break;
                                    }
                                } catch (parseError) {
                                    console.error('解析SSE数据失败:', parseError);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('读取SSE流失败:', error);
                    if (typingDiv) {
                        this.removeTypingIndicator(typingDiv);
                    }
                    this.addMessage('接收消息时发生错误', 'ai');
                } finally {
                    reader.releaseLock();
                }
            }

            addMessage(content, type) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}-message`;
                messageDiv.textContent = content;
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
                return messageDiv;
            }

            addTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'typing-indicator';
                typingDiv.textContent = 'AI正在输入...';
                this.chatMessages.appendChild(typingDiv);
                this.scrollToBottom();
                return typingDiv;
            }

            removeTypingIndicator(typingDiv) {
                if (typingDiv && typingDiv.parentNode) {
                    typingDiv.parentNode.removeChild(typingDiv);
                }
            }

            updateStatus(message, type) {
                this.status.textContent = message;
                this.status.className = `status ${type}`;
            }

            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
        }

        // 初始化聊天客户端
        document.addEventListener('DOMContentLoaded', () => {
            new AiChatClient();
        });
    </script>
</body>
</html>
