<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <!-- 变量定义 -->
    <property name="LOG_PATH" value="logs"/>
    <!--%-5level：左对齐并固定宽度为 5 字符，高亮显示日志级别（如 INFO 绿色，[%thread]显示当前线程名，-->
    <!--%logger{36}显示日志来源的类名，并限制字符最大长度，%msg：日志的具体消息内容。%n：换行符（兼容不同操作系统的换行格式）-->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight(%-5level) ${PID:- } --- [%thread] %logger{36} : %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <target>System.out</target>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，只输出Debug级别及以上的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>Debug</level>
        </filter>
    </appender>

    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--存放在JAR文件的logs里以application.log文件名存放-->
        <file>${LOG_PATH}/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 按天轮转，超过100MB则新建文件，日志仅保留30天，所有日志总大小3GB -->
            <fileNamePattern>${LOG_PATH}/application-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 异步输出 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 关闭 RMI 相关日志 -->
    <logger name="sun.rmi" level="ERROR"/>
    <logger name="javax.management" level="ERROR"/>

    <!-- 应用日志配置 -->
    <logger name="com.example.pure" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
    </logger>

    <!-- 框架日志级别 -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.mybatis" level="INFO"/>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
    </root>
</configuration>
