# ========================
# 通用配置 - 所有环境共享
# ========================

# @value注解查找值优先级,隐私和想要从docker的.env手动替换才用${VAR};
#1.命令行参数 2.环境变量 3.application-{profile}.yml 4.application.yml

# ========================
# 服务器配置
# ========================
server:
  port: 8080  # 应用服务端口
  compression:  # 响应压缩配置
    enabled: true  # 启用压缩
    mime-types: application/json,application/xml,text/html,text/plain  # 压缩类型
    min-response-size: 1024  # 最小压缩大小（字节）
  tomcat:  # Tomcat连接池配置
    max-threads: 200  # 最大工作线程数
    min-spare-threads: 10  # 最小空闲线程
    max-connections: 10000  # 最大连接数
    accept-count: 100  # 等待队列长度
    connection-timeout: 30000  # 连接超时(ms)
    uri-encoding: UTF-8

# ========================
# Spring核心配置
# ========================
spring:
  profiles: #springboot和java原生不支持.env注入${}的值里,如果没有注入${}值就会用默认(:)的值,.env只有docker容器环境使用
    active: ${SPRING_PROFILES_ACTIVE:dev}  # 默认开发环境，可通过环境变量覆盖
  jmx:
    enabled: false  # 禁用JMX监控
  mvc:
    async:
      request-timeout: 1800000  # 30 minutes in milliseconds

  # ========================
  # 安全配置
  # ========================
  security:
    user:  # 内存中的默认用户
      name: admin  # 管理员用户名
      password: admin  # 管理员密码

  # ========================
  # 文件上传配置
  # ========================
  servlet:
    multipart:
      max-file-size: 10GB  # 单个文件最大尺寸
      max-request-size: 10GB  # 请求最大尺寸
      enabled: true

  # ========================
  # 缓存配置
  # ========================
  cache:
    type: redis  # 使用Redis缓存
    redis:
      time-to-live: 3600000  # 缓存过期时间(ms)
      cache-null-values: true  # 是否缓存空值


# ========================
# 业务配置 - 通用部分
# ========================

# 验证码配置
verification:
  code:
    length: 6  # 验证码长度
    expiration: 360  # 验证码有效期(秒)

# ========================
# MyBatis配置
# ========================
mybatis:
  mapper-locations: classpath:mapper/*.xml  # XML映射文件路径
  type-aliases-package: com.example.pure.model  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换
    cache-enabled: true  # 启用二级缓存
    lazy-loading-enabled: true  # 启用延迟加载
    aggressive-lazy-loading: false  # 禁用侵略性延迟加载
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 日志实现

# ========================
# 二维码登录配置
# ========================
qrlogin:
  redis:
    expire-seconds: 300  # 二维码在Redis中的过期时间(秒)
  timeout:
    display: 120  # 二维码显示的有效期(秒)
    cleanup: 300  # 二维码清理的超时时间(秒)

# ========================
# 文件存储配置
# ========================
sprite:
  generation:
    path: ${SPRITE_PATH:d:/upload/video_sprites}  # 视频精灵图生成路径

file:
  download:
    max-retries: 3
    retry-delay: 1000
  storage:
    location:
      download: ${FILE_DOWNLOAD_PATH:D:/AAA}
      upload: ${FILE_UPLOAD_PATH:D:/upload}
    max-size: 1073741824 #1GB
# ========================
# API文档配置
# ========================
springdoc:
  swagger-ui:
    path: /swagger-ui.html  # Swagger UI路径
  api-docs:
    path: /v3/api-docs  # OpenAPI文档路径
  packages-to-scan: com.example.pure.controller  # 扫描的控制器包
  #匹配的API路径
  paths-to-match: >
    /api/user/**,/api/auth/**,/api/verification/**,/api/files/image/**,
    /api/view/images/**,/api/user-profile/**,/api/messages/**,/api/video/interaction/**,
    /api/videoUrl/**,/api/messages/**,/oauth/**,/upload-general/**,/api/view/images/**,
    /api/qrcode/**,/api/qrlogin/**,/api/user-profile/**,/api/ws-test/**,/api/video/comments/**,
    /api/ai-chat/**,/api/ai/config/**,/v1/**

# ========================
# Knife4j增强配置,增强SpringDoc文档展示
# ========================
knife4j:
  enable: true  # 启用Knife4j
  setting:
    language: zh-CN  # 中文界面
    swagger-model-name: 实体类列表  # 模型名称
    enable-footer: false  # 禁用页脚
  basic:
    enable: false  # 禁用基础认证
    username: admin  # 文档访问账号
    password: admin  # 文档访问密码
  openapi:
    title: Spring Boot REST API  # 文档标题
    description: Spring Boot REST API with JWT Authentication  # 文档描述
    email: <EMAIL>  # 联系人邮箱
    version: 1.0  # API版本
    license: Apache 2.0  # 许可证
    license-url: https://springdoc.org  # 许可证链接
    terms-of-service: https://springdoc.org  # 服务条款
    group:  # 分组配置
      test1:
        group-name: 分组名称
        api-rule: package
        api-rule-resources:
          - com.example.pure.controller

# ========================
# Actuator监控配置
# ========================
management:
  endpoints:
    enabled-by-default: true  # 默认启用所有端点
    web:
      exposure:
        include: "*"  # 暴露所有Web端点
      base-path: /actuator  # 端点基础路径
    jmx:
      enabled: false  # 禁用JMX端点

  endpoint:
    health:
      show-details: always  # 显示健康详情
      show-components: always  # 显示组件详情
    prometheus:
      enabled: true  # 启用Prometheus端点
    loggers:
      enabled: true  # 启用日志级别端点
    threaddump:
      enabled: true  # 启用线程转储端点

  info:
    env:
      enabled: true  # 显示环境信息
    git:
      mode: full  # 完整Git信息
    java:
      enabled: true  # 显示Java信息

  metrics:
    tags:
      application: ${spring.application.name}  # 指标应用标签
    export:
      prometheus:
        enabled: true  # 启用Prometheus导出

  health:
    defaults:
      enabled: true  # 启用默认健康检查

# ========================
# 应用信息配置
# ========================
info:
  app:
    name: Pure Application  # 应用名称
    description: Spring Boot Pure Application  # 应用描述
    version: 1.0.0  # 应用版本
    java:
      version: ${java.version}  # Java版本(自动获取)
    spring-boot:
      version: ${spring-boot.version}  # Spring Boot版本(自动获取)

# ========================
# 雪花算法配置
# ========================
snowflake:
  datacenter-id: ${SNOWFLAKE_DATACENTER_ID:1}  # 数据中心ID
  worker-id: ${SNOWFLAKE_WORKER_ID:1}  # 工作机器ID

# ========================
# AI大模型API转发系统配置
# ========================
ai:
  # 安全配置
  security:
    max-timeout: 300000        # 最大超时限制（5分钟）
    max-tokens-limit: 32768    # 最大token硬限制
    rate-limit:
      requests-per-minute: 60  # 每分钟最大请求数

  # 加密配置
  encryption:
    secret: ${AI_ENCRYPTION_SECRET:default-secret-key-for-ai-api-encryption}  # API密钥加密密钥
    key-rotation-days: 30      # API Key轮换周期

  # AI提供商配置
  providers:
    openai:
      base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
      models:
        - "gpt-3.5-turbo"
        - "gpt-4"
        - "gpt-4-turbo"
        - "gpt-4o"
        - "gpt-4o-mini"
      timeout: 120000  # 请求超时时间(ms) - 增加到2分钟
    anthropic:
      base-url: ${ANTHROPIC_BASE_URL:https://api.anthropic.com/v1}
      models:
        - "claude-3-sonnet"
        - "claude-3-opus"
        - "claude-3-haiku"
        - "claude-3-5-sonnet"
        - "claude-3-5-haiku"
        - "claude-opus-4-20250514"
        - "claude-sonnet-4-20250514"
      timeout: 120000  # 请求超时时间(ms) - 增加到2分钟
      # 使用 OpenAI 兼容格式
      openai-compatible: true
    google:
      base-url: ${GOOGLE_AI_BASE_URL:https://generativelanguage.googleapis.com/v1beta/openai}
      models:
        - "gemini-pro"
        - "gemini-pro-vision"
        - "gemini-1.5-pro"
        - "gemini-1.5-flash"
        - "gemini-2.0-flash-exp"
        - "gemini-2.5-flash"
        - "gemini-2.5-pro"
      timeout: 120000  # 请求超时时间(ms) - 增加到2分钟
      # 使用 OpenAI 兼容格式
      openai-compatible: true

  # 负载均衡配置
  load-balance:
    max-error-count: 5         # 最大错误次数阈值
    max-error-rate: 0.1        # 最大错误率阈值（10%）
    max-concurrent-requests: 10 # 最大并发请求数
    health-check-interval: 300  # 健康检查间隔(秒)

  # 默认配置
  defaults:
    model: "gpt-3.5-turbo"
    temperature: 0.7
    max-tokens: 8192  # 增加默认token数以支持更长的响应
    top-p: 1.0
    stream-enabled: true
    timeout-seconds: 120  # 增加超时时间以支持更长的流式响应
