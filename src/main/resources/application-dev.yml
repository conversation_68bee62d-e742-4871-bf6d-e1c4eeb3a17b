# ========================
# 开发环境配置
# ========================

spring:
  # ========================
  # 应用名称配置
  # ========================
  application:
    name: ${SPRING_APPLICATION_NAME:pure}
  # ========================
  # 数据源配置 - 开发环境
  # ========================
  datasource:
    primary: #springboot和java原生不支持.env注入${}的值里,如果没有注入${}值就会用默认(:)的值,.env只有docker容器环境使用
      url: jdbc:mysql://${DB_HOST:127.0.0.1}:${DB_PORT:3307}/${DB_NAME:pure}?useUnicode=true&characterEncoding=UTF-8&serverTimezone=UTC
      username: ${DB_USERNAME:root} #在docker环境使用.env文件的变量从docker compose导入进来
      password: ${DB_PASSWORD:123456}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: HikariPool-Primary
        maximum-pool-size: ${DB_POOL_MAX_SIZE:10}
        minimum-idle: 5
        idle-timeout: 300000
        connection-timeout: 20000
        max-lifetime: 1200000
    secondary:
      url: jdbc:mysql://${DB_SECONDARY_HOST:localhost}:${DB_SECONDARY_PORT:3306}/school2?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      username: ${DB_SECONDARY_USERNAME:hao}
      password: ${DB_SECONDARY_PASSWORD:12345678}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: HikariPool-Secondary
        maximum-pool-size: 10
        minimum-idle: 5
        idle-timeout: 300000
        connection-timeout: 20000
        max-lifetime: 1200000

  # ========================
  # Redis配置 - 开发环境
  # ========================
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:2}
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

  # ========================
  # 邮件配置 - 开发环境
  # ========================
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:fxyopjthfslpbbie}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    protocol: smtp
    default-encoding: UTF-8

# ========================
# JWT配置 - 开发环境
# ========================
jwt:
  secret: ${JWT_SECRET:aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa}
  expiration: ${JWT_EXPIRATION:604800}  # 7天
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:1209600}  # 14天


backend:
  url: ${BACKEND_URL:http://localhost:8080}

frontend:
  url: ${FRONTEND_URL:http://localhost:8848}

# ========================
# Cloudflare R2配置 - 开发环境
# ========================
cloudflare:
  r2:
    endpoint: ${CLOUDFLARE_R2_ENDPOINT:https://076ef3ccd15e2e7a449f7d712317824b.r2.cloudflarestorage.com}
    access-key-id: ${CLOUDFLARE_R2_ACCESS_KEY_ID:7a11146fa887bbe862d0c7b5c44acf8e}
    secret-access-key: ${CLOUDFLARE_R2_SECRET_ACCESS_KEY:c613fd21bcee56149c83971c55880b5130ef7332845e2230b317fc1d3cceae31}
    bucket: ${CLOUDFLARE_R2_BUCKET:mysite}
    region: ${CLOUDFLARE_R2_REGION:auto}
    public-url: ${CLOUDFLARE_R2_PUBLIC_URL:https://hao8.fun}
    presignUpload-duration-minutes: ${CLOUDFLARE_R2_UPLOAD_DURATION:20}
    presignDownload-duration-minutes: ${CLOUDFLARE_R2_DOWNLOAD_DURATION:180}

# ========================
# R2 上传配置 - 开发环境
# ========================
r2:
  upload:
    max-retries: ${R2_UPLOAD_MAX_RETRIES:3}
    retry-delay: ${R2_UPLOAD_RETRY_DELAY:1000}

# ========================
# reCAPTCHA配置 - 开发环境
# ========================
recaptcha:
  secret-key: ${RECAPTCHA_SECRET_KEY:6LenmWYrAAAAAHuqQflQix2NMN5oYYd30mwHJsGP}

# ========================
# 日志配置 - 开发环境
# ========================
logging:
  level:
    root: INFO
    com.example.pure: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
    com.example.pure.mapper: DEBUG
    com.example.demo13.security: DEBUG
    org.springframework.messaging: DEBUG
    org.springframework.web.socket: DEBUG
    org.springframework.web.socket.messaging: TRACE
    com.example.demo13.controller.WebSocketTestController: DEBUG

  file:
    name: logs/application.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
