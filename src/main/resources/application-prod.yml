# ========================
# 生产环境配置
# ========================

spring:
  # ========================
  # 应用名称配置
  # ========================
  application:
    name: ${SPRING_APPLICATION_NAME:pure}
  # ========================
  # 数据源配置 - 生产环境，建造打包成jar或者在docker用这的配置
  # ========================
  datasource:
    primary:
      url: jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME:pure}?useUnicode=true&characterEncoding=UTF-8&serverTimezone=UTC&useSSL=true&allowPublicKeyRetrieval=true&autoReconnect=true&failOverReadOnly=false&maxReconnects=3&initialTimeout=2&connectTimeout=30000&socketTimeout=60000&cachePrepStmts=true&useServerPrepStmts=true&prepStmtCacheSize=250&prepStmtCacheSqlLimit=2048&useLocalSessionState=true&rewriteBatchedStatements=true
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: HikariPool-Primary
        maximum-pool-size: ${DB_POOL_MAX_SIZE:20}  # 生产环境增大连接池
        minimum-idle: ${DB_POOL_MIN_IDLE:10}
        idle-timeout: 300000
        connection-timeout: 20000
        max-lifetime: 1200000
    secondary:
      url: jdbc:mysql://${DB_SECONDARY_HOST}:${DB_SECONDARY_PORT}/school2?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=true
      username: ${DB_SECONDARY_USERNAME}
      password: ${DB_SECONDARY_PASSWORD}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        pool-name: HikariPool-Secondary
        maximum-pool-size: ${DB_SECONDARY_POOL_MAX_SIZE:10}
        minimum-idle: ${DB_SECONDARY_POOL_MIN_IDLE:5}
        idle-timeout: 300000
        connection-timeout: 20000
        max-lifetime: 1200000

  # ========================
  # Redis配置 - 生产环境
  # ========================
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    database: ${REDIS_DATABASE:2}
    timeout: 10000
    lettuce:
      pool:
        max-active: ${REDIS_POOL_MAX_ACTIVE:16}  # 生产环境增大连接池
        max-wait: -1
        max-idle: ${REDIS_POOL_MAX_IDLE:16}
        min-idle: ${REDIS_POOL_MIN_IDLE:4}

  # ========================
  # 邮件配置 - 生产环境
  # ========================
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    protocol: smtp
    default-encoding: UTF-8

# ========================
# JWT配置 - 生产环境
# ========================
jwt:
  secret: ${JWT_SECRET}  # 生产环境必须通过环境变量提供
  expiration: ${JWT_EXPIRATION:86400}  # 生产环境缩短为1天
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800}  # 7天

backend:
  url: ${BACKEND_URL:https://hao8.fun}

frontend:
  url: ${FRONTEND_URL:https://hao8.fun}

# ========================
# Cloudflare R2配置 - 生产环境
# ========================
cloudflare:
  r2:
    endpoint: ${CLOUDFLARE_R2_ENDPOINT}
    access-key-id: ${CLOUDFLARE_R2_ACCESS_KEY_ID}
    secret-access-key: ${CLOUDFLARE_R2_SECRET_ACCESS_KEY}
    bucket: ${CLOUDFLARE_R2_BUCKET}
    region: ${CLOUDFLARE_R2_REGION:auto}
    public-url: ${CLOUDFLARE_R2_PUBLIC_URL}
    presignUpload-duration-minutes: ${CLOUDFLARE_R2_UPLOAD_DURATION:60}
    presignDownload-duration-minutes: ${CLOUDFLARE_R2_DOWNLOAD_DURATION:180}

# ========================
# R2 上传配置 - 生产环境
# ========================
r2:
  upload:
    max-retries: ${R2_UPLOAD_MAX_RETRIES:5}  # 生产环境增加重试次数
    retry-delay: ${R2_UPLOAD_RETRY_DELAY:2000}  # 生产环境增加重试延迟

# ========================
# reCAPTCHA配置 - 生产环境
# ========================
recaptcha:
  secret-key: ${RECAPTCHA_SECRET_KEY}

# ========================
# 日志配置 - 生产环境
# ========================
logging:
  level:
    root: WARN  # 生产环境减少日志输出
    com.example.pure: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.mybatis: WARN
    com.example.pure.mapper: INFO
    com.example.demo13.security: INFO
    org.springframework.messaging: WARN
    org.springframework.web.socket: WARN
    org.springframework.web.socket.messaging: WARN
    com.example.demo13.controller.WebSocketTestController: INFO

  file:
    name: logs/application.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# ========================
# 生产环境性能优化
# ========================
server:
  tomcat:
    max-threads: ${SERVER_MAX_THREADS:300}  # 生产环境增加线程数
    min-spare-threads: ${SERVER_MIN_SPARE_THREADS:20}
    max-connections: ${SERVER_MAX_CONNECTIONS:20000}
    accept-count: ${SERVER_ACCEPT_COUNT:200}
