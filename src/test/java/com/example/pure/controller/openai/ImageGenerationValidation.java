package com.example.pure.controller.openai;

import com.example.pure.model.dto.request.openai.OpenAiImageRequest;

/**
 * Simple validation test for image generation
 */
public class ImageGenerationValidation {

    public static void main(String[] args) {
        System.out.println("Testing OpenAI Image Generation Request Validation...");
        
        // Test 1: Valid DALL-E 3 request
        try {
            OpenAiImageRequest request1 = new OpenAiImageRequest();
            request1.setModel("dall-e-3");
            request1.setPrompt("A beautiful sunset over the ocean");
            request1.setN(1);
            request1.setSize("1024x1024");
            request1.setQuality("standard");
            request1.setStyle("vivid");
            
            request1.validateModelCompatibility();
            System.out.println("✓ Test 1 PASSED: Valid DALL-E 3 request");
        } catch (Exception e) {
            System.out.println("✗ Test 1 FAILED: " + e.getMessage());
        }
        
        // Test 2: Invalid DALL-E 3 request (multiple images)
        try {
            OpenAiImageRequest request2 = new OpenAiImageRequest();
            request2.setModel("dall-e-3");
            request2.setPrompt("A beautiful sunset over the ocean");
            request2.setN(2); // Should fail
            request2.setSize("1024x1024");
            
            request2.validateModelCompatibility();
            System.out.println("✗ Test 2 FAILED: Should have thrown exception");
        } catch (IllegalArgumentException e) {
            if (e.getMessage().contains("DALL-E 3")) {
                System.out.println("✓ Test 2 PASSED: Correctly rejected multiple images for DALL-E 3");
            } else {
                System.out.println("✗ Test 2 FAILED: Wrong error message: " + e.getMessage());
            }
        }
        
        // Test 3: Valid DALL-E 2 request
        try {
            OpenAiImageRequest request3 = new OpenAiImageRequest();
            request3.setModel("dall-e-2");
            request3.setPrompt("A beautiful sunset over the ocean");
            request3.setN(3);
            request3.setSize("512x512");
            
            request3.validateModelCompatibility();
            System.out.println("✓ Test 3 PASSED: Valid DALL-E 2 request");
        } catch (Exception e) {
            System.out.println("✗ Test 3 FAILED: " + e.getMessage());
        }
        
        // Test 4: Invalid DALL-E 2 size
        try {
            OpenAiImageRequest request4 = new OpenAiImageRequest();
            request4.setModel("dall-e-2");
            request4.setPrompt("A beautiful sunset over the ocean");
            request4.setN(1);
            request4.setSize("1024x1792"); // Should fail
            
            request4.validateModelCompatibility();
            System.out.println("✗ Test 4 FAILED: Should have thrown exception");
        } catch (IllegalArgumentException e) {
            if (e.getMessage().contains("DALL-E 2")) {
                System.out.println("✓ Test 4 PASSED: Correctly rejected invalid size for DALL-E 2");
            } else {
                System.out.println("✗ Test 4 FAILED: Wrong error message: " + e.getMessage());
            }
        }
        
        System.out.println("\nValidation tests completed!");
    }
}
