package com.example.pure.controller;

import com.example.pure.model.dto.response.auth.QRLoginDTO.CommunicationType;
import com.example.pure.model.dto.response.auth.QRLoginDTO.QRScanRequest;
import com.example.pure.service.auth.QRLoginSSEService;
import com.example.pure.util.QRLoginRedisUtil;
import com.example.pure.util.QRLoginRedisUtil.QRCodeInfo;
import com.example.pure.util.QRLoginRedisUtil.QRCodeStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 二维码登录控制器测试类
 * <p>
 * 测试二维码登录的各种功能，包括 WebSocket 和 SSE 两种通信方式
 * </p>
 */
@SpringBootTest
class QRLoginControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private QRLoginRedisUtil qrLoginUtil;

    @MockBean
    private QRLoginSSEService qrLoginSSEService;

    /**
     * 测试创建二维码
     */
    @Test
    void testCreateQRCode() throws Exception {
        // 模拟二维码信息
        QRCodeInfo mockQRInfo = new QRCodeInfo();
        mockQRInfo.setStatus(QRCodeStatus.PENDING);
        mockQRInfo.setExpireTime(LocalDateTime.now().plusMinutes(5));

        when(qrLoginUtil.generateQRCodeId()).thenReturn("test-qr-id");
        when(qrLoginUtil.getQRCodeInfo("test-qr-id")).thenReturn(mockQRInfo);

        mockMvc.perform(get("/api/qrlogin/create"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.qrId").value("test-qr-id"));
    }

    /**
     * 测试 SSE 订阅二维码状态
     */
    @Test
    void testSubscribeQRStatusSSE() throws Exception {
        // 模拟二维码信息
        QRCodeInfo mockQRInfo = new QRCodeInfo();
        mockQRInfo.setStatus(QRCodeStatus.PENDING);
        mockQRInfo.setExpireTime(LocalDateTime.now().plusMinutes(5));

        when(qrLoginUtil.getQRCodeInfo("test-qr-id")).thenReturn(mockQRInfo);

        mockMvc.perform(get("/api/qrlogin/sse/subscribe/test-qr-id"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "text/event-stream"));
    }

    /**
     * 测试 SSE 订阅不存在的二维码
     */
    @Test
    void testSubscribeQRStatusSSE_NotFound() throws Exception {
        when(qrLoginUtil.getQRCodeInfo("non-existent-qr-id")).thenReturn(null);

        mockMvc.perform(get("/api/qrlogin/sse/subscribe/non-existent-qr-id"))
                .andExpect(status().isNotFound());
    }

    /**
     * 测试扫描二维码 - WebSocket 通信方式
     */
    @Test
    void testScanQRCode_WebSocket() throws Exception {
        QRScanRequest request = QRScanRequest.builder()
                .qrId("test-qr-id")
                .token("Bearer test-token")
                .communicationType(CommunicationType.WEBSOCKET)
                .build();

        when(qrLoginUtil.checkDuplicateVerificationRequest(any(), any())).thenReturn(false);
        when(qrLoginUtil.scanQRCode(any(), any())).thenReturn(true);

        mockMvc.perform(post("/api/qrlogin/scan")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试扫描二维码 - SSE 通信方式
     */
    @Test
    void testScanQRCode_SSE() throws Exception {
        QRScanRequest request = QRScanRequest.builder()
                .qrId("test-qr-id")
                .token("Bearer test-token")
                .communicationType(CommunicationType.SSE)
                .build();

        when(qrLoginUtil.checkDuplicateVerificationRequest(any(), any())).thenReturn(false);
        when(qrLoginUtil.scanQRCode(any(), any())).thenReturn(true);

        mockMvc.perform(post("/api/qrlogin/scan")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    /**
     * 测试通信类型枚举
     */
    @Test
    void testCommunicationType() {
        // 测试枚举值
        assert CommunicationType.WEBSOCKET.getCode().equals("websocket");
        assert CommunicationType.SSE.getCode().equals("sse");
        assert CommunicationType.WEBSOCKET.getDescription().equals("WebSocket实时通信");
        assert CommunicationType.SSE.getDescription().equals("Server-Sent Events流式通信");
    }
}
