package com.example.pure.config;

import com.example.pure.listener.VideoUploadProgressListener;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.transfer.s3.progress.TransferListener;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VideoUploadProgressListener测试类
 * <p>
 * 验证日志格式化修复是否正确
 * </p>
 */
public class VideoUploadProgressListenerTest {

    private static final Logger log = LoggerFactory.getLogger(VideoUploadProgressListenerTest.class);

    /**
     * 测试日志格式化修复
     */
    @Test
    public void testLogFormatting() {
        System.out.println("\n🔧 测试日志格式化修复");
        System.out.println("=" .repeat(50));

        // 测试各种数值的格式化
        testNumberFormatting();

        // 测试VideoUploadProgressListener创建
        testProgressListenerCreation();

        System.out.println("\n✅ 日志格式化修复验证完成");
    }

    /**
     * 测试数值格式化
     */
    private void testNumberFormatting() {
        System.out.println("\n📊 测试数值格式化：");

        // 测试文件大小格式化
        long fileSize = 1024L * 1024 * 150; // 150MB
        String formattedSize = String.format("%.2f", fileSize / (1024.0 * 1024.0));
        System.out.println("   • 文件大小：" + formattedSize + " MB");
        assertEquals("150.00", formattedSize);

        // 测试进度百分比格式化
        double progress = 0.6789;
        String formattedProgress = String.format("%.1f", progress * 100);
        System.out.println("   • 进度百分比：" + formattedProgress + "%");
        assertEquals("67.9", formattedProgress);

        // 测试速度格式化
        double speed = 12.3456789;
        String formattedSpeed = String.format("%.2f", speed);
        System.out.println("   • 上传速度：" + formattedSpeed + " MB/s");
        assertEquals("12.35", formattedSpeed);

        System.out.println("✅ 数值格式化测试通过");
    }

    /**
     * 测试VideoUploadProgressListener创建
     */
    private void testProgressListenerCreation() {
        System.out.println("\n🎯 测试VideoUploadProgressListener创建：");

        String objectKey = "video/test-uuid/test-episode.mp4";
        long totalBytes = 50L * 1024 * 1024; // 50MB

        VideoUploadProgressListener listener = VideoUploadProgressListener.create(objectKey, totalBytes);

        assertNotNull(listener, "VideoUploadProgressListener应该被成功创建");
        assertTrue(listener instanceof TransferListener, "应该实现TransferListener接口");

        System.out.println("   • ObjectKey: " + objectKey);
        System.out.println("   • 文件大小: " + String.format("%.2f", totalBytes / (1024.0 * 1024.0)) + " MB");
        System.out.println("✅ VideoUploadProgressListener创建成功");
    }

    /**
     * 测试日志输出格式
     */
    @Test
    public void testLogOutputFormat() {
        System.out.println("\n📝 测试日志输出格式：");

        // 模拟正确的日志格式
        String objectKey = "video/test-uuid/episode-1.mp4";
        double progressPercent = 0.456;
        long transferredBytes = 45L * 1024 * 1024; // 45MB
        double speedMBps = 8.7654321;
        String etaStr = "2分30秒";

        // 使用修复后的格式化方式
        String logMessage = String.format(
            "上传进度 - objectKey: %s, 进度: %s%%, 已传输: %s MB, 速度: %s MB/s, 预计剩余: %s",
            objectKey,
            String.format("%.1f", progressPercent * 100),
            String.format("%.2f", transferredBytes / (1024.0 * 1024.0)),
            String.format("%.2f", speedMBps),
            etaStr
        );

        System.out.println("   修复后的日志格式：");
        System.out.println("   " + logMessage);

        // 验证格式化结果
        assertTrue(logMessage.contains("45.6%"), "进度应该正确格式化");
        assertTrue(logMessage.contains("45.00 MB"), "传输大小应该正确格式化");
        assertTrue(logMessage.contains("8.77 MB/s"), "速度应该正确格式化");
        assertTrue(logMessage.contains("2分30秒"), "预计时间应该正确显示");

        // 确保没有格式化占位符残留
        assertFalse(logMessage.contains("{:."), "不应该包含格式化占位符");

        System.out.println("✅ 日志输出格式验证通过");
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        System.out.println("\n🔍 测试边界情况：");

        // 测试零值
        String zeroSpeed = String.format("%.2f", 0.0);
        assertEquals("0.00", zeroSpeed);
        System.out.println("   • 零速度格式化：" + zeroSpeed + " MB/s");

        // 测试小数值
        String smallValue = String.format("%.2f", 0.001);
        assertEquals("0.00", smallValue);
        System.out.println("   • 小数值格式化：" + smallValue + " MB");

        // 测试大数值
        double largeValue = 1234.5678;
        String largeFormatted = String.format("%.2f", largeValue);
        assertEquals("1234.57", largeFormatted);
        System.out.println("   • 大数值格式化：" + largeFormatted + " MB");

        System.out.println("✅ 边界情况测试通过");
    }
}
