package com.example.pure.config;

import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 内容反序列化器测试
 */
@SpringBootTest
class ContentDeserializerTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testStringContentDeserialization() throws Exception {
        // 测试字符串格式的 content
        String json = "{\n" +
                "  \"model\": \"gpt-4\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": \"Hello, how are you?\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        OpenAiChatRequest request = objectMapper.readValue(json, OpenAiChatRequest.class);

        assertNotNull(request);
        assertEquals("gpt-4", request.getModel());
        assertEquals(1, request.getMessages().size());

        OpenAiChatRequest.OpenAiMessage message = request.getMessages().get(0);
        assertEquals("user", message.getRole());
        assertNotNull(message.getContent());
        assertEquals(1, message.getContent().size());

        OpenAiChatRequest.ContentPart part = message.getContent().get(0);
        assertEquals("text", part.getType());
        assertEquals("Hello, how are you?", part.getText());
    }

    @Test
    void testArrayContentDeserialization() throws Exception {
        // 测试数组格式的 content
        String json = "{\n" +
                "  \"model\": \"gpt-4\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": [\n" +
                "        {\n" +
                "          \"type\": \"text\",\n" +
                "          \"text\": \"Hello, how are you?\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        OpenAiChatRequest request = objectMapper.readValue(json, OpenAiChatRequest.class);

        assertNotNull(request);
        assertEquals(1, request.getMessages().size());

        OpenAiChatRequest.OpenAiMessage message = request.getMessages().get(0);
        assertEquals("user", message.getRole());
        assertNotNull(message.getContent());
        assertEquals(1, message.getContent().size());

        OpenAiChatRequest.ContentPart part = message.getContent().get(0);
        assertEquals("text", part.getType());
        assertEquals("Hello, how are you?", part.getText());
    }

    @Test
    void testMultimodalContentDeserialization() throws Exception {
        // 测试多模态数组格式的 content
        String json = "{\n" +
                "  \"model\": \"gpt-4\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": [\n" +
                "        {\n" +
                "          \"type\": \"text\",\n" +
                "          \"text\": \"What's in this image?\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"type\": \"image_url\",\n" +
                "          \"image_url\": {\n" +
                "            \"url\": \"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\"\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        OpenAiChatRequest request = objectMapper.readValue(json, OpenAiChatRequest.class);

        assertNotNull(request);
        assertEquals(1, request.getMessages().size());

        OpenAiChatRequest.OpenAiMessage message = request.getMessages().get(0);
        assertEquals("user", message.getRole());
        assertNotNull(message.getContent());
        assertEquals(2, message.getContent().size());

        // 验证文本部分
        OpenAiChatRequest.ContentPart textPart = message.getContent().get(0);
        assertEquals("text", textPart.getType());
        assertEquals("What's in this image?", textPart.getText());

        // 验证图片部分
        OpenAiChatRequest.ContentPart imagePart = message.getContent().get(1);
        assertEquals("image_url", imagePart.getType());
        assertNotNull(imagePart.getImageUrl());
        assertTrue(imagePart.getImageUrl().getUrl().startsWith("data:image/jpeg;base64,"));
    }

    @Test
    void testInvalidContentFormat() {
        // 测试无效格式
        String json = "{\n" +
                "  \"model\": \"gpt-4\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": 123\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        assertThrows(Exception.class, () -> {
            objectMapper.readValue(json, OpenAiChatRequest.class);
        });
    }

    @Test
    void testExtraBodyDeserialization() throws Exception {
        // 测试 extra_body 字段
        String json = "{\n" +
                "  \"model\": \"gpt-4\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": \"Hello\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"extra_body\": {\n" +
                "    \"custom_parameter\": \"test_value\",\n" +
                "    \"experimental_feature\": true,\n" +
                "    \"numeric_param\": 42\n" +
                "  }\n" +
                "}";

        OpenAiChatRequest request = objectMapper.readValue(json, OpenAiChatRequest.class);

        assertNotNull(request);
        assertEquals("gpt-4", request.getModel());
        assertNotNull(request.getExtraBody());
        assertEquals(3, request.getExtraBody().size());
        assertEquals("test_value", request.getExtraBody().get("custom_parameter"));
        assertEquals(true, request.getExtraBody().get("experimental_feature"));
        assertEquals(42, request.getExtraBody().get("numeric_param"));
    }
}
