package com.example.pure.constant;

import com.example.pure.model.dto.response.auth.QRLoginDTO.CommunicationType;
import com.example.pure.util.QRContentParser;
import com.example.pure.util.QRContentParser.QRParseResult;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SecurityConstants 二维码登录常量测试类
 * <p>
 * 验证二维码登录相关常量的正确性和一致性
 * </p>
 */
class SecurityConstantsQRLoginTest {

    @Test
    void testQRLoginSchemeConstant() {
        // 验证二维码登录协议前缀
        assertEquals("qrlogin://", SecurityConstants.QR_LOGIN_SCHEME);

        // 验证协议前缀在实际使用中的正确性
        String testQrId = "test-qr-id";
        String expectedContent = SecurityConstants.QR_LOGIN_SCHEME + testQrId + "?type=websocket";
        String actualContent = QRContentParser.generateQRContent(testQrId, CommunicationType.WEBSOCKET);

        assertEquals(expectedContent, actualContent);
    }

    @Test
    void testSSETimeoutConstant() {
        // 验证 SSE 超时时间常量
        assertEquals(150000L, SecurityConstants.SSE_TIMEOUT);

        // 验证超时时间的合理性（应该大于二维码有效期）
        assertTrue(SecurityConstants.SSE_TIMEOUT > 120000L, "SSE 超时时间应该大于二维码有效期（120秒）");
    }

    @Test
    void testQRLoginRedisPrefixConstant() {
        // 验证 Redis 键前缀
        assertEquals("qrlogin:", SecurityConstants.QR_LOGIN_REDIS_PREFIX);

        // 验证前缀格式的正确性
        assertTrue(SecurityConstants.QR_LOGIN_REDIS_PREFIX.endsWith(":"), "Redis 键前缀应该以冒号结尾");
        assertFalse(SecurityConstants.QR_LOGIN_REDIS_PREFIX.contains(" "), "Redis 键前缀不应包含空格");
    }

    @Test
    void testDefaultCommunicationTypeConstant() {
        // 验证默认通信类型
        assertEquals("WEBSOCKET", SecurityConstants.DEFAULT_COMMUNICATION_TYPE);

        // 验证默认通信类型可以正确转换为枚举
        assertDoesNotThrow(() -> {
            CommunicationType type = CommunicationType.valueOf(SecurityConstants.DEFAULT_COMMUNICATION_TYPE);
            assertEquals(CommunicationType.WEBSOCKET, type);
        });
    }

    @Test
    void testQRContentParsingWithConstants() {
        // 测试使用常量生成和解析二维码内容的一致性
        String qrId = "test-consistency";

        // 生成 WebSocket 类型的二维码内容
        String websocketContent = QRContentParser.generateQRContent(qrId, CommunicationType.WEBSOCKET);
        QRParseResult websocketResult = QRContentParser.parseQRContent(websocketContent);

        assertTrue(websocketResult.isValid());
        assertEquals(qrId, websocketResult.getQrId());
        assertEquals(CommunicationType.WEBSOCKET, websocketResult.getCommunicationType());

        // 生成 SSE 类型的二维码内容
        String sseContent = QRContentParser.generateQRContent(qrId, CommunicationType.SSE);
        QRParseResult sseResult = QRContentParser.parseQRContent(sseContent);

        assertTrue(sseResult.isValid());
        assertEquals(qrId, sseResult.getQrId());
        assertEquals(CommunicationType.SSE, sseResult.getCommunicationType());
    }

    @Test
    void testConstantsConsistency() {
        // 验证常量之间的一致性

        // 验证协议前缀与解析逻辑的一致性
        String testContent = SecurityConstants.QR_LOGIN_SCHEME + "test-id?type=sse";
        QRParseResult result = QRContentParser.parseQRContent(testContent);
        assertTrue(result.isValid(), "使用常量生成的内容应该能够正确解析");

        // 验证默认通信类型的有效性
        assertDoesNotThrow(() -> {
            CommunicationType.valueOf(SecurityConstants.DEFAULT_COMMUNICATION_TYPE);
        }, "默认通信类型应该是有效的枚举值");
    }

    @Test
    void testRedisKeyGeneration() {
        // 测试 Redis 键的生成
        String qrId = "test-redis-key";
        String expectedKey = SecurityConstants.QR_LOGIN_REDIS_PREFIX + qrId;

        assertEquals("qrlogin:test-redis-key", expectedKey);

        // 验证键的格式
        assertTrue(expectedKey.startsWith(SecurityConstants.QR_LOGIN_REDIS_PREFIX));
        assertTrue(expectedKey.length() > SecurityConstants.QR_LOGIN_REDIS_PREFIX.length());
    }

    @Test
    void testTimeoutReasonableness() {
        // 验证超时时间的合理性
        long sseTimeout = SecurityConstants.SSE_TIMEOUT;

        // SSE 超时应该在合理范围内（30秒到10分钟）
        assertTrue(sseTimeout >= 30000L, "SSE 超时时间不应少于30秒");
        assertTrue(sseTimeout <= 600000L, "SSE 超时时间不应超过10分钟");

        // 应该是毫秒单位（通常是1000的倍数）
        assertEquals(0, sseTimeout % 1000, "SSE 超时时间应该是秒的整数倍（毫秒单位）");
    }

    @Test
    void testConstantImmutability() {
        // 验证常量的不可变性（通过反射检查 final 修饰符）
        try {
            var field = SecurityConstants.class.getField("QR_LOGIN_SCHEME");
            assertTrue(java.lang.reflect.Modifier.isFinal(field.getModifiers()), "QR_LOGIN_SCHEME 应该是 final 的");
            assertTrue(java.lang.reflect.Modifier.isStatic(field.getModifiers()), "QR_LOGIN_SCHEME 应该是 static 的");
            assertTrue(java.lang.reflect.Modifier.isPublic(field.getModifiers()), "QR_LOGIN_SCHEME 应该是 public 的");
        } catch (NoSuchFieldException e) {
            fail("QR_LOGIN_SCHEME 常量应该存在");
        }
    }
}
