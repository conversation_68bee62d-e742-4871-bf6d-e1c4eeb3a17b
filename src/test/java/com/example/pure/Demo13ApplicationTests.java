package com.example.pure;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.encrypt.BytesEncryptor;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.crypto.keygen.KeyGenerators;

import java.util.Arrays;

@Slf4j
@SpringBootTest
class pureApplicationTests {

    @Test
    void contextLoads() {
    }

    @Test
    public void test() {
        String salt = KeyGenerators.string().generateKey();
        BytesEncryptor encryptor= Encryptors.stronger("abcdefgh",salt);
        byte[] bytes = "123456".getBytes();
        byte[] encryptedBytes=encryptor.encrypt(bytes);
        String convertedString=encryptedBytes.toString();

        byte[] decryptedBytes=encryptor.decrypt(encryptedBytes);
        String rowString= Arrays.toString(decryptedBytes);


        TextEncryptor encryptor2=Encryptors.text("password", salt);
        String encryptedText = encryptor2.encrypt("123456");
        String rowText=encryptor2.decrypt(encryptedText);
    }
    @Test
    public void test2(){
        log.warn("this is message");
    }

}
