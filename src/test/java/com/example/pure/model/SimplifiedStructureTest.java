package com.example.pure.model;

import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化结构测试
 */
class SimplifiedStructureTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testSimplifiedMultimodalStructure() throws Exception {
        // 创建简化的多模态请求
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setModel("gpt-4o");
        
        List<OpenAiChatRequest.OpenAiMessage> messages = new ArrayList<>();
        OpenAiChatRequest.OpenAiMessage message = new OpenAiChatRequest.OpenAiMessage();
        message.setRole("user");
        
        // 直接创建ContentPart列表
        List<OpenAiChatRequest.ContentPart> content = new ArrayList<>();
        
        // 文本部分
        OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
        textPart.setType("text");
        textPart.setText("请分析这张图片中的内容，描述你看到了什么，并提供详细的解释。");
        content.add(textPart);
        
        // 图片部分
        OpenAiChatRequest.ContentPart imagePart = new OpenAiChatRequest.ContentPart();
        imagePart.setType("image_url");
        OpenAiChatRequest.ImageUrl imageUrl = new OpenAiChatRequest.ImageUrl();
        imageUrl.setUrl("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...");
        imagePart.setImageUrl(imageUrl);
        content.add(imagePart);
        
        message.setContent(content);
        messages.add(message);
        request.setMessages(messages);

        // 序列化
        String json = objectMapper.writeValueAsString(request);
        System.out.println("简化结构JSON:");
        System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request));

        // 验证JSON结构
        assertTrue(json.contains("\"content\":["));
        assertTrue(json.contains("\"type\":\"text\""));
        assertTrue(json.contains("\"type\":\"image_url\""));
        assertTrue(json.contains("\"image_url\":{\"url\":\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...\""));

        // 反序列化验证
        OpenAiChatRequest deserializedRequest = objectMapper.readValue(json, OpenAiChatRequest.class);
        assertEquals("gpt-4o", deserializedRequest.getModel());
        assertEquals(1, deserializedRequest.getMessages().size());
        
        OpenAiChatRequest.OpenAiMessage deserializedMessage = deserializedRequest.getMessages().get(0);
        assertEquals("user", deserializedMessage.getRole());
        
        List<OpenAiChatRequest.ContentPart> deserializedContent = deserializedMessage.getContent();
        assertNotNull(deserializedContent);
        assertEquals(2, deserializedContent.size());
        
        // 验证文本部分
        OpenAiChatRequest.ContentPart deserializedTextPart = deserializedContent.get(0);
        assertEquals("text", deserializedTextPart.getType());
        assertEquals("请分析这张图片中的内容，描述你看到了什么，并提供详细的解释。", deserializedTextPart.getText());
        
        // 验证图片部分
        OpenAiChatRequest.ContentPart deserializedImagePart = deserializedContent.get(1);
        assertEquals("image_url", deserializedImagePart.getType());
        assertNotNull(deserializedImagePart.getImageUrl());
        assertEquals("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...", 
                    deserializedImagePart.getImageUrl().getUrl());

        System.out.println("✅ 简化结构测试通过！");
    }

    @Test
    void testParseYourExactFormat() throws Exception {
        // 测试解析您提供的确切格式
        String yourFormatJson = "{\n" +
            "  \"model\": \"gpt-4o\",\n" +
            "  \"messages\": [\n" +
            "    {\n" +
            "      \"role\": \"user\",\n" +
            "      \"content\": [\n" +
            "        {\n" +
            "          \"type\": \"text\",\n" +
            "          \"text\": \"请分析这张图片中的内容，描述你看到了什么，并提供详细的解释。\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"type\": \"image_url\",\n" +
            "          \"image_url\": {\n" +
            "            \"url\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}";

        System.out.println("解析您的确切格式:");
        System.out.println(yourFormatJson);

        // 反序列化
        OpenAiChatRequest request = objectMapper.readValue(yourFormatJson, OpenAiChatRequest.class);
        
        // 验证解析结果
        assertEquals("gpt-4o", request.getModel());
        assertEquals(1, request.getMessages().size());
        
        OpenAiChatRequest.OpenAiMessage message = request.getMessages().get(0);
        assertEquals("user", message.getRole());
        
        List<OpenAiChatRequest.ContentPart> content = message.getContent();
        assertNotNull(content);
        assertEquals(2, content.size());
        
        // 验证文本部分
        OpenAiChatRequest.ContentPart textPart = content.get(0);
        assertEquals("text", textPart.getType());
        assertEquals("请分析这张图片中的内容，描述你看到了什么，并提供详细的解释。", textPart.getText());
        
        // 验证图片部分
        OpenAiChatRequest.ContentPart imagePart = content.get(1);
        assertEquals("image_url", imagePart.getType());
        assertNotNull(imagePart.getImageUrl());
        assertEquals("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...", 
                    imagePart.getImageUrl().getUrl());

        // 重新序列化验证
        String reserializedJson = objectMapper.writeValueAsString(request);
        System.out.println("\n重新序列化的JSON:");
        System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request));
        
        // 验证重新序列化后包含正确的字段
        assertTrue(reserializedJson.contains("\"image_url\":{\"url\":\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...\""));
        
        System.out.println("✅ 您的确切格式解析和序列化都正常！");
    }

    @Test
    void testTextOnlyMessage() throws Exception {
        // 测试纯文本消息
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setModel("gpt-3.5-turbo");
        
        List<OpenAiChatRequest.OpenAiMessage> messages = new ArrayList<>();
        OpenAiChatRequest.OpenAiMessage message = new OpenAiChatRequest.OpenAiMessage();
        message.setRole("user");
        
        // 只有文本内容
        List<OpenAiChatRequest.ContentPart> content = new ArrayList<>();
        OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
        textPart.setType("text");
        textPart.setText("Hello, how are you?");
        content.add(textPart);
        
        message.setContent(content);
        messages.add(message);
        request.setMessages(messages);

        // 序列化
        String json = objectMapper.writeValueAsString(request);
        System.out.println("纯文本消息JSON:");
        System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request));

        // 验证JSON结构
        assertTrue(json.contains("\"content\":[{\"type\":\"text\",\"text\":\"Hello, how are you?\"}]"));

        // 反序列化验证
        OpenAiChatRequest deserializedRequest = objectMapper.readValue(json, OpenAiChatRequest.class);
        List<OpenAiChatRequest.ContentPart> deserializedContent = 
            deserializedRequest.getMessages().get(0).getContent();
        
        assertEquals(1, deserializedContent.size());
        assertEquals("text", deserializedContent.get(0).getType());
        assertEquals("Hello, how are you?", deserializedContent.get(0).getText());

        System.out.println("✅ 纯文本消息测试通过！");
    }

    @Test
    void testMultipleImagesMessage() throws Exception {
        // 测试多张图片消息
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setModel("gpt-4o");
        
        List<OpenAiChatRequest.OpenAiMessage> messages = new ArrayList<>();
        OpenAiChatRequest.OpenAiMessage message = new OpenAiChatRequest.OpenAiMessage();
        message.setRole("user");
        
        List<OpenAiChatRequest.ContentPart> content = new ArrayList<>();
        
        // 文本部分
        OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
        textPart.setType("text");
        textPart.setText("比较这些图片的差异：");
        content.add(textPart);
        
        // 添加3张图片
        for (int i = 1; i <= 3; i++) {
            OpenAiChatRequest.ContentPart imagePart = new OpenAiChatRequest.ContentPart();
            imagePart.setType("image_url");
            OpenAiChatRequest.ImageUrl imageUrl = new OpenAiChatRequest.ImageUrl();
            imageUrl.setUrl("https://example.com/image" + i + ".jpg");
            imagePart.setImageUrl(imageUrl);
            content.add(imagePart);
        }
        
        message.setContent(content);
        messages.add(message);
        request.setMessages(messages);

        // 序列化
        String json = objectMapper.writeValueAsString(request);
        System.out.println("多张图片消息JSON:");
        System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request));

        // 验证JSON包含所有图片
        assertTrue(json.contains("image1.jpg"));
        assertTrue(json.contains("image2.jpg"));
        assertTrue(json.contains("image3.jpg"));

        // 反序列化验证
        OpenAiChatRequest deserializedRequest = objectMapper.readValue(json, OpenAiChatRequest.class);
        List<OpenAiChatRequest.ContentPart> deserializedContent = 
            deserializedRequest.getMessages().get(0).getContent();
        
        assertEquals(4, deserializedContent.size()); // 1个文本 + 3个图片
        
        // 验证图片数量
        long imageCount = deserializedContent.stream()
            .filter(part -> "image_url".equals(part.getType()))
            .count();
        assertEquals(3, imageCount);

        System.out.println("✅ 多张图片消息测试通过！");
    }
}
