package com.example.pure.service.openai;

import com.example.pure.model.adapter.ChatCompletionResponse;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.impl.ReasoningExtractorServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 推理过程提取服务测试
 */
@ExtendWith(MockitoExtension.class)
class ReasoningExtractorServiceTest {

    private ReasoningExtractorService reasoningExtractorService;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        reasoningExtractorService = new ReasoningExtractorServiceImpl(objectMapper);
    }

    @Test
    void testShouldEnableReasoning_WithIncludeReasoningTrue() {
        // 准备测试数据
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setIncludeReasoning(true);
        request.setModel("o1-preview");

        // 执行测试
        boolean result = reasoningExtractorService.shouldEnableReasoning(UserApiKey.ProviderType.OPENAI, request);

        // 验证结果
        assertTrue(result, "应该启用推理过程");
    }

    @Test
    void testShouldEnableReasoning_WithIncludeReasoningFalse() {
        // 准备测试数据
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setIncludeReasoning(false);
        request.setModel("o1-preview");

        // 执行测试
        boolean result = reasoningExtractorService.shouldEnableReasoning(UserApiKey.ProviderType.OPENAI, request);

        // 验证结果
        assertFalse(result, "不应该启用推理过程");
    }

    @Test
    void testShouldEnableReasoning_WithNonReasoningModel() {
        // 准备测试数据
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setIncludeReasoning(true);
        request.setModel("gpt-4");

        // 执行测试
        boolean result = reasoningExtractorService.shouldEnableReasoning(UserApiKey.ProviderType.OPENAI, request);

        // 验证结果
        assertFalse(result, "非推理模型不应该启用推理过程");
    }

    @Test
    void testShouldEnableReasoning_ClaudeModel() {
        // 准备测试数据
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setIncludeReasoning(true);
        request.setModel("claude-4");

        // 执行测试
        boolean result = reasoningExtractorService.shouldEnableReasoning(UserApiKey.ProviderType.ANTHROPIC, request);

        // 验证结果
        assertTrue(result, "Claude 4应该支持推理过程");
    }

    @Test
    void testShouldEnableReasoning_GeminiModel() {
        // 准备测试数据
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setIncludeReasoning(true);
        request.setModel("gemini-2.5-pro");

        // 执行测试
        boolean result = reasoningExtractorService.shouldEnableReasoning(UserApiKey.ProviderType.GOOGLE, request);

        // 验证结果
        assertTrue(result, "Gemini 2.5应该支持推理过程");
    }

    @Test
    void testExtractReasoning_WithNullResponse() {
        // 准备测试数据
        OpenAiChatRequest request = new OpenAiChatRequest();
        request.setIncludeReasoning(false);

        // 执行测试
        String result = reasoningExtractorService.extractReasoning(
                UserApiKey.ProviderType.OPENAI, null, request);

        // 验证结果
        assertNull(result, "未启用推理时应返回null");
    }
}
