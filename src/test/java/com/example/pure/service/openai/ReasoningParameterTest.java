package com.example.pure.service.openai;

import com.example.pure.config.AiConfig;
import com.example.pure.model.adapter.ChatCompletionRequest;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.impl.ModelAdapterServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 推理参数传递测试
 */
@ExtendWith(MockitoExtension.class)
class ReasoningParameterTest {

    private ModelAdapterServiceImpl modelAdapterService;
    private ObjectMapper objectMapper;

    @Mock
    private WebClient webClient;

    @Mock
    private AiConfig aiConfig;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        modelAdapterService = new ModelAdapterServiceImpl(webClient, objectMapper, aiConfig);
    }

    @Test
    void testBuildOpenAiRequestBody_WithReasoningForGoogle() throws Exception {
        // 准备测试数据
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gemini-2.5-pro");
        request.setIncludeReasoning(true);
        request.setMessages(List.of(new ChatCompletionRequest.ChatMessage("user", "测试消息")));
        request.setTemperature(0.7);

        // 使用反射调用私有方法
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                modelAdapterService,
                "buildOpenAiRequestBody",
                request,
                true,
                UserApiKey.ProviderType.GOOGLE
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("gemini-2.5-pro", result.get("model"));
        assertEquals(true, result.get("stream"));
        assertEquals(0.7, result.get("temperature"));

        // 验证 Google 特定的推理参数
        assertEquals("medium", result.get("reasoning_effort"));

        System.out.println("Google 请求体: " + objectMapper.writeValueAsString(result));
    }

    @Test
    void testBuildOpenAiRequestBody_WithReasoningForOpenAI() throws Exception {
        // 准备测试数据
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("o1-preview");
        request.setIncludeReasoning(true);
        request.setMessages(List.of(new ChatCompletionRequest.ChatMessage("user", "测试消息")));

        // 使用反射调用私有方法
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                modelAdapterService,
                "buildOpenAiRequestBody",
                request,
                false,
                UserApiKey.ProviderType.OPENAI
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("o1-preview", result.get("model"));
        assertEquals(false, result.get("stream"));

        // 验证 OpenAI 特定的推理参数
        assertEquals("medium", result.get("reasoning_effort"));

        System.out.println("OpenAI 请求体: " + objectMapper.writeValueAsString(result));
    }

    @Test
    void testBuildOpenAiRequestBody_WithReasoningForClaude() throws Exception {
        // 准备测试数据
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("claude-4");
        request.setIncludeReasoning(true);
        request.setMessages(List.of(new ChatCompletionRequest.ChatMessage("user", "测试消息")));

        // 使用反射调用私有方法
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                modelAdapterService,
                "buildOpenAiRequestBody",
                request,
                true,
                UserApiKey.ProviderType.ANTHROPIC
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("claude-4", result.get("model"));
        assertEquals(true, result.get("stream"));

        // 验证 Claude 特定的推理参数
        Map<String, Object> thinking = (Map<String, Object>) result.get("thinking");
        assertNotNull(thinking);
        assertEquals("enabled", thinking.get("type"));
        assertEquals(10000, thinking.get("budget_tokens"));

        System.out.println("Claude 请求体: " + objectMapper.writeValueAsString(result));
    }

    @Test
    void testBuildOpenAiRequestBody_WithoutReasoning() throws Exception {
        // 准备测试数据
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-4");
        request.setIncludeReasoning(false);
        request.setMessages(List.of(new ChatCompletionRequest.ChatMessage("user", "测试消息")));

        // 使用反射调用私有方法
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                modelAdapterService,
                "buildOpenAiRequestBody",
                request,
                false,
                UserApiKey.ProviderType.OPENAI
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("gpt-4", result.get("model"));
        assertEquals(false, result.get("stream"));

        // 验证没有推理参数
        assertNull(result.get("reasoning_effort"));
        assertNull(result.get("thinking"));
        assertNull(result.get("reasoning_effort"));

        System.out.println("无推理请求体: " + objectMapper.writeValueAsString(result));
    }
}
