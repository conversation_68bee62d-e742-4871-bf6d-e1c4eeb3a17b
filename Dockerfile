# 第一阶段：构建阶段
FROM maven:3.9.6-amazoncorretto-11 AS builder

# 设置的当前工作(运行命令行命令或复制命令)的上下文路径
WORKDIR /build

# 先只复制 pom.xml，只要pom.xml没有被修改就不会再次更新库
# 从当前主机的文件的配置文件同级目录复制到容器的(.)工作目录/build里
COPY pom.xml .

# 下载依赖（这一层会被缓存，除非 pom.xml 变化），将所有依赖缓存到本地仓库，使后续构建可以在离线状态下进行
RUN mvn dependency:go-offline -B

# 再复制源码
COPY src ./src

# 构建应用,清理上次编译打包过的文件并跳过测试
RUN mvn clean package -DskipTests -B

# 第二阶段：运行阶段
FROM amazoncorretto:11.0.27

# 🔥 安装健康检查所需的工具
RUN yum update -y && yum install -y curl && yum clean all

# 设置存放JAR文件的工作路径
WORKDIR /app

# 从构建阶段复制构建好的 JAR 文件
COPY --from=builder /build/target/*.jar app.jar

# 暴露端口
EXPOSE 8080

# 启动应用（设置JVM内存参数）
CMD ["java", "-Xms512m", "-Xmx1024m", "-jar", "app.jar"]

